package utils

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"google.golang.org/grpc/metadata"
)

func GrpcSetContext(ctx context.Context) context.Context {
	gCtx := xgin.FromContext(ctx)
	md := metadata.New(map[string]string{
		xnet.RequestId: xnet.GetRequestId(gCtx.Request),
	})
	return metadata.NewOutgoingContext(ctx, md)
}

package shumei

import (
	"bytes"
	"compress/flate"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"io/ioutil"
	"strconv"
)

var androidPublicKey = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
var iosPublicKey = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
var webPublicKey = ``
var quickPublicKey = ``

var android_map_ = map[string]string{
	"a1":   "rtype",
	"a2":   "smid",
	"a3":   "privacy",
	"a4":   "smseq",
	"a5":   "apputm",
	"a6":   "os",
	"a7":   "sdkver",
	"a8":   "sdk_flavor",
	"a9":   "t",
	"a10":  "osver",
	"a11":  "appId",
	"a12":  "pid",
	"a13":  "model",
	"a14":  "abtmac",
	"a15":  "axposed",
	"a16":  "ainfo",
	"a17":  "net",
	"a18":  "props",
	"a19":  "bssid",
	"a20":  "imei",
	"a21":  "tn",
	"a22":  "imei1",
	"a23":  "imei2",
	"a24":  "adid",
	"a25":  "imsi",
	"a26":  "mac",
	"a27":  "apps",
	"a28":  "whiteapp",
	"a29":  "band",
	"a30":  "ssid",
	"a31":  "wifiip",
	"a32":  "cpuCount",
	"a33":  "cpuModel",
	"a34":  "cpuFreq",
	"a35":  "cpuVendor",
	"a36":  "screen",
	"a37":  "brightness",
	"a38":  "appver",
	"a39":  "appname",
	"a40":  "boot",
	"a41":  "name",
	"a42":  "proc",
	"a43":  "brand",
	"a44":  "network",
	"a45":  "operator",
	"a46":  "sys",
	"a47":  "sensor",
	"a48":  "mem",
	"a49":  "iccid",
	"a50":  "cell",
	"a51":  "aps",
	"a52":  "riskapp",
	"a53":  "riskdir",
	"a54":  "emu",
	"a55":  "ccmd5",
	"a56":  "signdn",
	"a57":  "signhash",
	"a58":  "virtual",
	"a59":  "virtualcnt",
	"a60":  "virtualuid",
	"a61":  "virtualproc",
	"a62":  "files",
	"a63":  "input",
	"a64":  "acc",
	"a65":  "cost",
	"a66":  "net_error",
	"a67":  "ip_cache",
	"a68":  "fc_t",
	"a69":  "availableSpace",
	"a70":  "freeSpace",
	"a71":  "totalSpace",
	"a72":  "battery",
	"a73":  "mockLoc",
	"a74":  "debugger",
	"a75":  "debuggable",
	"a76":  "proxyV2",
	"a77":  "xp_callback",
	"a78":  "xp_cache",
	"a79":  "err",
	"a80":  "sid",
	"a81":  "aenc",
	"a82":  "ainfoMd5",
	"a83":  "permission",
	"a84":  "drmId",
	"a85":  "notCollect",
	"a86":  "option",
	"a87":  "elapsedReadltime",
	"a88":  "binderhook",
	"a89":  "virtualDevice",
	"a90":  "targetSdk",
	"a91":  "settings",
	"a92":  "display",
	"a93":  "screenOn",
	"a94":  "availMem",
	"a95":  "randomMac",
	"a96":  "organization",
	"a97":  "box",
	"a98":  "gBox",
	"a99":  "xpPkgs",
	"a100": "xpModules",
	"a101": "xpLoaders",
	"a102": "xpApp",
	"a103": "oaid",
	"a105": "zposed",
	"a107": "hookJava",
	"a108": "spSmId294",
	"a109": "sdSmId294",
	"a110": "spSmId288",
	"a111": "sdSmId288",
	"a112": "apkSignInfo",
	"a113": "taiJiBasePackage",
	"a114": "exceptionInfo",
	"a115": "sysEnv",
	"a116": "locationCls",
	"a117": "fakeLocExcept",
	"a118": "trackNode",
	"a119": "sensorsData",
	"a120": "trafficBytes",
	"a121": "adbEnabled",
	"a122": "adid2",
	"a123": "audio",
	"a124": "extraInfo",
	"a125": "appTime",
	"a126": "outerid",
	"a127": "bootCount",
	"a129": "runningProcessCount",
	"a130": "appUsedCount",
	"a131": "simCountryISO",
	"a132": "networkCountryIso",
	"a133": "collectCost",
	"a134": "ghPluginFiles",
	"a135": "hasMagisk",
	"a136": "sdkBuild",
	"a138": "gmtFiles",
	"a139": "dataDirReadable",
	"a140": "parentDirReadable",
	"b1":   "gothk",
	"b2":   "resett",
	"b3":   "ds_md5",
	"b4":   "ds_md52",
	"b5":   "vf_md5",
	"b6":   "sb_md5",
	"b7":   "vl_md5",
	"b8":   "sf_md5",
	"b9":   "font_md5",
	"b10":  "font_md52",
	"b11":  "ap_mac",
	"b12":  "wifi_mac",
	"b13":  "sys_props",
	"b15":  "root",
	"b16":  "tmpr_fw",
	"b17":  "abi",
	"b18":  "riskfile",
	"b19":  "hook",
	"b20":  "is_art",
	"b21":  "hook_java",
	"b22":  "bootId",
	"b23":  "randomKey",
	"b24":  "is_vpn",
	"b25":  "arpInfo",
	"b26":  "dirs",
	"b27":  "mem_md5",
	"b28":  "memreal_md5",
	"b29":  "vig",
	"b30":  "cid",
	"b31":  "tjCls",
	"b32":  "serviceMock",
	"b33":  "baseApkSize",
	"b34":  "dbg",
	"b35":  "x8speeder",
	"b36":  "clock_gettime",
	"b37":  "gettimeofday",
	"b38":  "emuShareContent",
	"b39":  "ghCls",
	"b40":  "ghSocket",
	"b41":  "hyxgSocket",
	"b42":  "appProcesses",
	"b43":  "arp2",
	"b44":  "stfSocket",
	"b45":  "probeOpenCamera",
	"b46":  "probePreviewCB",
	"b47":  "probeSysProps",
}

var ios_map_ = map[string]string{
	"a1":   "smid",
	"a2":   "smseq",
	"a3":   "rtype",
	"a4":   "os",
	"a5":   "sdkver",
	"a6":   "t",
	"a7":   "osver",
	"a8":   "model",
	"a9":   "idfv",
	"a10":  "idfa",
	"a11":  "track",
	"a12":  "boot",
	"a13":  "root",
	"a14":  "is_vpn",
	"a15":  "appname",
	"a16":  "appver",
	"a17":  "appversion",
	"a18":  "acCode",
	"a19":  "stCode",
	"a20":  "rmCode",
	"a21":  "name",
	"a22":  "totalSpace",
	"a23":  "freeSpace",
	"a24":  "memory",
	"a25":  "brightness",
	"a26":  "battery",
	"a27":  "width",
	"a28":  "height",
	"a29":  "scaledDensity",
	"a30":  "networkType",
	"a31":  "ssid",
	"a32":  "bssid",
	"a33":  "dns",
	"a34":  "countryIso",
	"a35":  "mcc",
	"a36":  "mnc",
	"a37":  "languages",
	"a38":  "gps",
	"a39":  "orientation",
	"a40":  "accessory",
	"a41":  "md5",
	"a42":  "tn",
	"a43":  "riskapp",
	"a44":  "riskdir",
	"a45":  "s_c",
	"a46":  "apputm",
	"a47":  "appId",
	"a48":  "sid",
	"a49":  "batteryState",
	"a50":  "userInterfaceIdiom",
	"a51":  "rTotalSpace",
	"a52":  "rFreeSpace",
	"a53":  "ubiquityIdentityToken",
	"a54":  "environment",
	"a55":  "osverStr",
	"a56":  "nscreen",
	"a57":  "organization",
	"a58":  "box",
	"a59":  "isAppsExist",
	"a60":  "asIpa",
	"a61":  "bootUUID",
	"a62":  "bootTime",
	"a63":  "physicalCpu",
	"a64":  "cpuType",
	"a65":  "Debugging",
	"a66":  "Dependencies",
	"a67":  "Hostname",
	"a68":  "kernVer",
	"a69":  "kernOsVer",
	"a70":  "cryptids",
	"a71":  "sl",
	"a72":  "updateTimes",
	"a73":  "installTime",
	"a74":  "rootExt",
	"a75":  "addrCode",
	"a76":  "machine",
	"a77":  "touch",
	"a78":  "sensorData",
	"a79":  "hwmodel",
	"a80":  "cpucache",
	"a81":  "volume",
	"a82":  "sims",
	"a83":  "extraInfo",
	"a84":  "carrierName",
	"a85":  "sysv",
	"a86":  "proxy",
	"a87":  "collectCost",
	"a88":  "sbnCode",
	"a89":  "todCode",
	"a90":  "cgCode",
	"a91":  "mcCode",
	"a92":  "teamId",
	"a93":  "sdkBuild",
	"a95":  "mountInfo",
	"a96":  "network",
	"a97":  "signInfo",
	"a98":  "cydiaSource",
	"a99":  "installt",
	"a100": "csCodes",
	"a101": "root2",
	"a103": "netInfos",
	"a104": "oi",
	"a105": "oc_risk",
	"a106": "c_risk",
	"a107": "audio_outs",
	"a108": "sims2",
	"a109": "keyboard",
	"a110": "capture",
	"a111": "locale",
	"a112": "bootTime2",
	"a113": "space",
	"a114": "asIpa2",
	"a115": "caches",
	"a116": "airplane",
	"a117": "account",
	"a118": "timeZone",
	"a119": "availMem",
	"a120": "gpsLocation",
	"a121": "ips",
	"c1":   "CFNetworkCopySystemProxySettings",
	"c2":   "CNCopyCurrentNetworkInfo",
	"c3":   "SCNetworkReachabilityGetFlags",
	"c4":   "dyld_get_image_header",
	"c5":   "dyld_get_image_name",
	"c6":   "dyld_get_image_vmaddr_slide",
	"c7":   "dyld_image_count",
	"c8":   "dlsym",
	"c9":   "fopen",
	"c10":  "getenv",
	"c11":  "getifaddrs",
	"c12":  "sysctl",
	"c13":  "uname",
	"s1":   "mobileCountryCode",
	"s2":   "mobileNetworkCode",
	"s3":   "isoCountryCode",
	"s4":   "reachabilityForInternetConnection",
	"s5":   "valueWithError",
	"s6":   "isReachableViaWiFi",
	"s7":   "localizedModel",
	"s8":   "systemVersion",
	"s9":   "platform",
	"s10":  "carrierName",
	"s11":  "hwmodel",
	"s12":  "currentRadioAccessTechnology",
	"s13":  "name",
	"s14":  "model",
	"s15":  "value",
	"s16":  "isReachableViaWWANP",
	"s17":  "identifierForVendor",
	"s18":  "loc",
	"s19":  "locDele",
	"s20":  "ocel",
	"s21":  "ocelft",
	"s22":  "oces",
	"k1":   "fname",
	"k2":   "fbase",
	"k3":   "opcode",
	"k4":   "saddr",
	"k5":   "sname",
	"n1":   "wi",
	"n2":   "wo",
	"n3":   "ci",
	"n4":   "co",
	"i1":   "cpuSerial",
	"i2":   "rmn",
	"i3":   "ri",
}

func unPadding(src []byte) []byte {
	for i := len(src) - 1; ; i-- {
		if src[i] != 0 {
			return src[:i+1]
		}
	}
	return nil
}

func newDecrypt(src, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := 16
	decryptData := make([]byte, len(src))
	tmpData := make([]byte, blockSize)

	for index := 0; index < len(src); index += blockSize {
		block.Decrypt(tmpData, src[index:index+blockSize])
		copy(decryptData, tmpData)
	}
	return unPadding(decryptData), nil
}

func rsaDecrypt(privateKey []byte, ciphertext []byte) ([]byte, error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, errors.New("private key error!")
	}

	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return rsa.DecryptPKCS1v15(rand.Reader, priv.(*rsa.PrivateKey), ciphertext)
}

func rsaDecryptBoxId(ciphertext []byte, privateKey []byte) ([]byte, error) {
	priv, err := x509.ParsePKCS1PrivateKey(privateKey)
	if err != nil {
		return nil, errors.New("x509 parse ERROR")
	}
	return rsa.DecryptPKCS1v15(rand.Reader, priv, ciphertext)
}

//func base64Decode(src string) ([]byte, error) {
//	by, err := base64.StdEncoding.DecodeString(src)
//	if err != nil {
//		return by, err
//	}
//	return by, nil
//}

//func getMd5String(s string) string {
//	h := md5.New()
//	h.Write([]byte(s))
//	return hex.EncodeToString(h.Sum(nil))
//}

func aesEncrypt(key []byte, strMesg string) ([]byte, error) {
	var iv = []byte(key)[:aes.BlockSize]
	encrypted := make([]byte, len(strMesg))
	aesBlockEncrypter, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	aesEncrypter := cipher.NewCFBEncrypter(aesBlockEncrypter, iv)
	aesEncrypter.XORKeyStream(encrypted, []byte(strMesg))
	return encrypted, nil
}

func aesDecryptCBC(encrypted []byte, key []byte) (decrypted []byte) {
	block, _ := aes.NewCipher(key)
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	decrypted = make([]byte, len(encrypted))
	blockMode.CryptBlocks(decrypted, encrypted)
	decrypted = pkcs5UnPadding(decrypted)
	return decrypted
}

func aesDecryptCBCNew(encrypted []byte, key []byte, iv []byte) (decrypted []byte) {
	block, _ := aes.NewCipher(key)
	blockMode := cipher.NewCBCDecrypter(block, iv)
	decrypted = make([]byte, len(encrypted))
	blockMode.CryptBlocks(decrypted, encrypted)
	decrypted = pkcs5UnPadding(decrypted)
	return decrypted
}

func pkcs5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func aesECBDecrypt(encrypted []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}
	blockSize := 16
	decryptData := make([]byte, len(encrypted))
	tmpData := make([]byte, blockSize)
	for index := 0; index < len(encrypted); index += blockSize {
		block.Decrypt(tmpData, encrypted[index:index+blockSize])
		copy(decryptData[index:], tmpData)
	}
	return pkcs5UnPadding(decryptData), nil
}

func decryptAinfo(encryptedAinfo, key string) (string, error) {
	md5Key := getMd5String(key)
	ainfo, err := base64Decode(encryptedAinfo)
	if err != nil {
		return "", err
	}
	iv := "0102030405060708"
	decryptedAinfo := aesDecryptCBCNew(ainfo, []byte(md5Key), []byte(iv))
	return string(decryptedAinfo), nil
}

func DecryptSha256(cipherText, key string) (string, error) {
	s, _ := base64.StdEncoding.DecodeString(cipherText)

	pkPem, _ := pem.Decode([]byte(key))

	pk, err := x509.ParsePKCS8PrivateKey(pkPem.Bytes)

	rng := rand.Reader
	t, err := rsa.DecryptOAEP(sha256.New(), rng, pk.(*rsa.PrivateKey), s, nil)
	if err != nil {
		return "", err
	}
	return string(t), nil
}

func config_get_key(key, otype string) string {
	key_map_ := make(map[string]string)
	if otype == "ios" {
		key_map_ = ios_map_
	} else if otype == "android" {
		key_map_ = android_map_
	}
	if it, ok := key_map_[key]; ok {
		return it
	}
	return ""
}

func KeyReplace(orginal map[string]interface{}, otype string) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range orginal {
		if val, ok := v.(map[string]interface{}); ok {
			new_key := config_get_key(k, otype)
			tmplate := KeyReplace(val, otype)
			if new_key != "" {
				result[new_key] = tmplate
			} else {
				result[k] = tmplate
			}
		} else {
			new_key := config_get_key(k, otype)
			if new_key != "" {
				result[new_key] = v
			} else {
				result[k] = v
			}
		}
		if val, ok := v.(string); ok {
			temp := make(map[string]interface{})
			err := json.Unmarshal([]byte(val), &temp)
			if err == nil {
				new_key := config_get_key(k, otype)
				tmplate := KeyReplace(temp, otype)
				if new_key != "" {
					result[new_key] = tmplate
				} else {
					result[k] = tmplate
				}
			}
		}
	}
	return result
}

func Decrypt(source, os, ainfoKey string) (string, error) {
	var rsaPriKey string
	switch os {
	case "android":
		rsaPriKey = androidPublicKey
	case "ios":
		rsaPriKey = iosPublicKey
		ainfoKey = "nil"
	case "web":
		rsaPriKey = webPublicKey
	case "quick":
		rsaPriKey = quickPublicKey
	}

	origin := make(map[string]interface{})
	err := json.Unmarshal([]byte(source), &origin)
	if err == nil {
		_, is_v4 := origin["os"].(string)
		if is_v4 {
			return DecryptV4(source, rsaPriKey)
		} else {
			return DecryptV3(source, rsaPriKey, ainfoKey)
		}
	}
	return "", err
}

func DecryptV3(source, rsaPriKey, ainfoKey string) (string, error) {
	origin := make(map[string]interface{})
	if err := json.Unmarshal([]byte(source), &origin); err == nil {
		if data, ok := origin["data"]; ok {
			encode := 0
			fingerprint := ""
			dataMap, ok1 := data.(map[string]interface{})
			if ok1 {
				encode = int(dataMap["fpEncode"].(float64))
				fingerprint = dataMap["fingerprint"].(string)
			}
			if encode == 7 { //decrypt android data
				pri := dataMap["pri"].(string)
				base64decodePri, err := base64Decode(pri)
				if err != nil {
					return "", err
				}
				des_key, err1 := rsaDecrypt([]byte(rsaPriKey), base64decodePri)
				if err1 != nil {
					return "", err1
				}
				des_key_str := getMd5String(string(des_key))
				b64decodeFP, err2 := base64Decode(fingerprint)
				if err2 != nil {
					return "", err2
				}
				iv := "0102030405060708"
				decryptedFP := aesDecryptCBCNew([]byte(b64decodeFP), []byte(des_key_str), []byte(iv))

				fpMap := make(map[string]interface{})
				err = json.Unmarshal(decryptedFP, &fpMap)
				if err != nil {
					return "", err
				}
				encryptedAinfo, ok1 := fpMap["ainfo"].(string)
				if ok1 {
					decryptedAinfo, err := decryptAinfo(encryptedAinfo, ainfoKey)
					if err != nil {
						return "", err
					}
					ainfoMap := make(map[string]interface{})
					err = json.Unmarshal([]byte(decryptedAinfo), &ainfoMap)
					if err != nil {
						return "", err
					}
					fpMap["ainfo"] = ainfoMap
					decryptedFPFinal, _ := json.Marshal(fpMap)
					return string(decryptedFPFinal), nil
				}
			} else if encode == 4 { //decrypt ios data
				org, ok := origin["organization"].(string)
				if !ok {
					return "", errors.New("miss organization")
				}
				keyPrefix := "smsdk"
				key := keyPrefix + org
				key = getMd5String(key)
				b64decodeFP, err1 := base64Decode(fingerprint)
				if err1 != nil {
					return "", err1
				}
				decryptedFP, err2 := aesECBDecrypt([]byte(b64decodeFP), []byte(key))
				if err2 != nil {
					return "", err2
				}
				return string(decryptedFP), nil
			} else if encode == 11 {
				pri := dataMap["pri"].(string)

				des_key_string, err := DecryptSha256(pri, rsaPriKey)
				if err != nil {
					return "", err
				}

				des_key_str := getMd5String(des_key_string)
				b64decodeFP, err := base64Decode(fingerprint)
				if err != nil {
					return "", err
				}
				iv := "0102030405060708"
				decryptedFP := aesDecryptCBCNew([]byte(b64decodeFP), []byte(des_key_str), []byte(iv))
				fpMap := make(map[string]interface{})
				err = json.Unmarshal(decryptedFP, &fpMap)
				if err != nil {
					return "", err
				}
				fpMap = KeyReplace(fpMap, "android")
				encryptedAinfo, ok1 := fpMap["ainfo"].(string)
				if ok1 {
					decryptedAinfo, err := decryptAinfo(encryptedAinfo, ainfoKey)
					if err != nil {
						return "", err
					}
					ainfoMap := make(map[string]interface{})
					err = json.Unmarshal([]byte(decryptedAinfo), &ainfoMap)
					if err != nil {
						return "", err
					}
					ainfoMap = KeyReplace(ainfoMap, "android")
					fpMap["ainfo"] = ainfoMap
					decryptedFPFinal, _ := json.Marshal(fpMap)
					return string(decryptedFPFinal), nil
				}
			} else if encode == 9 {
				pri := dataMap["pri"].(string)
				base64decodePri, err := base64Decode(pri)
				if err != nil {
					return "", err
				}
				des_key, err1 := rsaDecrypt([]byte(rsaPriKey), base64decodePri)
				if err1 != nil {
					return "", err1
				}
				des_key_str := getMd5String(string(des_key))
				b64decodeFP, err2 := base64Decode(fingerprint)
				if err2 != nil {
					return "", err2
				}
				iv := "0102030405060708"
				decryptedFP := aesDecryptCBCNew([]byte(b64decodeFP), []byte(des_key_str), []byte(iv))

				fpMap := make(map[string]interface{})
				err = json.Unmarshal(decryptedFP, &fpMap)
				if err != nil {
					return "", err
				}
				fpMap = KeyReplace(fpMap, "android")
				encryptedAinfo, ok1 := fpMap["ainfo"].(string)
				if ok1 {
					decryptedAinfo, err := decryptAinfo(encryptedAinfo, ainfoKey)
					if err != nil {
						return "", err
					}
					ainfoMap := make(map[string]interface{})
					err = json.Unmarshal([]byte(decryptedAinfo), &ainfoMap)
					if err != nil {
						return "", err
					}
					ainfoMap = KeyReplace(ainfoMap, "android")
					fpMap["ainfo"] = ainfoMap
					decryptedFPFinal, _ := json.Marshal(fpMap)
					return string(decryptedFPFinal), nil
				}
			} else if encode == 10 {
				pri := dataMap["pri"].(string)
				base64decodePri, err := base64Decode(pri)
				if err != nil {
					return "", err
				}
				des_key, err1 := rsaDecrypt([]byte(rsaPriKey), base64decodePri)
				if err1 != nil {
					return "", err1
				}
				des_key_str := getMd5String(string(des_key))
				b64decodeFP, err2 := base64Decode(fingerprint)
				if err2 != nil {
					return "", err2
				}
				decryptedFP, _ := aesECBDecrypt([]byte(b64decodeFP), []byte(des_key_str))
				fpMap := make(map[string]interface{})
				err = json.Unmarshal(decryptedFP, &fpMap)
				if err != nil {
					return "", err
				}
				fpMap = KeyReplace(fpMap, "ios")
				if val, ok := fpMap["environment"]; ok {
					var hexdecode string
					str := val.(string)
					for i := 0; i < len(str); i += 2 {
						s, _ := strconv.ParseInt(str[i:i+2], 16, 32)
						hexdecode += string(rune(^s + 256))
					}
					fpMap["environment"] = hexdecode
				}
				if scMap, ok := fpMap["s_c"].(map[string]interface{}); ok {
					for _, it := range scMap {
						if tempMap, ok := it.(map[string]interface{}); ok {
							for key, value := range tempMap {
								var hexdecode string
								str := value.(string)
								for i := 0; i < len(str); i += 2 {
									s, _ := strconv.ParseInt(str[i:i+2], 16, 32)
									hexdecode += string(rune(^s + 256))
								}
								if key != "error" {
									tempMap[key] = hexdecode
								}
							}
						}
					}
				}

				decryptedFPFinal, _ := json.Marshal(fpMap)
				return string(decryptedFPFinal), nil
			}
		}
	}
	return "", errors.New("data format error")
}

func DecryptV4(source, rsaPriKey string) (string, error) {
	origin := make(map[string]interface{})
	if err := json.Unmarshal([]byte(source), &origin); err == nil {
		encode, ok := origin["encode"].(float64)
		if !ok {
			return "", errors.New("can not find encode")
		}
		fingerprint, ok := origin["data"].(string)
		if !ok {
			return "", errors.New("can not find fingerprint")
		}
		ep, ok := origin["ep"].(string)
		if !ok {
			return "", errors.New("can not find ep")
		}

		if encode == 2 {
			des_key_string, err := DecryptSha256(ep, rsaPriKey)
			if err != nil {
				return "", err
			}
			des_key_str := getMd5String(des_key_string)
			b64decodeFP, err := base64Decode(fingerprint)
			if err != nil {
				return "", err
			}
			iv := "0102030405060708"
			decryptedFP := aesDecryptCBCNew([]byte(b64decodeFP), []byte(des_key_str), []byte(iv))
			r := bytes.NewReader(decryptedFP)
			flateReader := flate.NewReader(r)
			defer flateReader.Close()
			body, err := ioutil.ReadAll(flateReader)
			if err != nil {
				return "", err
			}
			fpMap := make(map[string]interface{})
			err = json.Unmarshal(body, &fpMap)
			if err != nil {
				return "", err
			}
			fpMap = KeyReplace(fpMap, "android")
			decryptedFPFinal, _ := json.Marshal(fpMap)
			return string(decryptedFPFinal), nil
		} else if encode == 3 {
			base64decodePri, err := base64Decode(ep)
			if err != nil {
				return "", err
			}
			des_key, err := rsaDecrypt([]byte(rsaPriKey), []byte(base64decodePri))
			if err != nil {
				return "", err
			}
			des_key_str := getMd5String(string(des_key))
			b64decodeFP, err := base64Decode(fingerprint)
			if err != nil {
				return "", err
			}
			decryptedFP, _ := aesECBDecrypt([]byte(b64decodeFP), []byte(des_key_str))
			r := bytes.NewReader(decryptedFP)
			flateReader := flate.NewReader(r)
			defer flateReader.Close()
			body, err := ioutil.ReadAll(flateReader)
			if err != nil {
				return "", err
			}
			fpMap := make(map[string]interface{})
			err = json.Unmarshal(body, &fpMap)
			if err != nil {
				return "", err
			}
			fpMap = KeyReplace(fpMap, "ios")

			if val, ok := fpMap["environment"]; ok {
				var hexdecode string
				str := val.(string)
				for i := 0; i < len(str); i += 2 {
					s, _ := strconv.ParseInt(str[i:i+2], 16, 32)
					hexdecode += string(rune(^s + 256))
				}
				fpMap["environment"] = hexdecode
			}

			if scMap, ok := fpMap["s_c"].(map[string]interface{}); ok {
				for _, it := range scMap {
					if tempMap, ok := it.(map[string]interface{}); ok {
						for key, value := range tempMap {
							var hexdecode string
							str := value.(string)
							for i := 0; i < len(str); i += 2 {
								s, _ := strconv.ParseInt(str[i:i+2], 16, 32)
								hexdecode += string(rune(^s + 256))
							}
							if key != "error" {
								tempMap[key] = hexdecode
							}
						}
					}
				}
			}
			decryptedFPFinal, _ := json.Marshal(fpMap)
			return string(decryptedFPFinal), nil
		} else if encode == 4 {
			des_key, err := rsaDecrypt([]byte(rsaPriKey), []byte(ep))
			if err != nil {
				return "", err
			}
			des_key_str := getMd5String(string(des_key))
			b64decodeFP, err := base64Decode(fingerprint)
			if err != nil {
				return "", err
			}
			decryptedFP, _ := aesECBDecrypt([]byte(b64decodeFP), []byte(des_key_str))
			r := bytes.NewReader(decryptedFP)
			flateReader := flate.NewReader(r)
			defer flateReader.Close()
			body, err := ioutil.ReadAll(flateReader)
			if err != nil {
				return "", err
			}
			fpMap := make(map[string]interface{})
			err = json.Unmarshal(body, &fpMap)
			if err != nil {
				return "", err
			}
			decryptedFPFinal, _ := json.Marshal(fpMap)
			return string(decryptedFPFinal), nil
		}
	}
	return "", errors.New("data format error")
}

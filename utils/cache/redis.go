package cache

import (
	"context"
	"risk/startup"
)

type Redis struct {
	Ctx context.Context
}

func (r Redis) Set(key, data string) error {
	return startup.GetRedis().Set(r.Ctx, key, data, 0).Err()
}

func (r Redis) Get(key string) (string, error) {
	return startup.GetRedis().Get(r.Ctx, key).Result()
}

func (r Redis) Remove(key string) error {
	return startup.GetRedis().Del(r.Ctx, key).Err()
}

func (r Redis) Exist(key string) bool {
	cmd := startup.GetRedis().Exists(r.Ctx, key)
	if cmd.Err() != nil {
		return false
	}
	return cmd.Val() == 1
}

func (r Redis) HSet(key, field, data string) error {
	return startup.GetRedis().HSet(r.Ctx, key, field, data).Err()
}

func (r Redis) HGet(key, field string) (string, error) {
	return startup.GetRedis().HGet(r.Ctx, key, field).Result()
}

func (r Redis) HGetAll(key string) (map[string]string, error) {
	return startup.GetRedis().HGetAll(r.Ctx, key).Result()
}

func (r Redis) HDel(key, field string) error {
	return startup.GetRedis().HDel(r.Ctx, key, field).Err()
}

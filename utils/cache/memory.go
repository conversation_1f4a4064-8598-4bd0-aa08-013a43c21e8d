package cache

import (
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/cache/memory"
	"risk/config"
	"sync"
)

type Memory struct{}

var syncOnce sync.Once
var memoryCache *memory.LRUCache

func (m Memory) Set(key, data string) error {
	if cache, err := m.getCache(); err != nil {
		return err
	} else {
		cache.Add(key, data)
		return nil
	}
}

func (m Memory) Get(key string) (string, error) {
	if cache, err := m.getCache(); err != nil {
		return "", err
	} else {
		if data, ok := cache.Get(key); ok {
			return data.(string), nil
		} else {
			return "", nil
		}
	}
}

func (m Memory) Remove(key string) error {
	if cache, err := m.getCache(); err != nil {
		return err
	} else {
		cache.Remove(key)
		return nil
	}
}

func (m Memory) Exist(key string) bool {
	if cache, err := m.getCache(); err != nil {
		return false
	} else {
		_, ok := cache.Get(key)
		return ok
	}
}

func (m Memory) HSet(key, field, data string) error {
	str, err := m.Get(key)
	val := make(map[string]string)
	if err != nil {
		return err
	} else if str != "" {
		sonic.Unmarshal([]byte(str), &val)
	}
	val[field] = data
	MarStr, _ := sonic.MarshalString(val)
	return m.Set(key, MarStr)
}

func (m Memory) HGet(key, field string) (string, error) {
	str, err := m.Get(key)
	var data map[string]string
	if err != nil {
		return "", err
	} else if str == "" {
		return "", nil
	}

	sonic.Unmarshal([]byte(str), &data)
	if val, ok := data[field]; ok {
		return val, nil
	}
	return "", nil
}

func (m Memory) HGetAll(key string) (map[string]string, error) {
	str, err := m.Get(key)
	var data map[string]string
	if err != nil {
		return nil, err
	} else if str == "" {
		return nil, nil
	}
	sonic.Unmarshal([]byte(str), &data)
	return data, nil
}

func (m Memory) HDel(key, field string) error {
	str, err := m.Get(key)
	var data map[string]string
	if err != nil {
		return err
	} else if str == "" {
		return nil
	}
	sonic.Unmarshal([]byte(str), &data)
	if _, ok := data[field]; ok {
		delete(data, field)
		MarStr, _ := sonic.MarshalString(data)
		return m.Set(key, MarStr)
	}
	return nil
}

func (Memory) getCache() (*memory.LRUCache, error) {
	syncOnce.Do(func() {
		memoryCache = memory.NewLRUCache(config.Get().Cache.Cap)
	})
	if memoryCache == nil {
		return nil, fmt.Errorf("memorycache is nil")
	}
	return memoryCache, nil
}

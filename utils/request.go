package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"risk/startup"
	"strconv"
	"time"

	"gitlab.papegames.com/fringe/pkg/http"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type PigeonResp struct {
	Ret  int             `json:"ret"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

func PigeonRequest(ctx context.Context, u string, vals url.Values) ([]byte, error) {
	req, err := http.NewRequest(ctx)
	if err != nil {
		return nil, err
	}
	req.SetHeader("Content-Type", "application/x-www-form-urlencoded")
	req.SetHeader("Accept", "application/json")

	ts := time.Now().Unix()

	gctx := xgin.FromContext(ctx)

	clientid := GetClientid(gctx)
	lang := GetLang(gctx)

	signer := shared.NewSigner(startup.GetClientConfig())
	sign, err := signer.Generate(clientid, ts)
	if err != nil {
		return nil, err
	}

	vals.Set("clientid", clientid)
	vals.Set("timestamp", strconv.FormatInt(ts, 10))
	vals.Set("sig", sign)
	if lang != "" {
		vals.Set("lang", lang)
	}

	req.SetFormDataFromValues(vals)

	res, err := req.Post(u)
	if err != nil {
		return nil, err
	}

	xlog.FromContext(ctx).Info("pigeon request url", xlog.String("url", u), xlog.Any("vals", vals), xlog.Int("status", res.StatusCode()), xlog.ByteString("body", res.Body()))

	if !res.IsSuccess() {
		return nil, fmt.Errorf("pigeon request %s failed, status code: %d", u, res.StatusCode())
	}

	return res.Body(), nil
}

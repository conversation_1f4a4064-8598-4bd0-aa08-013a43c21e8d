package utils

import (
	"context"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/segmentio/kafka-go"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring/uuid"
	"hash/fnv"
)

func GenerateNonce(seed ...[]byte) string {
	return guid.S(seed...)
}

func GenerateCode() string {
	return grand.Digits(6)
}

// CheckIfInGrayScale checks if a user falls within the grayscale percentage
func CheckIfInGrayScale(userID string, grayScalePercentage uint32) bool {
	hashValue := hash(userID)
	return hashValue%100 < grayScalePercentage
}

// hash calculates a hash value for a given string
func hash(s string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(s))
	return h.Sum32()
}

func KafkaCtx(ctx context.Context, msg *kafka.Message) context.Context {
	var (
		requestId = uuid.New().String()
	)
	for _, v := range msg.Headers {
		if string(v.Key) == xnet.RequestId && string(v.Value) != "" {
			requestId = string(v.Value)
			break
		}
	}
	ctx = xlog.NewContext(ctx, xlog.L().With(xlog.RequestID(requestId)))
	return ctx
}

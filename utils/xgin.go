package utils

import (
	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/pkg/shared"
)

var (
	xginClientId = "_@xginPassportClientid"
	xginLang     = "_@xginPassportLang"
	xginSigner   = "_@xginPassportSigner"
)

func WithClientid(c *gin.Context, clientid string) {
	c.Set(xginClientId, clientid)
}

func GetClientid(c *gin.Context) string {
	if vb, ok := c.Get(xginClientId); ok {
		if b, ok := vb.(string); ok {
			return b
		}
	}
	return ""
}

func WithLang(c *gin.Context, lang string) {
	c.Set(xginLang, lang)
}

func GetLang(c *gin.Context) string {
	if vb, ok := c.Get(xginLang); ok {
		if b, ok := vb.(string); ok {
			return b
		}
	}
	return ""
}

func WithSigner(c *gin.Context, signer *shared.Signer) {
	c.Set(xginSigner, signer)
}

func GetSigner(c *gin.Context) *shared.Signer {
	if vb, ok := c.Get(xginSigner); ok {
		if b, ok := vb.(*shared.Signer); ok {
			return b
		}
	}
	return nil
}

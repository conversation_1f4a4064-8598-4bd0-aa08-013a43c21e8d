package utils

import "unicode"

// 校验身份证合法性（含18位校验位）
func IsValidIDCard(id string) bool {
	if len(id) != 18 {
		return false
	}
	weight := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	checkCode := []byte{'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'}
	sum := 0
	for i := 0; i < 17; i++ {
		if id[i] < '0' || id[i] > '9' {
			return false
		}
		sum += int(id[i]-'0') * weight[i]
	}
	return id[17] == checkCode[sum%11] || (id[17] == 'x' && checkCode[sum%11] == 'X')
}

// 校验姓名合法性（只允许中文）
func IsValidName(name string) bool {
	if name == "" {
		return false
	}
	for _, r := range name {
		if !unicode.Is(unicode.Han, r) {
			return false
		}
	}
	return len(name) >= 2 && len(name) <= 6
}

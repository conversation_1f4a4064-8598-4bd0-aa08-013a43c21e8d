package utils

import (
	"errors"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"strings"
)

type SdkDeviceId struct {
	Os        string `json:"os"`
	DeviceId  string `json:"device_id"`
	Oaid      string `json:"oaid"`
	AndroidId string `json:"android_id"`
	Idfv      string `json:"idfv"`
	Idfa      string `json:"idfa"`
	Afid      string `json:"afid"`
	Gaid      string `json:"gaid"`
}

// android:   android id > oaid > gaid > afid > firebaseid > paper_device_id
// ios: idfv > idfa > paper_device_id
// pc:  主板UUID > MachineGUID > MAC地址 > 硬盘序列号 > CPU ID > 产品ID
func (s *SdkDeviceId) GetDeviceId(information ...string) (string, error) {
	if s.Os == "" {
		if err := s.getInformation(information[0]); err != nil {
			return "", err
		}
	}
	switch strings.ToLower(s.Os) {
	case "android":
		return s.Android()
	case "ios":
		return s.IOS()
	default:
		return s.Other()
	}
}

// 以前持久缓存的android id > oaid > gaid > afid > firebaseid > paper_device_id，没有就挨个往后取
func (s *SdkDeviceId) Android() (string, error) {
	if s.AndroidId != "" {
		return s.AndroidId, nil
	} else if s.Oaid != "" {
		return s.Oaid, nil
	} else if s.Gaid != "" {
		return s.Oaid, nil
	} else if s.Afid != "" {
		return s.Oaid, nil
	} else if s.DeviceId != "" {
		return s.DeviceId, nil
	}
	return "", errors.New("no device id")
}

// 有 IDFA 拿 IDFA 没有拿 IDFV 然后存到 keychain 中缓存
func (s *SdkDeviceId) IOS() (string, error) {
	if s.DeviceId != "" {
		return s.DeviceId, nil
	}
	if s.Idfv != "" {
		return s.Idfv, nil
	}
	if s.Idfa != "" {
		return s.Idfa, nil

	}
	return "", errors.New("no device id")
}
func (s *SdkDeviceId) Other() (string, error) {
	if s.DeviceId != "" {
		return s.DeviceId, nil
	}
	return "", errors.New("no device id")
}

func (s *SdkDeviceId) getInformation(information string) error {
	if err := sonic.UnmarshalString(information, s); err != nil {
		return err
	}
	xlog.Info("get device id", xlog.Any("information", s))
	return nil
}

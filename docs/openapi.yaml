# Generated with protoc-gen-openapi
# https://gitlab.papegames.com/fringe/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
servers:
    - url: https://risk.papegames.com
paths:
    /v1/health:
        get:
            tags:
                - RiskSDKService
            description: 健康检查
            operationId: Health
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: p2
    /v1/risk/account/check:
        post:
            tags:
                - RiskServerService
            description: 账号风控检测
            operationId: 账号风控检测
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AccountRiskCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/HandlerRequest'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/app/check:
        post:
            tags:
                - RiskServerService
            description: 业务风控检测
            operationId: 业务风控检测
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AppRiskCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/HandlerRequest'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/app_id/check:
        post:
            tags:
                - RiskServerService
            description: appId 验证
            operationId: 加密数据解密
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/CheckAppIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/CheckAppIdRequest'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/bind/notice:
        post:
            tags:
                - RiskServerService
            operationId: 绑定手机号通知
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/BindNoticeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/biz/init:
        post:
            tags:
                - RiskSDKService
            description: init接口
            operationId: 初始化接口
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/BizInitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/BizInitResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/captcha/g/check:
        post:
            tags:
                - RiskSDKService
            description: 极验验证接口
            operationId: 极验验证接口
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/GCaptchaRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GCaptchaResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/captcha/token/check:
        post:
            tags:
                - RiskServerService
            description: 极验服务端接口
            operationId: 极验验证接口
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/CaptchaTokenCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/CaptchaTokenCheckResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/code/check:
        post:
            tags:
                - RiskSDKService
            operationId: 短信验证
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKCheckCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/comet/send:
        post:
            tags:
                - RiskServerService
            operationId: 发送长链消息
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKSendCometRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/decode:
        post:
            tags:
                - RiskServerService
            description: 加密数据解密
            operationId: 加密数据解密
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/DecodeDataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/DecodeDataResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/device/risk:
        post:
            tags:
                - RiskServerService
            description: 设备风险信息查询
            operationId: 设备风险信息查询
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/DeviceRiskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/DeviceRiskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/device/sdk_doid:
        post:
            tags:
                - RiskServerService
            operationId: SDK DOID转DOID
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKDOIDRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SDKDOIDResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/ds/check:
        post:
            tags:
                - RiskServerService
            operationId: SDK DOID验证
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKDOIDCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SDKDOIDCheckResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/email/check:
        post:
            tags:
                - RiskSDKService
            operationId: 邮箱地址验证
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKCheckEmailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/encode:
        post:
            tags:
                - RiskServerService
            description: 数据加密
            operationId: 加密数据解密
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/DecodeDataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/DecodeDataResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/face/code:
        post:
            tags:
                - RiskSDKService
            operationId: 人脸验证初始化（移动端）
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/FaceCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/FaceCodeResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/face/code/web:
        post:
            tags:
                - RiskServerService
            operationId: 人脸验证初始化（Server端)
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/FaceCodeForWebRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/FaceCodeForWebResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/face/qrcode/generate:
        post:
            tags:
                - RiskServerService
            operationId: 二维码初始化（PC端)
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/QrCodeGenerateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/QrCodeGenerateResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/face/qrcode/scan:
        post:
            tags:
                - RiskServerService
            operationId: 二维码扫码
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/QrCodeScanRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/QrCodeScanResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/face/qrcode/status:
        post:
            tags:
                - RiskServerService
            operationId: 使用code查询人脸认证状态
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/FaceStatusQueryByQrCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/FaceStatusQueryByQrCodeResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/face/result:
        post:
            tags:
                - RiskSDKService
            operationId: 人脸验证认证结果
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/FaceResultRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/FaceResultResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/face/status:
        post:
            tags:
                - RiskServerService
            operationId: FaceStatus
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/FaceStatusRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/FaceStatusResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/gs/check:
        post:
            tags:
                - RiskSDKService
            description: 游戏账号风险信息查询
            operationId: 游戏账号风险信息查询
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/NIDCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/NIDCheckResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/hd/get:
        post:
            tags:
                - RiskSDKService
            description: 获取设备号
            operationId: 获取DOID
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/DeviceGetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/DeviceGetResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/payment/check:
        post:
            tags:
                - RiskServerService
            description: 支付风控检测
            operationId: 支付风控检测
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/PayRiskCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/HandlerRequest'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: 服务端
    /v1/risk/phone/check:
        post:
            tags:
                - RiskSDKService
            operationId: 手机号码验证
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKCheckPhoneRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SDKCheckPhoneResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/phone/sendcode:
        post:
            tags:
                - RiskSDKService
            operationId: 手机号码单发短信
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/SDKPhoneCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P0
            x-apifox-folder: SDK端
    /v1/risk/shumei/decode:
        post:
            tags:
                - RiskServerService
            operationId: 数美boxId解密
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/ShumeiDecodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/ShumeiDecodeResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 服务端
    /v1/risk/tob/approved/delete:
        post:
            tags:
                - RiskServerService
            operationId: 允许账号/设备-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/DeleteApprovedRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/black_list/create:
        post:
            tags:
                - RiskServerService
            description: 黑名单列表-添加
            operationId: 加白名单
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AddBlackListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/AddResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/black_list/delete:
        post:
            tags:
                - RiskServerService
            description: 黑名单列表-删除
            operationId: 黑名单列表-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/ClientIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/black_list/update:
        post:
            tags:
                - RiskServerService
            description: 黑名单列表-修改
            operationId: 黑名单列表-修改
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/EditBlackListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/config_app/create:
        post:
            tags:
                - RiskServerService
            description: 风控开关配置-添加
            operationId: 风控配置-添加
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AddConfigAppRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/AddResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/config_app/delete:
        post:
            tags:
                - RiskServerService
            description: 风控开关配置-删除
            operationId: 风控配置-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/ClientIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/config_app/update:
        post:
            tags:
                - RiskServerService
            description: 风控开关配置-修改
            operationId: 风控配置-修改
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/EditConfigAppRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/dark_list/create:
        post:
            tags:
                - RiskServerService
            description: 黑产列表-添加
            operationId: 黑产列表-添加
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AddDarkListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/AddResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/dark_list/delete:
        post:
            tags:
                - RiskServerService
            description: 黑产列表-删除
            operationId: 黑产列表-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/ClientIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/dark_list/update:
        post:
            tags:
                - RiskServerService
            description: 黑产列表-修改
            operationId: 黑产列表-修改
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/EditDarkListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/geetest_config/reload:
        post:
            tags:
                - RiskServerService
            operationId: 重新加载极验配置
            requestBody:
                content:
                    application/x-www-form-urlencoded: {}
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/white_list/create:
        post:
            tags:
                - RiskServerService
            description: 白名单列表-添加
            operationId: 黑名单列表-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/AddWhiteListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/AddResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/white_list/delete:
        post:
            tags:
                - RiskServerService
            description: 白名单列表-删除
            operationId: 白名单列表-删除
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/ClientIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
    /v1/risk/tob/white_list/update:
        post:
            tags:
                - RiskServerService
            description: 白名单列表-修改
            operationId: 白名单列表-修改
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/EditWhiteListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: ToB端
components:
    schemas:
        AccountRiskCheckRequest:
            required:
                - nid
                - ip
                - action
                - client_id
            type: object
            properties:
                nid:
                    minimum: !!float 1
                    type: string
                ip:
                    pattern: ipv4
                    type: string
                action:
                    type: string
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                device:
                    type: string
                DOID:
                    type: string
                app_id:
                    type: string
                lang:
                    type: string
                role_id:
                    type: string
                scene:
                    type: string
        AddBlackListRequest:
            required:
                - client_id
                - app_id
                - uid
                - type
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                app_id:
                    type: string
                uid:
                    type: string
                type:
                    type: integer
                    format: int32
                expired_at:
                    type: integer
                    format: int64
        AddConfigAppRequest:
            required:
                - app_id
                - frequency
            type: object
            properties:
                client_id:
                    type: integer
                    format: uint32
                app_id:
                    type: string
                frequency:
                    type: string
                is_open:
                    type: integer
                    format: int32
                type:
                    type: integer
                    format: int32
                title:
                    type: string
                handle:
                    type: integer
                    format: int32
        AddDarkListRequest:
            required:
                - client_id
                - app_id
                - uid
                - type
                - score
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                app_id:
                    type: string
                uid:
                    type: string
                type:
                    type: integer
                    format: int32
                score:
                    type: integer
                    description: int32 provider = 5 [   (google.api.field_behavior) = REQUIRED ];
                    format: int32
                expired_at:
                    type: integer
                    format: int64
        AddResponse:
            type: object
            properties:
                id:
                    type: integer
                    format: uint64
        AddWhiteListRequest:
            required:
                - client_id
                - app_id
                - uid
                - type
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                app_id:
                    type: string
                uid:
                    type: string
                type:
                    type: integer
                    format: int32
                expired_at:
                    type: integer
                    format: int64
        AppRiskCheckRequest:
            required:
                - nid
                - ip
                - action
                - app_id
            type: object
            properties:
                nid:
                    minimum: !!float 1
                    type: string
                    description: 用户的nid
                ip:
                    pattern: ipv4
                    type: string
                action:
                    type: string
                app_id:
                    type: string
                device:
                    type: string
                DOID:
                    type: string
                client_id:
                    type: integer
                    format: uint32
                lang:
                    type: string
                role_id:
                    type: string
                scene:
                    type: string
        BindNoticeRequest:
            type: object
            properties:
                nid:
                    type: integer
                    format: int64
                code:
                    type: integer
                    format: int32
                client_id:
                    type: integer
                    format: uint32
                app_id:
                    type: string
                doid:
                    type: string
                nonce:
                    type: string
        BizInitCaptchaData:
            type: object
            properties:
                captcha_id:
                    type: string
                close:
                    type: boolean
        BizInitRequest:
            required:
                - client_id
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                include_keys:
                    type: string
                exclude_keys:
                    type: string
        BizInitResponse:
            type: object
            properties:
                captcha:
                    $ref: '#/components/schemas/BizInitCaptchaData'
                oae:
                    type: boolean
                ccr:
                    type: boolean
                interactive_msg:
                    type: string
                options:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/RawMessage'
        CaptchaTokenCheckRequest:
            required:
                - client_id
                - pass_token
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                pass_token:
                    type: string
        CaptchaTokenCheckResponse:
            type: object
            properties:
                has_pass:
                    type: boolean
        CheckAppIdRequest:
            type: object
            properties:
                app_id:
                    type: string
                app_key:
                    type: string
                aes_key:
                    type: string
                app_secret:
                    type: string
        ClientIdRequest:
            required:
                - client_id
                - id
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: int64
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
        DecodeDataRequest:
            type: object
            properties:
                app_id:
                    type: string
                timestamp:
                    type: integer
                    format: int64
                data:
                    type: string
        DecodeDataResponse:
            type: object
            properties:
                value:
                    $ref: '#/components/schemas/RawMessage'
        DeleteApprovedRequest:
            type: object
            properties:
                nid:
                    type: string
                DOID:
                    type: string
        DeviceGetRequest:
            required:
                - information
                - third_device_id
            type: object
            properties:
                information:
                    type: string
                third_device_id:
                    type: string
        DeviceGetResponse:
            type: object
            properties:
                DOID:
                    type: string
                provider_device_id:
                    type: string
        DeviceRiskRequest:
            required:
                - DOID
                - client_id
            type: object
            properties:
                DOID:
                    type: string
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
        DeviceRiskResponse:
            type: object
            properties:
                score:
                    type: integer
                    format: uint32
        EditBlackListRequest:
            required:
                - client_id
                - id
                - expired_at
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                expired_at:
                    type: integer
                    format: int64
        EditConfigAppRequest:
            required:
                - id
                - is_open
            type: object
            properties:
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                frequency:
                    type: string
                handle:
                    type: integer
                    format: int64
                is_open:
                    type: integer
                    format: int32
        EditDarkListRequest:
            required:
                - client_id
                - id
                - expired_at
                - score
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                expired_at:
                    type: integer
                    format: int64
                score:
                    type: integer
                    format: int32
        EditWhiteListRequest:
            required:
                - client_id
                - id
                - expired_at
            type: object
            properties:
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                expired_at:
                    type: integer
                    format: int64
        FaceCodeForWebRequest:
            required:
                - scene
                - vendor
                - client_id
                - DOID
                - return_url
                - app_id
            type: object
            properties:
                nid:
                    type: string
                    description: 用户nid
                token:
                    type: string
                    description: 用户token
                real_name:
                    type: string
                    description: 真实姓名
                id_card:
                    type: string
                    description: 身份证
                phone:
                    type: string
                    description: 手机号码
                scene:
                    type: string
                    description: 场景，固定值:account_real_info
                vendor:
                    type: string
                    description: 厂商，固定值:aliyun
                extra:
                    type: string
                    description: 扩展信息，json格式, meta_info必传，{"meta_info":{}}
                client_id:
                    type: integer
                    description: 租户 ID
                    format: int64
                DOID:
                    type: string
                    description: DOID值
                return_url:
                    type: string
                    description: 认证结束后回跳页面的链接地址
                app_id:
                    type: string
                    description: appId
                ip:
                    type: string
                    description: ip
        FaceCodeForWebResponse:
            type: object
            properties:
                vendor:
                    type: string
                    description: 厂商
                code:
                    type: string
                    description: 请求的唯一code
                return_url:
                    type: string
                    description: 认证结束后回跳页面的链接地址
                service:
                    allOf:
                        - $ref: '#/components/schemas/FaceServiceData'
                    description: 厂商的扩展数据
        FaceCodeRequest:
            required:
                - nid
                - token
                - scene
                - vendor
                - client_id
                - DOID
            type: object
            properties:
                nid:
                    type: string
                token:
                    type: string
                scene:
                    type: string
                vendor:
                    type: string
                extra:
                    type: string
                client_id:
                    type: integer
                    format: int64
                DOID:
                    type: string
        FaceCodeResponse:
            type: object
            properties:
                vendor:
                    type: string
                code:
                    type: string
                service:
                    $ref: '#/components/schemas/FaceServiceData'
        FaceResultRequest:
            required:
                - code
                - client_id
            type: object
            properties:
                code:
                    type: string
                    description: 人脸识别初始化接口返回的code
                client_id:
                    type: integer
                    description: 租户 ID
                    format: int64
                DOID:
                    type: string
                    description: DOID值
        FaceResultResponse:
            type: object
            properties:
                status:
                    type: integer
                    format: int32
                pass_token:
                    type: string
        FaceServiceData:
            type: object
            properties:
                certify_id:
                    type: string
                    description: 认证id
        FaceStatusQueryByQrCodeRequest:
            required:
                - qrcode_id
            type: object
            properties:
                qrcode_id:
                    type: string
                    description: 二维码id
        FaceStatusQueryByQrCodeResponse:
            type: object
            properties:
                status:
                    type: integer
                    description: '人脸认证状态 0:未认证 1：认证通过  2: 认证不通过'
                    format: uint32
        FaceStatusRequest:
            required:
                - client_id
            type: object
            properties:
                nid:
                    type: string
                    description: 用户nid
                scene:
                    type: string
                    description: 场景
                pass_token:
                    type: string
                    description: 人脸识别token
                client_id:
                    type: integer
                    description: 租户 ID
                    format: int64
                DOID:
                    type: string
                ip:
                    type: string
                id_card:
                    type: string
                real_name:
                    type: string
        FaceStatusResponse:
            type: object
            properties:
                status:
                    type: integer
                    format: uint32
        GCaptchaRequest:
            required:
                - number
                - captcha
                - token
                - time
                - captcha_id
                - nonce
                - client_id
                - app_id
            type: object
            properties:
                number:
                    type: string
                captcha:
                    type: string
                token:
                    type: string
                time:
                    type: string
                captcha_id:
                    type: string
                nonce:
                    type: string
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                app_id:
                    type: string
                DOID:
                    type: string
        GCaptchaResponse:
            type: object
            properties:
                pass_token:
                    type: string
        HandlerRequest:
            type: object
            properties:
                nonce:
                    type: string
                provider:
                    type: string
                phone:
                    type: array
                    items:
                        type: string
                sale_phone:
                    type: array
                    items:
                        type: string
                email:
                    type: string
                sale_email:
                    type: string
                ttl:
                    type: integer
                    format: int64
        NIDCheckRequest:
            required:
                - nid
                - client_id
                - sig
                - timestamp
            type: object
            properties:
                nid:
                    type: string
                ip:
                    type: string
                client_id:
                    type: integer
                    format: uint32
                DOID:
                    type: string
                sig:
                    type: string
                timestamp:
                    type: integer
                    format: int64
        NIDCheckResponse:
            type: object
            properties:
                risk_score:
                    type: integer
                    format: int64
        PayRiskCheckRequest:
            required:
                - nid
                - action
                - client_id
            type: object
            properties:
                nid:
                    minimum: !!float 1
                    type: string
                ip:
                    type: string
                action:
                    type: string
                client_id:
                    minimum: !!float 100
                    type: integer
                    format: uint32
                device:
                    type: string
                charge_type:
                    type: integer
                    format: uint32
                platform:
                    type: integer
                    format: uint32
                product_id:
                    type: string
                role_id:
                    type: string
                region:
                    type: integer
                    format: uint32
                zone_id:
                    type: integer
                    format: uint32
                lang:
                    type: string
                app_id:
                    type: string
                scene:
                    type: string
        QrCodeGenerateRequest:
            required:
                - nid
                - client_id
                - token
                - scene
                - vendor
                - doid
                - app_id
            type: object
            properties:
                nid:
                    type: string
                    description: 用户nid
                client_id:
                    type: integer
                    description: 租户 ID
                    format: int64
                token:
                    type: string
                    description: 用户token
                real_name:
                    type: string
                    description: 真实姓名
                id_card:
                    type: string
                    description: 身份证
                return_url:
                    type: string
                scene:
                    type: string
                vendor:
                    type: string
                doid:
                    type: string
                app_id:
                    type: string
        QrCodeGenerateResponse:
            type: object
            properties:
                qrcode_id:
                    type: string
                    description: 二维码id
                expired_at:
                    type: string
                    description: 过期时间
        QrCodeScanRequest:
            required:
                - qrcode_id
            type: object
            properties:
                qrcode_id:
                    type: string
                    description: 二维码id
        QrCodeScanResponse:
            required:
                - client_id
                - nid
                - token
                - scene
                - vendor
                - doid
                - app_id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户 ID
                    format: int64
                nid:
                    type: string
                    description: 用户nid
                token:
                    type: string
                    description: 用户token
                real_name:
                    type: string
                    description: 真实姓名
                id_card:
                    type: string
                    description: 身份证
                return_url:
                    type: string
                scene:
                    type: string
                vendor:
                    type: string
                doid:
                    type: string
                app_id:
                    type: string
        RawMessage:
            type: object
        SDKCheckCodeRequest:
            required:
                - nonce
                - code
                - client_id
                - app_id
            type: object
            properties:
                nonce:
                    type: string
                code:
                    type: string
                client_id:
                    type: integer
                    format: uint32
                app_id:
                    type: string
                DOID:
                    type: string
        SDKCheckEmailRequest:
            required:
                - nonce
                - email
            type: object
            properties:
                nonce:
                    type: string
                email:
                    type: string
        SDKCheckPhoneRequest:
            required:
                - nonce
                - phone
                - client_id
                - app_id
            type: object
            properties:
                nonce:
                    type: string
                phone:
                    type: string
                mock:
                    type: integer
                    format: int32
                client_id:
                    type: integer
                    format: uint32
                app_id:
                    type: string
        SDKCheckPhoneResponse:
            type: object
            properties:
                code:
                    type: string
        SDKDOIDCheckRequest:
            required:
                - DOID
            type: object
            properties:
                DOID:
                    type: string
        SDKDOIDCheckResponse:
            type: object
            properties:
                pass:
                    type: boolean
                device_id:
                    type: string
        SDKDOIDRequest:
            type: object
            properties:
                sdk_DOID:
                    type: string
        SDKDOIDResponse:
            type: object
            properties:
                DOID:
                    type: string
        SDKPhoneCodeRequest:
            required:
                - nonce
                - client_id
                - app_id
            type: object
            properties:
                nonce:
                    type: string
                client_id:
                    type: integer
                    format: uint32
                app_id:
                    type: string
        SDKSendCometRequest:
            required:
                - clientid
                - nid
            type: object
            properties:
                clientid:
                    type: string
                nid:
                    type: string
                code:
                    type: integer
                    format: uint64
                doid:
                    type: string
                ip:
                    type: string
                account:
                    type: string
        SendCometRequest:
            required:
                - client_id
                - nid
            type: object
            properties:
                client_id:
                    type: string
                nid:
                    type: string
                code:
                    type: integer
                    format: uint64
                DOID:
                    type: string
                ip:
                    type: string
                account:
                    type: string
        ShumeiDecodeRequest:
            type: object
            properties:
                boxid:
                    type: string
        ShumeiDecodeResponse:
            type: object
            properties:
                value:
                    type: string
                boxid:
                    type: string
        _failReturn:
            type: object
            properties:
                code:
                    example: 400
                    type: integer
                    format: int32
                info:
                    example: error
                    type: string
                request_id:
                    example: 16vHbfABAd
                    type: string
tags:
    - name: RiskSDKService
      description: This API represents pay-risk service.
    - name: RiskServerService
      description: This API represents pay-risk service.

package control

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/constant"
	"risk/proto"
	"risk/service"
)

func (c *ServerControl) PayCheckRisk(ctx context.Context, request *proto.PayRiskCheckRequest) (*proto.HandlerRequest, error) {
	var params service.Params
	err := copier.Copy(&params, request)
	logger := xlog.FromContext(ctx)
	if err != nil {
		logger.Error(fmt.Sprintf("copier.Copy error:%v", err))
		return nil, err
	}
	params.Type = constant.RiskTypePay
	if request.Scene == "" {
		params.Scene = constant.RiskPayment
	}
	return service.NewRiskStrategy(ctx).ChkStrategy(&params)
}

func (c *ServerControl) AccountCheckRisk(ctx context.Context, request *proto.AccountRiskCheckRequest) (*proto.HandlerRequest, error) {
	var params service.Params
	copier.Copy(&params, request)
	err := copier.Copy(&params, request)
	logger := xlog.FromContext(ctx)
	if err != nil {
		logger.Error(fmt.Sprintf("copier.Copy error:%v", err))
		return nil, err
	}
	params.Type = constant.RiskTypeAccount
	if request.Scene == "" {
		params.Scene = constant.RiskSDK
	}
	params.HasEncode = 1
	return service.NewRiskStrategy(ctx).ChkStrategy(&params)
}

func (c *ServerControl) AppCheckRisk(ctx context.Context, request *proto.AppRiskCheckRequest) (*proto.HandlerRequest, error) {
	var params service.Params
	err := copier.Copy(&params, request)
	logger := xlog.FromContext(ctx)
	if err != nil {
		logger.Error(fmt.Sprintf("copier.Copy error:%v", err))
		return nil, err
	}
	params.Type = constant.RiskTypeApp
	if request.Scene == "" {
		params.Scene = constant.RiskSDK
	}
	params.HasEncode = 1
	return service.NewRiskStrategy(ctx).ChkStrategy(&params)
}

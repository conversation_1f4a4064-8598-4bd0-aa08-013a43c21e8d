package control

import (
	"context"
	"crypto/md5"
	"fmt"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/nuclear/ecode"
	"risk/constant"
	"risk/proto"
	"risk/service"
	"risk/startup"
	"strconv"
)

func (s *SDKControl) NIDRisk(ctx context.Context, request *proto.NIDCheckRequest) (*proto.NIDCheckResponse, error) {
	logger := xlog.FromContext(ctx)

	// 验签
	config := startup.GetClientConfig()
	clientConfig, err := config.Get(strconv.Itoa(int(request.ClientId)))
	if err != nil {
		logger.Error("get client config error", xlog.Err(err))
		return nil, nil
	}

	md := md5.New()
	str := fmt.Sprintf("%s%d", clientConfig.Secret, request.Timestamp)
	md.Write([]byte(str))
	sign := fmt.Sprintf("%x", md.Sum(nil))

	if sign != request.Sig {
		logger.Error("sign error",
			xlog.Any("request", request),
			xlog.String("sign str", str),
			xlog.String("sign", sign))
		return nil, ecode.RetErrArgs
	}

	var params service.Params
	err = copier.Copy(&params, request)
	if err != nil {
		logger.Error("copier.Copy error:%v", xlog.Err(err))
		return nil, nil
	}
	params.Action = constant.RiskEnterGame
	return service.NewRiskStrategy(ctx).ChkByNidForGame(&params)
}

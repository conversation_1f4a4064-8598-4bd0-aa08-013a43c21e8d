package control

import (
	"context"
	"risk/proto"
	"risk/service"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func (c *SDKControl) FaceCode(ctx context.Context, in *proto.FaceCodeRequest) (*proto.FaceCodeResponse, error) {
	svc, err := service.NewFaceSvcByVendor(ctx, in.Vendor, nil)
	if err != nil {
		xlog.FromContext(ctx).Error("NewFaceSvc error", xlog.Err(err))
		return nil, err
	}
	return svc.GetCodeForSDK(in)
}

func (c *SDKControl) FaceResult(ctx context.Context, in *proto.FaceResultRequest) (*proto.FaceResultResponse, error) {
	svc, err := service.GetFaceSvcByCode(ctx, in.Code)
	if err != nil {
		xlog.FromContext(ctx).Error("NewFaceSvc error", xlog.Err(err))
		return nil, err
	}
	return svc.Verify(in)
}

func (s *ServerControl) FaceStatus(ctx context.Context, in *proto.FaceStatusRequest) (*proto.FaceStatusResponse, error) {
	svc := service.NewFaceSvc(ctx)
	return svc.CheckStatus(in)
}

func (c *ServerControl) FaceCodeForWeb(ctx context.Context, in *proto.FaceCodeForWebRequest) (*proto.FaceCodeForWebResponse, error) {
	svc, err := service.NewFaceSvcByVendor(ctx, in.Vendor, nil)
	if err != nil {
		xlog.FromContext(ctx).Error("NewFaceSvc error", xlog.Err(err))
		return nil, err
	}
	return svc.GetCodeForWeb(in)
}

func (c *ServerControl) QrCodeGenerate(ctx context.Context, in *proto.QrCodeGenerateRequest) (*proto.QrCodeGenerateResponse, error) {
	logger := xlog.FromContext(ctx)
	logger.Info("QrCodeGenerate", xlog.Any("in", in))
	return nil, nil
}
func (c *ServerControl) QrCodeScan(ctx context.Context, in *proto.QrCodeScanRequest) (*proto.QrCodeScanResponse, error) {
	logger := xlog.FromContext(ctx)
	logger.Info("QrCodeGenerate", xlog.Any("in", in))
	return nil, nil
}
func (c *ServerControl) FaceStatusQueryByQrCode(ctx context.Context, in *proto.FaceStatusQueryByQrCodeRequest) (*proto.FaceStatusQueryByQrCodeResponse, error) {
	logger := xlog.FromContext(ctx)
	logger.Info("QrCodeGenerate", xlog.Any("in", in))
	return nil, nil
}

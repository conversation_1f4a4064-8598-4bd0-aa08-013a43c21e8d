package control

import (
	"context"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"risk/proto"
	"risk/rpc"
	"risk/service"
)

func (c *ServerControl) SDKSendComet(ctx context.Context, req *proto.SDKSendCometRequest) (*xtype.Empty, error) {
	nonceResp, err := service.NewRisk(ctx).GetNoPassResponseForComet(ctx, req.GetIp(), req.GetNid(), req.GetDoid(), uint32(req.GetCode()))
	if err != nil {
		return nil, err
	}
	msg, _ := sonic.MarshalString(nonceResp)

	if err := rpc.CometBroadcast(ctx, req.GetClientid(), req.Nid, msg); err != nil {
		return nil, err
	}

	return &xtype.Empty{}, nil
}

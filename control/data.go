package control

import (
	"context"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/pkg/shared/xencrypt"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"risk/proto"
	"risk/startup"
)

func (s *ServerControl) DecodeData(c context.Context, in *proto.DecodeDataRequest) (*proto.DecodeDataResponse, error) {
	appVerify := startup.GetVerify()
	if data := appVerify.GetVerifyData(in.AppId); data == nil {
		return nil, ecode.Error(ecode.BadRequest, "app id不存在！")
	}

	gCtx := xgin.FromContext(c)
	resData, err := xencrypt.
		NewXEncrypt(appVerify.GetAppPlatform(in.AppId)).
		Decrypt(gCtx.Request.Form, in.Data, appVerify.GetAesKey(in.AppId), c)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "解密数据失败！")

	}

	value, _ := sonic.MarshalString(resData)
	data := `{"code":0,"msg":"success","data":` + value + `}`
	gCtx.String(200, data)
	return nil, ecode.ResetContent
}

func (s *ServerControl) EncodeData(c context.Context, in *proto.DecodeDataRequest) (*proto.DecodeDataResponse, error) {
	appVerify := startup.GetVerify()
	if data := appVerify.GetVerifyData(in.AppId); data == nil {
		return nil, ecode.Error(ecode.BadRequest, "app id不存在！")
	}

	gCtx := xgin.FromContext(c)
	resData, err := xencrypt.
		NewXEncrypt(appVerify.GetAppPlatform(in.AppId)).
		Encrypt(gCtx.Request.Form, in.Data, appVerify.GetAesKey(in.AppId), c)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "解密数据失败！")
	}
	value, _ := sonic.Marshal(resData)
	return &proto.DecodeDataResponse{
		Value: xtype.NewRawMessage(value),
	}, nil
}
func (s *ServerControl) CheckAppId(c context.Context, in *proto.CheckAppIdRequest) (*proto.CheckAppIdRequest, error) {
	appVerify := startup.GetVerify()
	data := appVerify.GetVerifyData(in.AppId)
	if data == nil {
		return nil, ecode.Error(ecode.BadRequest, "app id不存在！")
	}

	if in.AppKey != "" && in.AppKey != appVerify.GetAppKey(in.AppId) {
		return nil, ecode.Error(ecode.BadRequest, "app key错误！")
	}

	if in.AppSecret != "" && in.AppSecret != appVerify.GetAppSecret(in.AppId) {
		return nil, ecode.Error(ecode.BadRequest, "app secret错误！")
	}

	if in.AesKey != "" && in.AesKey != appVerify.GetAesKey(in.AppId) {
		return nil, ecode.Error(ecode.BadRequest, "aes key错误！")
	}

	xgin.FromContext(c).JSON(200, &proto.CheckAppIdRequest{
		AppId:     data.AppID,
		AppKey:    data.AppKey,
		AesKey:    data.AesKey,
		AppSecret: data.AppSecret,
	})
	return nil, ecode.ResetContent
}

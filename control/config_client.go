package control

import (
	"context"
	"risk/proto"
	"risk/service/conf"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

// 添加
func (c *ServerControl) AddConfigApp(ctx context.Context, req *proto.AddConfigAppRequest) (*proto.AddResponse, error) {
	s := conf.NewConfigClient(ctx)
	if ok, err := s.Add(req); err != nil || !ok {
		return nil, err
	}
	return nil, nil
}

// 编辑
func (c *ServerControl) EditConfigApp(ctx context.Context, req *proto.EditConfigAppRequest) (*xtype.Empty, error) {
	s := conf.NewConfigClient(ctx)
	if ok, err := s.Update(req); err != nil || !ok {
		return nil, err
	}
	return nil, nil
}

// 删除
func (c *ServerControl) DeleteConfigApp(ctx context.Context, req *proto.ClientIdRequest) (*xtype.Empty, error) {
	s := conf.NewConfigClient(ctx)
	if ok, err := s.Delete(req); err != nil || !ok {
		return nil, err
	}
	return nil, nil
}

package control

import (
	"context"
	"risk/proto"
	"risk/service/conf"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

// 添加
func (c *ServerControl) AddBlackList(ctx context.Context, req *proto.AddBlackListRequest) (*proto.AddResponse, error) {
	s := conf.NewConfBlacklistSvr(ctx)
	if id, err := s.Add(ctx, req); err != nil || id == 0 {
		return &proto.AddResponse{Id: 0}, err
	} else {
		return &proto.AddResponse{Id: id}, nil
	}
}

// 编辑
func (c *ServerControl) EditBlackList(ctx context.Context, req *proto.EditBlackListRequest) (*xtype.Empty, error) {
	s := conf.NewConfBlacklistSvr(ctx)
	if ok, err := s.Update(ctx, req); err != nil || !ok {
		return nil, err
	}
	return nil, nil
}

// 删除
func (c *ServerControl) DeleteBlackList(ctx context.Context, req *proto.ClientIdRequest) (*xtype.Empty, error) {
	s := conf.NewConfBlacklistSvr(ctx)
	if ok, err := s.Delete(ctx, req); err != nil || !ok {
		return nil, err
	}
	return nil, nil
}

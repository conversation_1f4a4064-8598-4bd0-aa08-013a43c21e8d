package control

import (
	"context"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"go.uber.org/zap"
	"risk/config"
	"risk/proto"
	"risk/repo/device"
	"risk/rpc"
	"risk/service"
)

func (c *SDKControl) DeviceGet(ctx context.Context, in *proto.DeviceGetRequest) (*proto.DeviceGetResponse, error) {
	gCtx := xgin.FromContext(ctx)
	svc := service.NewDevice(ctx)

	return svc.SDKCreateDOID(in.ThirdDeviceId, in.Information, shared.GetClientIP(gCtx))
}

func (c *SDKControl) ShumeiDeviceGet(ctx context.Context, in *proto.ShumeiDeviceGetRequest) (*proto.DeviceGetResponse, error) {
	gCtx := xgin.FromContext(ctx)
	device.Headers = map[string]string{
		"User-Agent":      gCtx.GetHeader("User-Agent"),
		"X-Forwarded-For": gCtx.GetHeader("X-Forwarded-For"),
		"X-Real-IP":       shared.GetClientIP(gCtx),
	}

	logger := xlog.FromContext(ctx)
	body, err := gCtx.GetRawData()
	if err != nil {
		logger.Error("GetRawData error", zap.Error(err))
		return nil, ecode.ServerError
	}

	var data proto.ShumeiDeviceRequestData
	if err = sonic.Unmarshal(body, &data); err != nil {
		logger.Error("Unmarshal error", zap.Error(err))
		return nil, ecode.ServerError
	}

	svc := service.NewDevice(ctx)

	resp, err := svc.ThirdSDKCreate(string(body), shared.GetClientIP(gCtx), data.Os, "shumei")
	if err != nil {
		return nil, ecode.ServerError
	}

	gCtx.JSON(200, resp)
	return nil, ecode.ResetContent
}

func (c *SDKControl) DeviceLogs(ctx context.Context, in *proto.DeviceLogsRequest) (*xtype.Empty, error) {
	return nil, nil
}

func (c *SDKControl) ShumeiDeviceConfig(ctx context.Context, in *proto.ShumeiDeviceConfigRequest) (*proto.ShumeiDeviceConfigResponse, error) {
	gCtx := xgin.FromContext(ctx)
	body, err := gCtx.GetRawData()
	logger := xlog.FromContext(ctx)
	if err != nil {
		logger.Error("GetRawData error", zap.Error(err))
		return nil, ecode.ServerError
	}

	device.Headers = map[string]string{
		"User-Agent":      gCtx.GetHeader("User-Agent"),
		"X-Forwarded-For": gCtx.GetHeader("X-Forwarded-For"),
		"X-Real-IP":       shared.GetClientIP(gCtx),
	}
	providerRepo := device.NewDevice("shumei")
	res, err := providerRepo.GetConfig(ctx, string(body))
	if err != nil {
		logger.Error("GetConfig error", zap.Error(err))
		return nil, ecode.ServerError
	}

	gCtx.String(200, res)
	return nil, ecode.ResetContent
}

func (s *ServerControl) SDKDOIDCheck(ctx context.Context, in *proto.SDKDOIDCheckRequest) (*proto.SDKDOIDCheckResponse, error) {
	svc := service.NewDevice(ctx)
	deviceId, err := svc.SDKDOIDCheck(in.GetDOID())
	if err != nil {
		return nil, ecode.Wrap(ecode.BadRequest, err)
	}
	return &proto.SDKDOIDCheckResponse{Pass: true, DeviceId: deviceId}, nil

}

func (c *SDKControl) SendComet(ctx context.Context, in *proto.SendCometRequest) (*xtype.Empty, error) {
	if !config.Get().Debug {
		return nil, nil
	}
	nonceResp, err := service.NewRisk(ctx).GetNoPassResponseForComet(ctx, in.GetIp(), in.GetNid(), in.GetDOID(), uint32(in.GetCode()))
	if err != nil {
		return nil, err
	}
	msg, _ := sonic.MarshalString(nonceResp)

	if err := rpc.CometBroadcast(ctx, in.GetClientId(), in.Nid, msg); err != nil {
		return nil, err
	}

	return nil, nil
}

func (s *ServerControl) ShumeiDecode(ctx context.Context, in *proto.ShumeiDecodeRequest) (*proto.ShumeiDecodeResponse, error) {
	var shumei device.Shumei //数美
	thirdDeviceId, err := shumei.OutputBoxId(in.GetBoxid())
	if err != nil {
		xlog.FromContext(ctx).Error("OutputBoxId error", zap.Error(err))
		return nil, ecode.Error(ecode.BadRequest, err.Error()+",boxid错误！")
	}
	return &proto.ShumeiDecodeResponse{Value: thirdDeviceId}, nil
}

func (s *ServerControl) SDKDOIDToDOID(ctx context.Context, in *proto.SDKDOIDRequest) (*proto.SDKDOIDResponse, error) {
	svc := service.NewDevice(ctx)
	doid, err := svc.SDKDOIDToDOID(in.GetSdk_DOID())
	if err != nil {
		return nil, err
	} else {
		return &proto.SDKDOIDResponse{DOID: doid}, nil
	}
}

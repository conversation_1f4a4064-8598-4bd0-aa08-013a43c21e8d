package control

import (
	"context"
	"encoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"risk/config"
	"risk/proto"
	"risk/service/conf"
	"risk/utils"
	"strconv"
	"strings"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
)

func (c *SDKControl) BizInit(ctx context.Context, req *proto.BizInitRequest) (*proto.BizInitResponse, error) {
	srvins := conf.GetConfGeetestConfSvrIns()
	res, err := srvins.GetByAppID(ctx, utils.GetAppID(xgin.FromContext(ctx)))
	if err != nil {
		return nil, err
	}

	var close bool
	if config.Get().Crypto.Close {
		if config.Get().Crypto.GrayScalePercentage > 0 {
			rand := time.Now().Nanosecond()
			close = utils.CheckIfInGrayScale(strconv.Itoa(rand), config.Get().Crypto.GrayScalePercentage)
		} else {
			close = config.Get().Crypto.Close
		}
	}

	prompt := "您确认关闭吗？"
	if config.Get().Machine.Prompt != "" {
		prompt = config.Get().Machine.Prompt
	}

	var includeKeys, excludeKeys []string

	if req.IncludeKeys != "" {
		includeKeys = strings.Split(req.IncludeKeys, ",")
	}

	if req.ExcludeKeys != "" {
		excludeKeys = strings.Split(req.ExcludeKeys, ",")
	}

	options := make(map[string]*xtype.RawMessage)
	sdkConfig := config.Get().Options
	for key, val := range sdkConfig {
		b, _ := json.Marshal(val)
		options[key] = &xtype.RawMessage{Raw: b}
	}

	for _, key := range excludeKeys {
		delete(options, key)
	}

	optionData := make(map[string]*xtype.RawMessage)
	if len(includeKeys) > 0 {
		for _, key := range includeKeys {
			if val, ok := options[key]; ok {
				optionData[key] = val
			}
		}
	} else {
		optionData = options
	}

	return &proto.BizInitResponse{
		Captcha: &proto.BizInitCaptchaData{
			CaptchaId: res.CaptchaID,
			Close:     config.Get().Machine.Geetest.Disable,
		},
		Oae:            config.Get().DeviceProvider.IshumeiProvider.Agency,
		Ccr:            close,
		InteractiveMsg: prompt, //"您确认关闭吗？",
		Options:        optionData,
	}, nil
}

package control

import (
	"context"
	"risk/proto"
	"risk/service/smscheck"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func (*SDKControl) SDKCheckPhone(ctx context.Context, req *proto.SDKCheckPhoneRequest) (*proto.SDKCheckPhoneResponse, error) {
	checker := smscheck.GetSMSChecker(ctx, req.GetNonce())
	return checker.CheckPhoneAndSendSMS(ctx, req.GetPhone(), req.Mock)
}

func (*SDKControl) SDKCheckSMSCode(ctx context.Context, req *proto.SDKCheckCodeRequest) (*xtype.Empty, error) {
	checker := smscheck.GetSMSChecker(ctx, req.GetNonce())
	if err := checker.CheckSMSCode(ctx, req); err != nil {
		return nil, err
	}
	return &xtype.Empty{}, nil
}

func (*SDKControl) SDKPhoneCode(ctx context.Context, req *proto.SDKPhoneCodeRequest) (*xtype.Empty, error) {
	//checker := smscheck.GetSMSChecker(ctx, req.GetNonce())
	//if err := checker.SendCode(ctx); err != nil {
	//	return nil, err
	//}
	return &xtype.Empty{}, nil
}

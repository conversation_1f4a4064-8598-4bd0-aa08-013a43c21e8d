package control

import (
	"context"
	"risk/proto"
	"risk/service"
	"risk/service/conf"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func (c *SDKControl) GCaptcha(ctx context.Context, request *proto.GCaptchaRequest) (*proto.GCaptchaResponse, error) {
	s := service.NewCaptcha(ctx)
	return s.<PERSON>ck(request, ctx)
}

func (c *ServerControl) ReloadGeetestConfig(ctx context.Context, req *proto.ReloadGeetestConfigRequest) (*xtype.Empty, error) {
	xlog.FromContext(ctx).Info("ReloadGeetestConfig")
	ins := conf.GetConfGeetestConfSvrIns()
	if err := ins.LoadAllConfigToRedis(ctx); err != nil {
		return nil, err
	}
	return &xtype.Empty{}, nil
}

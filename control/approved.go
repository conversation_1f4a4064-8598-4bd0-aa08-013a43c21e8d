package control

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"risk/constant"
	"risk/proto"
	"risk/startup"
)

func (s *ServerControl) DeleteApproved(ctx context.Context, req *proto.DeleteApprovedRequest) (*xtype.Empty, error) {
	rdb := startup.GetRedis()

	pipeliner := rdb.Pipeline()

	if req.DOID != "" {
		nidKey := fmt.Sprintf("%s%s", constant.RdbApprovedNid, req.Nid)
		pipeliner.Del(ctx, nidKey)
	}

	if req.DOID != "" {
		deviceKey := fmt.Sprintf("%s%s", constant.RdbApprovedDevice, req.DOID)
		pipeliner.Del(ctx, deviceKey)
	}

	_, err := pipeliner.Exec(ctx)
	if err != nil {
		return nil, ecode.Wrap(ecode.ServerError, err)
	}
	return nil, nil
}

package main

import (
	"risk/service"
	"risk/startup"
	"risk/watcher"
	worker "risk/work"

	"gitlab.papegames.com/fringe/sparrow"

	_ "risk/model"

	"risk/config"
	"risk/server"
)

func main() {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		startup.Startup,
		server.Startup,
		service.LoadCache,
		watcher.Startup,
		service.StartModel,
		service.StartScene,
	).Server(server.GetOutward(), server.GetInner()).
		Worker(worker.Get()).
		Launch()
}

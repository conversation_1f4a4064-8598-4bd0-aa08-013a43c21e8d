package startup

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var rdb *xredis.Client

func GetRedis() *xredis.Client {
	return rdb
}

func redis() error {
	var err error
	rdb, err = xredis.StdConfig().Build()
	if err != nil {
		xlog.S().Error("database.Startup with error: %s", err)
		return err
	}
	rdb.AddHook(xredis.TracingHook())
	return nil
}

package startup

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	conf "risk/config"
)

var biWriter *xkafka.Writer
var riskWriter *xkafka.Writer
var riskReader *xkafka.Reader

func GetBiKafka() *xkafka.Writer {
	return biWriter
}

func GetRiskKafkaWriter() *xkafka.Writer {
	return riskWriter
}

func GetRiskKafkaReader() *xkafka.Reader {
	return riskReader
}

func biKafka() error {
	config := xkafka.RawConfig("sparrow.broker.kafka.bi")
	var err error
	config.WithCompress(xkafka.Gzip.Codec())
	biWriter, err = config.WithBalancer(&xkafka.Hash{}).BuildWriter()
	if err != nil {
		return err
	}
	return nil
}

func riskKafka() error {
	config := xkafka.RawConfig("sparrow.broker.kafka.risk")
	var err error
	config.WithCompress(xkafka.Gzip.Codec())
	riskWriter, err = config.BuildWriter()
	if err != nil {
		return err
	}

	if conf.Get().ConsumerDisable {
		return nil
	}
	config = xkafka.RawConfig("sparrow.broker.kafka.risk")
	riskReader, err = config.BuildReader()
	if err != nil {
		return err
	}
	return nil
}

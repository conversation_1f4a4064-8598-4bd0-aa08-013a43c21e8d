package startup

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var (
	db            *xgorm.DB
	appVerifyDB   *xgorm.DB
	necessoriesDB *xgorm.DB
)

func GetMySql() *xgorm.DB { return db }

func GetNecessoriesDB() *xgorm.DB { return necessoriesDB }

func GetVerifyDB() *xgorm.DB { return appVerifyDB }

func mySql() error {
	var err error
	//gormConf := &xgorm.GormConfig{
	//	Logger: logger.Default.LogMode(logger.Info),
	//	NamingStrategy: schema.NamingStrategy{
	//		SingularTable: true,
	//	},
	//}
	config := xgorm.StdConfig()
	//xlog.Info("mysql config:%+v", config)
	config.WithoutMetrics = true //关掉默认的metrics 默认metrics对分表支持不友好
	db, err = config.WithInterceptor(
		xgorm.TraceInterceptor(),
		xgorm.DebugInterceptor(),
	).Build()
	if err != nil {
		xlog.S().Errorf("database.Startup with error: %s", err)
		return err
	}

	if xdebug.Debug() {
		err := xgorm.AutoMigrate()
		if err != nil {
			xlog.S().Errorf("AutoMigrate with error: %s", err)
			return err
		}
		xlog.S().Debug("AutoMigrate successful")
	}
	if err := verifyDB(); err != nil {
		return err
	}
	if err := initNecessoriesDB(); err != nil {
		return err
	}

	return nil
}

func verifyDB() error {
	var err error
	appVerifyDB, err = createDB("sparrow.database.verifydb")
	if err != nil {
		return err
	}
	return nil
}

func initNecessoriesDB() error {
	var err error
	necessoriesDB, err = createDB("sparrow.database.accessoriesdbreadconf")
	if err != nil {
		return err
	}
	return nil
}

func createDB(configKey string) (*xgorm.DB, error) {
	// tracing
	return xgorm.RawConfig(configKey).WithInterceptor(xgorm.TraceInterceptor()).
		// metrics
		Build()
}

package startup

import (
	"time"

	"gitlab.papegames.com/fringe/pkg/config/clientconfig"
	"gitlab.papegames.com/fringe/pkg/config/reloader"
)

var (
	clientConfig *clientconfig.Config
)

func GetClientConfig() *clientconfig.Config {
	return clientConfig
}

func ClientConfigStartup() error {
	clientConfig = clientconfig.New(GetNecessoriesDB())

	r := reloader.New(&reloader.Options{
		Refresh: 1 * time.Hour,
	})

	err := r.Register(clientConfig)
	if err != nil {
		return err
	}

	if err := r.Init(); err != nil {
		return err
	}

	return nil
}

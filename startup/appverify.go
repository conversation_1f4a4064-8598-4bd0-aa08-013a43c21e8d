package startup

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/pkg/config/appverify"
	"gitlab.papegames.com/fringe/pkg/config/reloader"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var (
	verify *appverify.Verify
)

func InitVerify() error {
	verify = appverify.New(GetVerifyDB())

	r := reloader.New(&reloader.Options{
		Refresh: 5 * time.Minute,
	})

	err := r.Register(verify)
	if err != nil {
		return err
	}
	return r.Init()
}

func AppVerify(path []string, debug bool) xgin.Interceptor {
	if debug {
		return func(next xgin.Handler) xgin.Handler {
			return func(c *gin.Context) (interface{}, ecode.Code) {
				for _, p := range path {
					if strings.HasPrefix(c.Request.URL.Path, p) {
						return next(c)
					}
				}
				ctx := xgin.Context(c)
				logger := xlog.FromContext(ctx)
				// 必要的参数校验
				err := c.Request.ParseForm()
				if err != nil {
					return nil, ecode.BadRequest
				}
				appid := c.Request.FormValue("app_id")
				if len(appid) == 0 {
					logger.Info("app verify, app_id is empty", xlog.Bool("app_verify", false))
					return shared.ReturnData(errors.New("app_id is empty"), debug)
				}
				return next(c)
			}
		}
	}
	return verify.AppVerify(path, debug)
}

func GetVerify() *appverify.Verify {
	return verify
}

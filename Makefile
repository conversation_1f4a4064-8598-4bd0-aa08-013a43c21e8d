Bin="pt-risk"

BuildTag:=$(shell git describe --abbrev=0 --tags --always)
BuildCommit=$(shell git rev-parse ${BuildTag}^0)
BuildTime=$(shell date '+%FT%T%:z')
BuildDate=$(shell date '+%Y%m%d')

BuildFlag="\
-X gitlab.papegames.com/fringe/sparrow/pkg.AppName=${Bin} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildTag=${BuildTag} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildCommit=${BuildCommit} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildTime=${BuildTime} \
"

default: build lint

build: proto
	@go version
	go generate ./...
	go build -o ${Bin} -ldflags ${BuildFlag}

archive: build
	@rm -rf build && mkdir -p build
	@cp -f ${Bin} scripts/restart.sh ./build
	@cd build && tar -zcf ${Bin}-${BuildTag}-${BuildDate}.tar.gz ./*
	@cd build &&  md5sum  ${Bin}-${BuildTag}-${BuildDate}.tar.gz

docker:
	@go version
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
		go build -o ${Bin} -ldflags ${BuildFlag}

lint:
	golangci-lint run

test:
	go test -race ./...

test-coverage:
	go test -race -timeout 30m -cover -coverprofile=./coverage.data ./...
	@go tool cover -html=./coverage.data -o ./coverage.html
	@go tool cover -func=coverage.data

proto:
	@echo "build proto"
	@protoc proto/*.proto --openapi_out=./docs/ --openapi_opt=naming=proto \
		--go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		--http_out=. --http_opt=paths=source_relative,without_hooks=1 \
		--validate_out=. --validate_opt=paths=source_relative
	@protoc proto/*.proto --gotag_out=. --gotag_opt=paths=source_relative

tag:
	@TAG=${BuildTag}; TAG=$${TAG:1}; \
	MAJOR="$${TAG%%.*}"; MAJOR=$${MAJOR:-'0'}; TAG="$${TAG#*.}"; \
	MINOR="$${TAG%%.*}"; MINOR=$${MINOR:-'0'}; TAG="$${TAG#*.}"; \
	PATCH="$${TAG%%.*}"; PATCH=$${PATCH:-'0'}; TAG="$${TAG#*.}"; \
	PATCH=$$(($${PATCH}+1)); TAG="v$${MAJOR}.$${MINOR}.$${PATCH}"; \
	sed -i "s/^v.*/$${TAG}/" .version; \
	git commit -a -m "chore: release $${TAG}"; \
	git push; \
	git tag $${TAG}; \
	git push origin $${TAG}

.PHONY: default build archive lint test test-coverage proto

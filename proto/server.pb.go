// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: proto/server.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      int64                  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientIdRequest) Reset() {
	*x = ClientIdRequest{}
	mi := &file_proto_server_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientIdRequest) ProtoMessage() {}

func (x *ClientIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientIdRequest.ProtoReflect.Descriptor instead.
func (*ClientIdRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{0}
}

func (x *ClientIdRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ClientIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteApprovedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           string                 `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	DOID          string                 `protobuf:"bytes,2,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteApprovedRequest) Reset() {
	*x = DeleteApprovedRequest{}
	mi := &file_proto_server_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteApprovedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteApprovedRequest) ProtoMessage() {}

func (x *DeleteApprovedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteApprovedRequest.ProtoReflect.Descriptor instead.
func (*DeleteApprovedRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteApprovedRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *DeleteApprovedRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type AddBlackListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid           string                 `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Type          int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBlackListRequest) Reset() {
	*x = AddBlackListRequest{}
	mi := &file_proto_server_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBlackListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBlackListRequest) ProtoMessage() {}

func (x *AddBlackListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBlackListRequest.ProtoReflect.Descriptor instead.
func (*AddBlackListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{2}
}

func (x *AddBlackListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AddBlackListRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddBlackListRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AddBlackListRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddBlackListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

type EditBlackListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditBlackListRequest) Reset() {
	*x = EditBlackListRequest{}
	mi := &file_proto_server_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditBlackListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditBlackListRequest) ProtoMessage() {}

func (x *EditBlackListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditBlackListRequest.ProtoReflect.Descriptor instead.
func (*EditBlackListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{3}
}

func (x *EditBlackListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *EditBlackListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditBlackListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

type AddWhiteListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid           string                 `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Type          int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddWhiteListRequest) Reset() {
	*x = AddWhiteListRequest{}
	mi := &file_proto_server_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddWhiteListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddWhiteListRequest) ProtoMessage() {}

func (x *AddWhiteListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddWhiteListRequest.ProtoReflect.Descriptor instead.
func (*AddWhiteListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{4}
}

func (x *AddWhiteListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AddWhiteListRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddWhiteListRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AddWhiteListRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddWhiteListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

type EditWhiteListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditWhiteListRequest) Reset() {
	*x = EditWhiteListRequest{}
	mi := &file_proto_server_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditWhiteListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditWhiteListRequest) ProtoMessage() {}

func (x *EditWhiteListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditWhiteListRequest.ProtoReflect.Descriptor instead.
func (*EditWhiteListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{5}
}

func (x *EditWhiteListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *EditWhiteListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditWhiteListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

type AddDarkListRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	ClientId uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId    string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid      string                 `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Type     int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	// int32 provider = 5 [
	//
	//	(google.api.field_behavior) = REQUIRED
	//
	// ];
	Score         int32 `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	ExpiredAt     int64 `protobuf:"varint,6,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddDarkListRequest) Reset() {
	*x = AddDarkListRequest{}
	mi := &file_proto_server_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddDarkListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDarkListRequest) ProtoMessage() {}

func (x *AddDarkListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDarkListRequest.ProtoReflect.Descriptor instead.
func (*AddDarkListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{6}
}

func (x *AddDarkListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AddDarkListRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddDarkListRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *AddDarkListRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddDarkListRequest) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AddDarkListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

type EditDarkListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	Score         int32                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditDarkListRequest) Reset() {
	*x = EditDarkListRequest{}
	mi := &file_proto_server_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditDarkListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditDarkListRequest) ProtoMessage() {}

func (x *EditDarkListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditDarkListRequest.ProtoReflect.Descriptor instead.
func (*EditDarkListRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{7}
}

func (x *EditDarkListRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *EditDarkListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditDarkListRequest) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *EditDarkListRequest) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type AddConfigAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Frequency     string                 `protobuf:"bytes,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
	IsOpen        int32                  `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Type          int32                  `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	Handle        int32                  `protobuf:"varint,7,opt,name=handle,proto3" json:"handle,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddConfigAppRequest) Reset() {
	*x = AddConfigAppRequest{}
	mi := &file_proto_server_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddConfigAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddConfigAppRequest) ProtoMessage() {}

func (x *AddConfigAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddConfigAppRequest.ProtoReflect.Descriptor instead.
func (*AddConfigAppRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{8}
}

func (x *AddConfigAppRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AddConfigAppRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddConfigAppRequest) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

func (x *AddConfigAppRequest) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *AddConfigAppRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddConfigAppRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddConfigAppRequest) GetHandle() int32 {
	if x != nil {
		return x.Handle
	}
	return 0
}

type EditConfigAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Frequency     string                 `protobuf:"bytes,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
	Handle        int64                  `protobuf:"varint,5,opt,name=handle,proto3" json:"handle,omitempty"`
	IsOpen        int32                  `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditConfigAppRequest) Reset() {
	*x = EditConfigAppRequest{}
	mi := &file_proto_server_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditConfigAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditConfigAppRequest) ProtoMessage() {}

func (x *EditConfigAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditConfigAppRequest.ProtoReflect.Descriptor instead.
func (*EditConfigAppRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{9}
}

func (x *EditConfigAppRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditConfigAppRequest) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

func (x *EditConfigAppRequest) GetHandle() int64 {
	if x != nil {
		return x.Handle
	}
	return 0
}

func (x *EditConfigAppRequest) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

type RiskCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasPass       uint32                 `protobuf:"varint,1,opt,name=has_pass,json=hasPass,proto3" json:"has_pass,omitempty"`
	DeviceId      string                 `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Captcha       *HandlerRequest        `protobuf:"bytes,2,opt,name=captcha,proto3" json:"captcha,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RiskCheckResponse) Reset() {
	*x = RiskCheckResponse{}
	mi := &file_proto_server_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckResponse) ProtoMessage() {}

func (x *RiskCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckResponse.ProtoReflect.Descriptor instead.
func (*RiskCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{10}
}

func (x *RiskCheckResponse) GetHasPass() uint32 {
	if x != nil {
		return x.HasPass
	}
	return 0
}

func (x *RiskCheckResponse) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RiskCheckResponse) GetCaptcha() *HandlerRequest {
	if x != nil {
		return x.Captcha
	}
	return nil
}

type HandlerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nonce         string                 `protobuf:"bytes,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Provider      string                 `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
	Phone         []string               `protobuf:"bytes,3,rep,name=phone,proto3" json:"phone,omitempty"`
	SalePhone     []string               `protobuf:"bytes,4,rep,name=sale_phone,json=salePhone,proto3" json:"sale_phone,omitempty"`
	Email         string                 `protobuf:"bytes,9,opt,name=email,proto3" json:"email,omitempty"`
	SaleEmail     string                 `protobuf:"bytes,10,opt,name=sale_email,json=saleEmail,proto3" json:"sale_email,omitempty"`
	Ttl           int64                  `protobuf:"varint,11,opt,name=ttl,proto3" json:"ttl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandlerRequest) Reset() {
	*x = HandlerRequest{}
	mi := &file_proto_server_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandlerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandlerRequest) ProtoMessage() {}

func (x *HandlerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandlerRequest.ProtoReflect.Descriptor instead.
func (*HandlerRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{11}
}

func (x *HandlerRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *HandlerRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *HandlerRequest) GetPhone() []string {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *HandlerRequest) GetSalePhone() []string {
	if x != nil {
		return x.SalePhone
	}
	return nil
}

func (x *HandlerRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *HandlerRequest) GetSaleEmail() string {
	if x != nil {
		return x.SaleEmail
	}
	return ""
}

func (x *HandlerRequest) GetTtl() int64 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

type Device struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fid           string                 `protobuf:"bytes,1,opt,name=fid,proto3" json:"fid,omitempty"`
	Ishumei       string                 `protobuf:"bytes,2,opt,name=ishumei,proto3" json:"ishumei,omitempty"`
	Aliyun        string                 `protobuf:"bytes,3,opt,name=aliyun,proto3" json:"aliyun,omitempty"`
	Geetest       string                 `protobuf:"bytes,4,opt,name=geetest,proto3" json:"geetest,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_proto_server_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{12}
}

func (x *Device) GetFid() string {
	if x != nil {
		return x.Fid
	}
	return ""
}

func (x *Device) GetIshumei() string {
	if x != nil {
		return x.Ishumei
	}
	return ""
}

func (x *Device) GetAliyun() string {
	if x != nil {
		return x.Aliyun
	}
	return ""
}

func (x *Device) GetGeetest() string {
	if x != nil {
		return x.Geetest
	}
	return ""
}

type AppRiskCheckRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户的nid
	Nid           string `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Ip            string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"` //用户的 IP 地址
	Action        string `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	AppId         string `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`           //应用的 app_id
	Device        string `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`                      //用户的设备
	DOID          string `protobuf:"bytes,6,opt,name=DOID,proto3" json:"DOID,omitempty"`                          //DOID
	ClientId      uint32 `protobuf:"varint,7,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"` //租户 ID
	Lang          string `protobuf:"bytes,8,opt,name=lang,proto3" json:"lang,omitempty"`                          //语言
	RoleId        string `protobuf:"bytes,9,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`        //角色ID
	Scene         string `protobuf:"bytes,10,opt,name=scene,proto3" json:"scene,omitempty"`                       //场景ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppRiskCheckRequest) Reset() {
	*x = AppRiskCheckRequest{}
	mi := &file_proto_server_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppRiskCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRiskCheckRequest) ProtoMessage() {}

func (x *AppRiskCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRiskCheckRequest.ProtoReflect.Descriptor instead.
func (*AppRiskCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{13}
}

func (x *AppRiskCheckRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *AppRiskCheckRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AppRiskCheckRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *AppRiskCheckRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppRiskCheckRequest) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *AppRiskCheckRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *AppRiskCheckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AppRiskCheckRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *AppRiskCheckRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *AppRiskCheckRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type AccountRiskCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           string                 `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	ClientId      uint32                 `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Device        string                 `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	DOID          string                 `protobuf:"bytes,6,opt,name=DOID,proto3" json:"DOID,omitempty"`
	AppId         string                 `protobuf:"bytes,7,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Lang          string                 `protobuf:"bytes,8,opt,name=lang,proto3" json:"lang,omitempty"`
	RoleId        string                 `protobuf:"bytes,9,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Scene         string                 `protobuf:"bytes,10,opt,name=scene,proto3" json:"scene,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountRiskCheckRequest) Reset() {
	*x = AccountRiskCheckRequest{}
	mi := &file_proto_server_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountRiskCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRiskCheckRequest) ProtoMessage() {}

func (x *AccountRiskCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRiskCheckRequest.ProtoReflect.Descriptor instead.
func (*AccountRiskCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{14}
}

func (x *AccountRiskCheckRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AccountRiskCheckRequest) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *AccountRiskCheckRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type DeviceIds struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Shuzilm       string                 `protobuf:"bytes,1,opt,name=shuzilm,proto3" json:"shuzilm,omitempty"`
	Shumei        string                 `protobuf:"bytes,2,opt,name=shumei,proto3" json:"shumei,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceIds) Reset() {
	*x = DeviceIds{}
	mi := &file_proto_server_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceIds) ProtoMessage() {}

func (x *DeviceIds) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceIds.ProtoReflect.Descriptor instead.
func (*DeviceIds) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{15}
}

func (x *DeviceIds) GetShuzilm() string {
	if x != nil {
		return x.Shuzilm
	}
	return ""
}

func (x *DeviceIds) GetShumei() string {
	if x != nil {
		return x.Shumei
	}
	return ""
}

type CaptchaTokenCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	PassToken     string                 `protobuf:"bytes,2,opt,name=pass_token,json=passToken,proto3" json:"pass_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaptchaTokenCheckRequest) Reset() {
	*x = CaptchaTokenCheckRequest{}
	mi := &file_proto_server_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaptchaTokenCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaTokenCheckRequest) ProtoMessage() {}

func (x *CaptchaTokenCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaTokenCheckRequest.ProtoReflect.Descriptor instead.
func (*CaptchaTokenCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{16}
}

func (x *CaptchaTokenCheckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *CaptchaTokenCheckRequest) GetPassToken() string {
	if x != nil {
		return x.PassToken
	}
	return ""
}

type CaptchaTokenCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasPass       bool                   `protobuf:"varint,1,opt,name=has_pass,json=hasPass,proto3" json:"has_pass,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaptchaTokenCheckResponse) Reset() {
	*x = CaptchaTokenCheckResponse{}
	mi := &file_proto_server_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaptchaTokenCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaptchaTokenCheckResponse) ProtoMessage() {}

func (x *CaptchaTokenCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaptchaTokenCheckResponse.ProtoReflect.Descriptor instead.
func (*CaptchaTokenCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{17}
}

func (x *CaptchaTokenCheckResponse) GetHasPass() bool {
	if x != nil {
		return x.HasPass
	}
	return false
}

type DeviceRiskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	ClientId      uint32                 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRiskRequest) Reset() {
	*x = DeviceRiskRequest{}
	mi := &file_proto_server_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRiskRequest) ProtoMessage() {}

func (x *DeviceRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRiskRequest.ProtoReflect.Descriptor instead.
func (*DeviceRiskRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{18}
}

func (x *DeviceRiskRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *DeviceRiskRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type DeviceRiskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Score         uint32                 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRiskResponse) Reset() {
	*x = DeviceRiskResponse{}
	mi := &file_proto_server_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRiskResponse) ProtoMessage() {}

func (x *DeviceRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRiskResponse.ProtoReflect.Descriptor instead.
func (*DeviceRiskResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{19}
}

func (x *DeviceRiskResponse) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type AddResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddResponse) Reset() {
	*x = AddResponse{}
	mi := &file_proto_server_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddResponse) ProtoMessage() {}

func (x *AddResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddResponse.ProtoReflect.Descriptor instead.
func (*AddResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{20}
}

func (x *AddResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type PayRiskCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           string                 `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	ClientId      uint32                 `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Device        string                 `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	ChargeType    uint32                 `protobuf:"varint,6,opt,name=charge_type,json=chargeType,proto3" json:"charge_type,omitempty"`
	Platform      uint32                 `protobuf:"varint,7,opt,name=platform,proto3" json:"platform,omitempty"`
	ProductId     string                 `protobuf:"bytes,8,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	RoleId        string                 `protobuf:"bytes,9,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Region        uint32                 `protobuf:"varint,10,opt,name=region,proto3" json:"region,omitempty"`
	ZoneId        uint32                 `protobuf:"varint,11,opt,name=zone_id,json=zoneId,proto3" json:"zone_id,omitempty"`
	Lang          string                 `protobuf:"bytes,12,opt,name=lang,proto3" json:"lang,omitempty"`
	AppId         string                 `protobuf:"bytes,13,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Scene         string                 `protobuf:"bytes,14,opt,name=scene,proto3" json:"scene,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayRiskCheckRequest) Reset() {
	*x = PayRiskCheckRequest{}
	mi := &file_proto_server_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayRiskCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayRiskCheckRequest) ProtoMessage() {}

func (x *PayRiskCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayRiskCheckRequest.ProtoReflect.Descriptor instead.
func (*PayRiskCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{21}
}

func (x *PayRiskCheckRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *PayRiskCheckRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *PayRiskCheckRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *PayRiskCheckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *PayRiskCheckRequest) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *PayRiskCheckRequest) GetChargeType() uint32 {
	if x != nil {
		return x.ChargeType
	}
	return 0
}

func (x *PayRiskCheckRequest) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *PayRiskCheckRequest) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *PayRiskCheckRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *PayRiskCheckRequest) GetRegion() uint32 {
	if x != nil {
		return x.Region
	}
	return 0
}

func (x *PayRiskCheckRequest) GetZoneId() uint32 {
	if x != nil {
		return x.ZoneId
	}
	return 0
}

func (x *PayRiskCheckRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *PayRiskCheckRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PayRiskCheckRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type SDKSendCometRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clientid      string                 `protobuf:"bytes,1,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Nid           string                 `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"`
	Code          uint64                 `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	Doid          string                 `protobuf:"bytes,4,opt,name=doid,proto3" json:"doid,omitempty"`
	Ip            string                 `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	Account       string                 `protobuf:"bytes,6,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKSendCometRequest) Reset() {
	*x = SDKSendCometRequest{}
	mi := &file_proto_server_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKSendCometRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKSendCometRequest) ProtoMessage() {}

func (x *SDKSendCometRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKSendCometRequest.ProtoReflect.Descriptor instead.
func (*SDKSendCometRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{22}
}

func (x *SDKSendCometRequest) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *SDKSendCometRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *SDKSendCometRequest) GetCode() uint64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SDKSendCometRequest) GetDoid() string {
	if x != nil {
		return x.Doid
	}
	return ""
}

func (x *SDKSendCometRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SDKSendCometRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type ReloadGeetestConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReloadGeetestConfigRequest) Reset() {
	*x = ReloadGeetestConfigRequest{}
	mi := &file_proto_server_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReloadGeetestConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadGeetestConfigRequest) ProtoMessage() {}

func (x *ReloadGeetestConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadGeetestConfigRequest.ProtoReflect.Descriptor instead.
func (*ReloadGeetestConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{23}
}

type DecodeDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeDataRequest) Reset() {
	*x = DecodeDataRequest{}
	mi := &file_proto_server_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeDataRequest) ProtoMessage() {}

func (x *DecodeDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeDataRequest.ProtoReflect.Descriptor instead.
func (*DecodeDataRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{24}
}

func (x *DecodeDataRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DecodeDataRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DecodeDataRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type DecodeDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         *xtype.RawMessage      `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecodeDataResponse) Reset() {
	*x = DecodeDataResponse{}
	mi := &file_proto_server_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeDataResponse) ProtoMessage() {}

func (x *DecodeDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeDataResponse.ProtoReflect.Descriptor instead.
func (*DecodeDataResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{25}
}

func (x *DecodeDataResponse) GetValue() *xtype.RawMessage {
	if x != nil {
		return x.Value
	}
	return nil
}

type CheckAppIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppKey        string                 `protobuf:"bytes,2,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	AesKey        string                 `protobuf:"bytes,3,opt,name=aes_key,json=aesKey,proto3" json:"aes_key,omitempty"`
	AppSecret     string                 `protobuf:"bytes,4,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAppIdRequest) Reset() {
	*x = CheckAppIdRequest{}
	mi := &file_proto_server_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAppIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAppIdRequest) ProtoMessage() {}

func (x *CheckAppIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAppIdRequest.ProtoReflect.Descriptor instead.
func (*CheckAppIdRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{26}
}

func (x *CheckAppIdRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckAppIdRequest) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *CheckAppIdRequest) GetAesKey() string {
	if x != nil {
		return x.AesKey
	}
	return ""
}

func (x *CheckAppIdRequest) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

type SDKDOIDCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKDOIDCheckRequest) Reset() {
	*x = SDKDOIDCheckRequest{}
	mi := &file_proto_server_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKDOIDCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKDOIDCheckRequest) ProtoMessage() {}

func (x *SDKDOIDCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKDOIDCheckRequest.ProtoReflect.Descriptor instead.
func (*SDKDOIDCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{27}
}

func (x *SDKDOIDCheckRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type SDKDOIDCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pass          bool                   `protobuf:"varint,1,opt,name=pass,proto3" json:"pass,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKDOIDCheckResponse) Reset() {
	*x = SDKDOIDCheckResponse{}
	mi := &file_proto_server_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKDOIDCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKDOIDCheckResponse) ProtoMessage() {}

func (x *SDKDOIDCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKDOIDCheckResponse.ProtoReflect.Descriptor instead.
func (*SDKDOIDCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{28}
}

func (x *SDKDOIDCheckResponse) GetPass() bool {
	if x != nil {
		return x.Pass
	}
	return false
}

func (x *SDKDOIDCheckResponse) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type ShumeiDecodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Boxid         string                 `protobuf:"bytes,1,opt,name=boxid,proto3" json:"boxid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDecodeRequest) Reset() {
	*x = ShumeiDecodeRequest{}
	mi := &file_proto_server_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDecodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDecodeRequest) ProtoMessage() {}

func (x *ShumeiDecodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDecodeRequest.ProtoReflect.Descriptor instead.
func (*ShumeiDecodeRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{29}
}

func (x *ShumeiDecodeRequest) GetBoxid() string {
	if x != nil {
		return x.Boxid
	}
	return ""
}

type ShumeiDecodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Boxid         string                 `protobuf:"bytes,2,opt,name=boxid,proto3" json:"boxid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDecodeResponse) Reset() {
	*x = ShumeiDecodeResponse{}
	mi := &file_proto_server_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDecodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDecodeResponse) ProtoMessage() {}

func (x *ShumeiDecodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDecodeResponse.ProtoReflect.Descriptor instead.
func (*ShumeiDecodeResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{30}
}

func (x *ShumeiDecodeResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ShumeiDecodeResponse) GetBoxid() string {
	if x != nil {
		return x.Boxid
	}
	return ""
}

type BindNoticeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           int64                  `protobuf:"varint,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Code          int32                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	ClientId      uint32                 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Doid          string                 `protobuf:"bytes,5,opt,name=doid,proto3" json:"doid,omitempty"`
	Nonce         string                 `protobuf:"bytes,6,opt,name=nonce,proto3" json:"nonce,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindNoticeRequest) Reset() {
	*x = BindNoticeRequest{}
	mi := &file_proto_server_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindNoticeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindNoticeRequest) ProtoMessage() {}

func (x *BindNoticeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindNoticeRequest.ProtoReflect.Descriptor instead.
func (*BindNoticeRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{31}
}

func (x *BindNoticeRequest) GetNid() int64 {
	if x != nil {
		return x.Nid
	}
	return 0
}

func (x *BindNoticeRequest) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BindNoticeRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *BindNoticeRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BindNoticeRequest) GetDoid() string {
	if x != nil {
		return x.Doid
	}
	return ""
}

func (x *BindNoticeRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

type SDKDOIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sdk_DOID      string                 `protobuf:"bytes,1,opt,name=sdk_DOID,json=sdkDOID,proto3" json:"sdk_DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKDOIDRequest) Reset() {
	*x = SDKDOIDRequest{}
	mi := &file_proto_server_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKDOIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKDOIDRequest) ProtoMessage() {}

func (x *SDKDOIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKDOIDRequest.ProtoReflect.Descriptor instead.
func (*SDKDOIDRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{32}
}

func (x *SDKDOIDRequest) GetSdk_DOID() string {
	if x != nil {
		return x.Sdk_DOID
	}
	return ""
}

type SDKDOIDResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKDOIDResponse) Reset() {
	*x = SDKDOIDResponse{}
	mi := &file_proto_server_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKDOIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKDOIDResponse) ProtoMessage() {}

func (x *SDKDOIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKDOIDResponse.ProtoReflect.Descriptor instead.
func (*SDKDOIDResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{33}
}

func (x *SDKDOIDResponse) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type FaceStatusRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户nid
	Nid string `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	// 场景
	Scene string `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	// 人脸识别token
	PassToken string `protobuf:"bytes,3,opt,name=pass_token,json=passToken,proto3" json:"pass_token,omitempty"`
	// 租户 ID
	ClientId      int64  `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DOID          string `protobuf:"bytes,5,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Ip            string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
	IdCard        string `protobuf:"bytes,7,opt,name=id_card,json=idCard,proto3" json:"id_card,omitempty"`
	RealName      string `protobuf:"bytes,8,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceStatusRequest) Reset() {
	*x = FaceStatusRequest{}
	mi := &file_proto_server_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceStatusRequest) ProtoMessage() {}

func (x *FaceStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceStatusRequest.ProtoReflect.Descriptor instead.
func (*FaceStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{34}
}

func (x *FaceStatusRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *FaceStatusRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *FaceStatusRequest) GetPassToken() string {
	if x != nil {
		return x.PassToken
	}
	return ""
}

func (x *FaceStatusRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FaceStatusRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *FaceStatusRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FaceStatusRequest) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *FaceStatusRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

type FaceStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        uint32                 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceStatusResponse) Reset() {
	*x = FaceStatusResponse{}
	mi := &file_proto_server_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceStatusResponse) ProtoMessage() {}

func (x *FaceStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceStatusResponse.ProtoReflect.Descriptor instead.
func (*FaceStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{35}
}

func (x *FaceStatusResponse) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type FaceCodeForWebRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户nid
	Nid string `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	// 用户token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// 真实姓名
	RealName string `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	// 身份证
	IdCard string `protobuf:"bytes,4,opt,name=id_card,json=idCard,proto3" json:"id_card,omitempty"`
	// 手机号码
	Phone string `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`
	// 场景，固定值:account_real_info
	Scene string `protobuf:"bytes,5,opt,name=scene,proto3" json:"scene,omitempty"`
	// 厂商，固定值:aliyun
	Vendor string `protobuf:"bytes,6,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// 扩展信息，json格式, meta_info必传，{"meta_info":{}}
	Extra string `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`
	// 租户 ID
	ClientId int64 `protobuf:"varint,8,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// DOID值
	DOID string `protobuf:"bytes,9,opt,name=DOID,proto3" json:"DOID,omitempty"`
	// 认证结束后回跳页面的链接地址
	ReturnUrl string `protobuf:"bytes,10,opt,name=return_url,json=returnUrl,proto3" json:"return_url,omitempty"`
	// appId
	AppId string `protobuf:"bytes,12,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// ip
	Ip            string `protobuf:"bytes,13,opt,name=ip,proto3" json:"ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceCodeForWebRequest) Reset() {
	*x = FaceCodeForWebRequest{}
	mi := &file_proto_server_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceCodeForWebRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceCodeForWebRequest) ProtoMessage() {}

func (x *FaceCodeForWebRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceCodeForWebRequest.ProtoReflect.Descriptor instead.
func (*FaceCodeForWebRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{36}
}

func (x *FaceCodeForWebRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FaceCodeForWebRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *FaceCodeForWebRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type FaceCodeForWebResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 厂商
	Vendor string `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor"`
	// 请求的唯一code
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	// 认证结束后回跳页面的链接地址
	ReturnUrl string `protobuf:"bytes,3,opt,name=return_url,json=returnUrl,proto3" json:"return_url"`
	// 厂商的扩展数据
	Service       *FaceServiceData `protobuf:"bytes,4,opt,name=service,proto3" json:"service"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceCodeForWebResponse) Reset() {
	*x = FaceCodeForWebResponse{}
	mi := &file_proto_server_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceCodeForWebResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceCodeForWebResponse) ProtoMessage() {}

func (x *FaceCodeForWebResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceCodeForWebResponse.ProtoReflect.Descriptor instead.
func (*FaceCodeForWebResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{37}
}

func (x *FaceCodeForWebResponse) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FaceCodeForWebResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FaceCodeForWebResponse) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *FaceCodeForWebResponse) GetService() *FaceServiceData {
	if x != nil {
		return x.Service
	}
	return nil
}

type QrCodeGenerateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户 ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 用户nid
	Nid string `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"`
	// 用户token
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// 真实姓名
	RealName string `protobuf:"bytes,4,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	// 身份证
	IdCard        string `protobuf:"bytes,5,opt,name=id_card,json=idCard,proto3" json:"id_card,omitempty"`
	ReturnUrl     string `protobuf:"bytes,6,opt,name=return_url,json=returnUrl,proto3" json:"return_url,omitempty"`
	Scene         string `protobuf:"bytes,7,opt,name=scene,proto3" json:"scene,omitempty"`
	Vendor        string `protobuf:"bytes,8,opt,name=vendor,proto3" json:"vendor,omitempty"`
	Doid          string `protobuf:"bytes,9,opt,name=doid,proto3" json:"doid,omitempty"`
	AppId         string `protobuf:"bytes,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrCodeGenerateRequest) Reset() {
	*x = QrCodeGenerateRequest{}
	mi := &file_proto_server_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCodeGenerateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCodeGenerateRequest) ProtoMessage() {}

func (x *QrCodeGenerateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCodeGenerateRequest.ProtoReflect.Descriptor instead.
func (*QrCodeGenerateRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{38}
}

func (x *QrCodeGenerateRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *QrCodeGenerateRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetDoid() string {
	if x != nil {
		return x.Doid
	}
	return ""
}

func (x *QrCodeGenerateRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type QrCodeGenerateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 二维码id
	QrcodeId string `protobuf:"bytes,1,opt,name=qrcode_id,json=qrcodeId,proto3" json:"qrcode_id"`
	// 过期时间
	ExpiredAt string `protobuf:"bytes,2,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at"`
	// 今日剩余次数
	RemainingAttempts string `protobuf:"bytes,3,opt,name=remaining_attempts,json=remainingAttempts,proto3" json:"remaining_attempts"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *QrCodeGenerateResponse) Reset() {
	*x = QrCodeGenerateResponse{}
	mi := &file_proto_server_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCodeGenerateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCodeGenerateResponse) ProtoMessage() {}

func (x *QrCodeGenerateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCodeGenerateResponse.ProtoReflect.Descriptor instead.
func (*QrCodeGenerateResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{39}
}

func (x *QrCodeGenerateResponse) GetQrcodeId() string {
	if x != nil {
		return x.QrcodeId
	}
	return ""
}

func (x *QrCodeGenerateResponse) GetExpiredAt() string {
	if x != nil {
		return x.ExpiredAt
	}
	return ""
}

func (x *QrCodeGenerateResponse) GetRemainingAttempts() string {
	if x != nil {
		return x.RemainingAttempts
	}
	return ""
}

type QrCodeScanRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 二维码id
	QrcodeId      string `protobuf:"bytes,1,opt,name=qrcode_id,json=qrcodeId,proto3" json:"qrcode_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrCodeScanRequest) Reset() {
	*x = QrCodeScanRequest{}
	mi := &file_proto_server_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCodeScanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCodeScanRequest) ProtoMessage() {}

func (x *QrCodeScanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCodeScanRequest.ProtoReflect.Descriptor instead.
func (*QrCodeScanRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{40}
}

func (x *QrCodeScanRequest) GetQrcodeId() string {
	if x != nil {
		return x.QrcodeId
	}
	return ""
}

type QrCodeScanResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户 ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id"`
	// 用户nid
	Nid string `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid"`
	// 用户token
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token"`
	// 真实姓名
	RealName string `protobuf:"bytes,4,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 身份证
	IdCard        string `protobuf:"bytes,5,opt,name=id_card,json=idCard,proto3" json:"id_card"`
	ReturnUrl     string `protobuf:"bytes,6,opt,name=return_url,json=returnUrl,proto3" json:"return_url"`
	Scene         string `protobuf:"bytes,7,opt,name=scene,proto3" json:"scene"`
	Vendor        string `protobuf:"bytes,8,opt,name=vendor,proto3" json:"vendor"`
	Doid          string `protobuf:"bytes,9,opt,name=doid,proto3" json:"doid"`
	AppId         string `protobuf:"bytes,10,opt,name=app_id,json=appId,proto3" json:"app_id"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrCodeScanResponse) Reset() {
	*x = QrCodeScanResponse{}
	mi := &file_proto_server_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCodeScanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCodeScanResponse) ProtoMessage() {}

func (x *QrCodeScanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCodeScanResponse.ProtoReflect.Descriptor instead.
func (*QrCodeScanResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{41}
}

func (x *QrCodeScanResponse) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *QrCodeScanResponse) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *QrCodeScanResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *QrCodeScanResponse) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *QrCodeScanResponse) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *QrCodeScanResponse) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *QrCodeScanResponse) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *QrCodeScanResponse) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *QrCodeScanResponse) GetDoid() string {
	if x != nil {
		return x.Doid
	}
	return ""
}

func (x *QrCodeScanResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type FaceStatusQueryByQrCodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 二维码id
	QrcodeId      string `protobuf:"bytes,1,opt,name=qrcode_id,json=qrcodeId,proto3" json:"qrcode_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceStatusQueryByQrCodeRequest) Reset() {
	*x = FaceStatusQueryByQrCodeRequest{}
	mi := &file_proto_server_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceStatusQueryByQrCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceStatusQueryByQrCodeRequest) ProtoMessage() {}

func (x *FaceStatusQueryByQrCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceStatusQueryByQrCodeRequest.ProtoReflect.Descriptor instead.
func (*FaceStatusQueryByQrCodeRequest) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{42}
}

func (x *FaceStatusQueryByQrCodeRequest) GetQrcodeId() string {
	if x != nil {
		return x.QrcodeId
	}
	return ""
}

type FaceStatusQueryByQrCodeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 人脸认证状态 0:未认证 1：认证通过  2: 认证不通过
	Status        uint32 `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceStatusQueryByQrCodeResponse) Reset() {
	*x = FaceStatusQueryByQrCodeResponse{}
	mi := &file_proto_server_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceStatusQueryByQrCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceStatusQueryByQrCodeResponse) ProtoMessage() {}

func (x *FaceStatusQueryByQrCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_server_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceStatusQueryByQrCodeResponse.ProtoReflect.Descriptor instead.
func (*FaceStatusQueryByQrCodeResponse) Descriptor() ([]byte, []int) {
	return file_proto_server_proto_rawDescGZIP(), []int{43}
}

func (x *FaceStatusQueryByQrCodeResponse) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_proto_server_proto protoreflect.FileDescriptor

const file_proto_server_proto_rawDesc = "" +
	"\n" +
	"\x12proto/server.proto\x12\x16papegames.sparrow.risk\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bopenapiv3/annotations.proto\x1a\x1apapegames/type/empty.proto\x1a\x13tagger/tagger.proto\x1a papegames/type/raw_message.proto\x1a\x10proto/base.proto\"b\n" +
	"\x0fClientIdRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12 \n" +
	"\x02id\x18\x02 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x02id\"=\n" +
	"\x15DeleteApprovedRequest\x12\x10\n" +
	"\x03nid\x18\x01 \x01(\tR\x03nid\x12\x12\n" +
	"\x04DOID\x18\x02 \x01(\tR\x04DOID\"\xb2\x01\n" +
	"\x13AddBlackListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x16\n" +
	"\x03uid\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x03uid\x12\x18\n" +
	"\x04type\x18\x04 \x01(\x05B\x04\xe2A\x01\x02R\x04type\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x05 \x01(\x03R\texpiredAt\"\x8c\x01\n" +
	"\x14EditBlackListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12 \n" +
	"\x02id\x18\x02 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x02id\x12#\n" +
	"\n" +
	"expired_at\x18\x03 \x01(\x03B\x04\xe2A\x01\x02R\texpiredAt\"\xb2\x01\n" +
	"\x13AddWhiteListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x16\n" +
	"\x03uid\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x03uid\x12\x18\n" +
	"\x04type\x18\x04 \x01(\x05B\x04\xe2A\x01\x02R\x04type\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x05 \x01(\x03R\texpiredAt\"\x8c\x01\n" +
	"\x14EditWhiteListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12 \n" +
	"\x02id\x18\x02 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x02id\x12#\n" +
	"\n" +
	"expired_at\x18\x03 \x01(\x03B\x04\xe2A\x01\x02R\texpiredAt\"\xcd\x01\n" +
	"\x12AddDarkListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x16\n" +
	"\x03uid\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x03uid\x12\x18\n" +
	"\x04type\x18\x04 \x01(\x05B\x04\xe2A\x01\x02R\x04type\x12\x1a\n" +
	"\x05score\x18\x05 \x01(\x05B\x04\xe2A\x01\x02R\x05score\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x06 \x01(\x03R\texpiredAt\"\xa7\x01\n" +
	"\x13EditDarkListRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12 \n" +
	"\x02id\x18\x02 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x02id\x12#\n" +
	"\n" +
	"expired_at\x18\x03 \x01(\x03B\x04\xe2A\x01\x02R\texpiredAt\x12\x1a\n" +
	"\x05score\x18\x04 \x01(\x05B\x04\xe2A\x01\x02R\x05score\"\xce\x01\n" +
	"\x13AddConfigAppRequest\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\rR\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\"\n" +
	"\tfrequency\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\tfrequency\x12\x17\n" +
	"\ais_open\x18\x04 \x01(\x05R\x06isOpen\x12\x12\n" +
	"\x04type\x18\x05 \x01(\x05R\x04type\x12\x14\n" +
	"\x05title\x18\x06 \x01(\tR\x05title\x12\x16\n" +
	"\x06handle\x18\a \x01(\x05R\x06handle\"\x8d\x01\n" +
	"\x14EditConfigAppRequest\x12 \n" +
	"\x02id\x18\x02 \x01(\x03B\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x02id\x12\x1c\n" +
	"\tfrequency\x18\x03 \x01(\tR\tfrequency\x12\x16\n" +
	"\x06handle\x18\x05 \x01(\x03R\x06handle\x12\x1d\n" +
	"\ais_open\x18\x04 \x01(\x05B\x04\xe2A\x01\x02R\x06isOpen\"\x8d\x01\n" +
	"\x11RiskCheckResponse\x12\x19\n" +
	"\bhas_pass\x18\x01 \x01(\rR\ahasPass\x12\x1b\n" +
	"\tdevice_id\x18\x03 \x01(\tR\bdeviceId\x12@\n" +
	"\acaptcha\x18\x02 \x01(\v2&.papegames.sparrow.risk.HandlerRequestR\acaptcha\"\xbe\x01\n" +
	"\x0eHandlerRequest\x12\x14\n" +
	"\x05nonce\x18\x01 \x01(\tR\x05nonce\x12\x1a\n" +
	"\bprovider\x18\x02 \x01(\tR\bprovider\x12\x14\n" +
	"\x05phone\x18\x03 \x03(\tR\x05phone\x12\x1d\n" +
	"\n" +
	"sale_phone\x18\x04 \x03(\tR\tsalePhone\x12\x14\n" +
	"\x05email\x18\t \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"sale_email\x18\n" +
	" \x01(\tR\tsaleEmail\x12\x10\n" +
	"\x03ttl\x18\v \x01(\x03R\x03ttl\"f\n" +
	"\x06Device\x12\x10\n" +
	"\x03fid\x18\x01 \x01(\tR\x03fid\x12\x18\n" +
	"\aishumei\x18\x02 \x01(\tR\aishumei\x12\x16\n" +
	"\x06aliyun\x18\x03 \x01(\tR\x06aliyun\x12\x18\n" +
	"\ageetest\x18\x04 \x01(\tR\ageetest\"\xa0\x02\n" +
	"\x13AppRiskCheckRequest\x12\"\n" +
	"\x03nid\x18\x01 \x01(\tB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x03nid\x12\x1e\n" +
	"\x02ip\x18\x02 \x01(\tB\x0e\xe2A\x01\x02\xbaG\a\x8a\x01\x04ipv4R\x02ip\x12\x1c\n" +
	"\x06action\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x06action\x12\x1b\n" +
	"\x06app_id\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x16\n" +
	"\x06device\x18\x05 \x01(\tR\x06device\x12\x12\n" +
	"\x04DOID\x18\x06 \x01(\tR\x04DOID\x12\x1b\n" +
	"\tclient_id\x18\a \x01(\rR\bclientId\x12\x12\n" +
	"\x04lang\x18\b \x01(\tR\x04lang\x12\x17\n" +
	"\arole_id\x18\t \x01(\tR\x06roleId\x12\x14\n" +
	"\x05scene\x18\n" +
	" \x01(\tR\x05scene\"\xb0\x02\n" +
	"\x17AccountRiskCheckRequest\x12\"\n" +
	"\x03nid\x18\x01 \x01(\tB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x03nid\x12\x1e\n" +
	"\x02ip\x18\x02 \x01(\tB\x0e\xe2A\x01\x02\xbaG\a\x8a\x01\x04ipv4R\x02ip\x12\x1c\n" +
	"\x06action\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x06action\x12-\n" +
	"\tclient_id\x18\x04 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x16\n" +
	"\x06device\x18\x05 \x01(\tR\x06device\x12\x12\n" +
	"\x04DOID\x18\x06 \x01(\tR\x04DOID\x12\x15\n" +
	"\x06app_id\x18\a \x01(\tR\x05appId\x12\x12\n" +
	"\x04lang\x18\b \x01(\tR\x04lang\x12\x17\n" +
	"\arole_id\x18\t \x01(\tR\x06roleId\x12\x14\n" +
	"\x05scene\x18\n" +
	" \x01(\tR\x05scene\"=\n" +
	"\tDeviceIds\x12\x18\n" +
	"\ashuzilm\x18\x01 \x01(\tR\ashuzilm\x12\x16\n" +
	"\x06shumei\x18\x02 \x01(\tR\x06shumei\"n\n" +
	"\x18CaptchaTokenCheckRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12#\n" +
	"\n" +
	"pass_token\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\tpassToken\"6\n" +
	"\x19CaptchaTokenCheckResponse\x12\x19\n" +
	"\bhas_pass\x18\x01 \x01(\bR\ahasPass\"\\\n" +
	"\x11DeviceRiskRequest\x12\x18\n" +
	"\x04DOID\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x04DOID\x12-\n" +
	"\tclient_id\x18\x02 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\"*\n" +
	"\x12DeviceRiskResponse\x12\x14\n" +
	"\x05score\x18\x01 \x01(\rR\x05score\"\x1d\n" +
	"\vAddResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\"\x95\x03\n" +
	"\x13PayRiskCheckRequest\x12\"\n" +
	"\x03nid\x18\x01 \x01(\tB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00\xf0?R\x03nid\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12\x1c\n" +
	"\x06action\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x06action\x12-\n" +
	"\tclient_id\x18\x04 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x16\n" +
	"\x06device\x18\x05 \x01(\tR\x06device\x12\x1f\n" +
	"\vcharge_type\x18\x06 \x01(\rR\n" +
	"chargeType\x12\x1a\n" +
	"\bplatform\x18\a \x01(\rR\bplatform\x12\x1d\n" +
	"\n" +
	"product_id\x18\b \x01(\tR\tproductId\x12\x17\n" +
	"\arole_id\x18\t \x01(\tR\x06roleId\x12\x16\n" +
	"\x06region\x18\n" +
	" \x01(\rR\x06region\x12\x17\n" +
	"\azone_id\x18\v \x01(\rR\x06zoneId\x12\x12\n" +
	"\x04lang\x18\f \x01(\tR\x04lang\x12\x15\n" +
	"\x06app_id\x18\r \x01(\tR\x05appId\x12\x14\n" +
	"\x05scene\x18\x0e \x01(\tR\x05scene\"\xa1\x01\n" +
	"\x13SDKSendCometRequest\x12 \n" +
	"\bclientid\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\bclientid\x12\x16\n" +
	"\x03nid\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x12\n" +
	"\x04code\x18\x03 \x01(\x04R\x04code\x12\x12\n" +
	"\x04doid\x18\x04 \x01(\tR\x04doid\x12\x0e\n" +
	"\x02ip\x18\x05 \x01(\tR\x02ip\x12\x18\n" +
	"\aaccount\x18\x06 \x01(\tR\aaccount\"\x1c\n" +
	"\x1aReloadGeetestConfigRequest\"\\\n" +
	"\x11DecodeDataRequest\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"F\n" +
	"\x12DecodeDataResponse\x120\n" +
	"\x05value\x18\x01 \x01(\v2\x1a.papegames.type.RawMessageR\x05value\"{\n" +
	"\x11CheckAppIdRequest\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12\x17\n" +
	"\aapp_key\x18\x02 \x01(\tR\x06appKey\x12\x17\n" +
	"\aaes_key\x18\x03 \x01(\tR\x06aesKey\x12\x1d\n" +
	"\n" +
	"app_secret\x18\x04 \x01(\tR\tappSecret\"/\n" +
	"\x13SDKDOIDCheckRequest\x12\x18\n" +
	"\x04DOID\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x04DOID\"G\n" +
	"\x14SDKDOIDCheckResponse\x12\x12\n" +
	"\x04pass\x18\x01 \x01(\bR\x04pass\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"+\n" +
	"\x13ShumeiDecodeRequest\x12\x14\n" +
	"\x05boxid\x18\x01 \x01(\tR\x05boxid\"B\n" +
	"\x14ShumeiDecodeResponse\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12\x14\n" +
	"\x05boxid\x18\x02 \x01(\tR\x05boxid\"\x97\x01\n" +
	"\x11BindNoticeRequest\x12\x10\n" +
	"\x03nid\x18\x01 \x01(\x03R\x03nid\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x05R\x04code\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\rR\bclientId\x12\x15\n" +
	"\x06app_id\x18\x04 \x01(\tR\x05appId\x12\x12\n" +
	"\x04doid\x18\x05 \x01(\tR\x04doid\x12\x14\n" +
	"\x05nonce\x18\x06 \x01(\tR\x05nonce\"+\n" +
	"\x0eSDKDOIDRequest\x12\x19\n" +
	"\bsdk_DOID\x18\x01 \x01(\tR\asdkDOID\",\n" +
	"\x0fSDKDOIDResponse\x12\x12\n" +
	"\x04DOID\x18\x01 \x01(\tR\x04DOID:\x05ȧ\x86\a\x01\"\xd7\x01\n" +
	"\x11FaceStatusRequest\x12\x10\n" +
	"\x03nid\x18\x01 \x01(\tR\x03nid\x12\x14\n" +
	"\x05scene\x18\x02 \x01(\tR\x05scene\x12\x1d\n" +
	"\n" +
	"pass_token\x18\x03 \x01(\tR\tpassToken\x12!\n" +
	"\tclient_id\x18\x04 \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x12\n" +
	"\x04DOID\x18\x05 \x01(\tR\x04DOID\x12\x0e\n" +
	"\x02ip\x18\x06 \x01(\tR\x02ip\x12\x17\n" +
	"\aid_card\x18\a \x01(\tR\x06idCard\x12\x1b\n" +
	"\treal_name\x18\b \x01(\tR\brealName\"3\n" +
	"\x12FaceStatusResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\rR\x06status:\x05ȧ\x86\a\x01\"\xea\x02\n" +
	"\x15FaceCodeForWebRequest\x12\x10\n" +
	"\x03nid\x18\x01 \x01(\tR\x03nid\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12\x1b\n" +
	"\treal_name\x18\x03 \x01(\tR\brealName\x12\x17\n" +
	"\aid_card\x18\x04 \x01(\tR\x06idCard\x12\x14\n" +
	"\x05phone\x18\v \x01(\tR\x05phone\x12\x1a\n" +
	"\x05scene\x18\x05 \x01(\tB\x04\xe2A\x01\x02R\x05scene\x12\x1c\n" +
	"\x06vendor\x18\x06 \x01(\tB\x04\xe2A\x01\x02R\x06vendor\x12\x14\n" +
	"\x05extra\x18\a \x01(\tR\x05extra\x12!\n" +
	"\tclient_id\x18\b \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x18\n" +
	"\x04DOID\x18\t \x01(\tB\x04\xe2A\x01\x02R\x04DOID\x12#\n" +
	"\n" +
	"return_url\x18\n" +
	" \x01(\tB\x04\xe2A\x01\x02R\treturnUrl\x12\x1b\n" +
	"\x06app_id\x18\f \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x0e\n" +
	"\x02ip\x18\r \x01(\tR\x02ip\"\xad\x01\n" +
	"\x16FaceCodeForWebResponse\x12\x16\n" +
	"\x06vendor\x18\x01 \x01(\tR\x06vendor\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x1d\n" +
	"\n" +
	"return_url\x18\x03 \x01(\tR\treturnUrl\x12A\n" +
	"\aservice\x18\x04 \x01(\v2'.papegames.sparrow.risk.FaceServiceDataR\aservice:\x05ȧ\x86\a\x01\"\xb4\x02\n" +
	"\x15QrCodeGenerateRequest\x12!\n" +
	"\tclient_id\x18\x01 \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x16\n" +
	"\x03nid\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x1a\n" +
	"\x05token\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x05token\x12\x1b\n" +
	"\treal_name\x18\x04 \x01(\tR\brealName\x12\x17\n" +
	"\aid_card\x18\x05 \x01(\tR\x06idCard\x12\x1d\n" +
	"\n" +
	"return_url\x18\x06 \x01(\tR\treturnUrl\x12\x1a\n" +
	"\x05scene\x18\a \x01(\tB\x04\xe2A\x01\x02R\x05scene\x12\x1c\n" +
	"\x06vendor\x18\b \x01(\tB\x04\xe2A\x01\x02R\x06vendor\x12\x18\n" +
	"\x04doid\x18\t \x01(\tB\x04\xe2A\x01\x02R\x04doid\x12\x1b\n" +
	"\x06app_id\x18\n" +
	" \x01(\tB\x04\xe2A\x01\x02R\x05appId\"\x8a\x01\n" +
	"\x16QrCodeGenerateResponse\x12\x1b\n" +
	"\tqrcode_id\x18\x01 \x01(\tR\bqrcodeId\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x02 \x01(\tR\texpiredAt\x12-\n" +
	"\x12remaining_attempts\x18\x03 \x01(\tR\x11remainingAttempts:\x05ȧ\x86\a\x01\"6\n" +
	"\x11QrCodeScanRequest\x12!\n" +
	"\tqrcode_id\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\bqrcodeId\"\xb8\x02\n" +
	"\x12QrCodeScanResponse\x12!\n" +
	"\tclient_id\x18\x01 \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x16\n" +
	"\x03nid\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x1a\n" +
	"\x05token\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x05token\x12\x1b\n" +
	"\treal_name\x18\x04 \x01(\tR\brealName\x12\x17\n" +
	"\aid_card\x18\x05 \x01(\tR\x06idCard\x12\x1d\n" +
	"\n" +
	"return_url\x18\x06 \x01(\tR\treturnUrl\x12\x1a\n" +
	"\x05scene\x18\a \x01(\tB\x04\xe2A\x01\x02R\x05scene\x12\x1c\n" +
	"\x06vendor\x18\b \x01(\tB\x04\xe2A\x01\x02R\x06vendor\x12\x18\n" +
	"\x04doid\x18\t \x01(\tB\x04\xe2A\x01\x02R\x04doid\x12\x1b\n" +
	"\x06app_id\x18\n" +
	" \x01(\tB\x04\xe2A\x01\x02R\x05appId:\x05ȧ\x86\a\x01\"C\n" +
	"\x1eFaceStatusQueryByQrCodeRequest\x12!\n" +
	"\tqrcode_id\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\bqrcodeId\"@\n" +
	"\x1fFaceStatusQueryByQrCodeResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\rR\x06status:\x05ȧ\x86\a\x012\x917\n" +
	"\x11RiskServerService\x12\xf3\x01\n" +
	"\x11CaptchaTokenCheck\x120.papegames.sparrow.risk.CaptchaTokenCheckRequest\x1a1.papegames.sparrow.risk.CaptchaTokenCheckResponse\"y\xdaA\tform-data\xbaGC*\x12极验验证接口j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/risk/captcha/token/check\x12\xd4\x01\n" +
	"\fAppCheckRisk\x12+.papegames.sparrow.risk.AppRiskCheckRequest\x1a&.papegames.sparrow.risk.HandlerRequest\"o\xdaA\tform-data\xbaGC*\x12业务风控检测j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/risk/app/check\x12\xe0\x01\n" +
	"\x10AccountCheckRisk\x12/.papegames.sparrow.risk.AccountRiskCheckRequest\x1a&.papegames.sparrow.risk.HandlerRequest\"s\xdaA\tform-data\xbaGC*\x12账号风控检测j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/risk/account/check\x12\xd8\x01\n" +
	"\fPayCheckRisk\x12+.papegames.sparrow.risk.PayRiskCheckRequest\x1a&.papegames.sparrow.risk.HandlerRequest\"s\xdaA\tform-data\xbaGC*\x12支付风控检测j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/risk/payment/check\x12\xdc\x01\n" +
	"\n" +
	"DeviceRisk\x12).papegames.sparrow.risk.DeviceRiskRequest\x1a*.papegames.sparrow.risk.DeviceRiskResponse\"w\xdaA\tform-data\xbaGI*\x18设备风险信息查询j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/device/risk\x12\xd4\x01\n" +
	"\fAddBlackList\x12+.papegames.sparrow.risk.AddBlackListRequest\x1a#.papegames.sparrow.risk.AddResponse\"r\xdaA\tform-data\xbaG:*\f加白名单j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/black_list/create\x12\xd2\x01\n" +
	"\rEditBlackList\x12,.papegames.sparrow.risk.EditBlackListRequest\x1a\x15.papegames.type.Empty\"|\xdaA\tform-data\xbaGD*\x16黑名单列表-修改j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/black_list/update\x12\xcf\x01\n" +
	"\x0fDeleteBlackList\x12'.papegames.sparrow.risk.ClientIdRequest\x1a\x15.papegames.type.Empty\"|\xdaA\tform-data\xbaGD*\x16黑名单列表-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/black_list/delete\x12\xde\x01\n" +
	"\fAddWhiteList\x12+.papegames.sparrow.risk.AddWhiteListRequest\x1a#.papegames.sparrow.risk.AddResponse\"|\xdaA\tform-data\xbaGD*\x16黑名单列表-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/white_list/create\x12\xd2\x01\n" +
	"\rEditWhiteList\x12,.papegames.sparrow.risk.EditWhiteListRequest\x1a\x15.papegames.type.Empty\"|\xdaA\tform-data\xbaGD*\x16白名单列表-修改j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/white_list/update\x12\xcf\x01\n" +
	"\x0fDeleteWhiteList\x12'.papegames.sparrow.risk.ClientIdRequest\x1a\x15.papegames.type.Empty\"|\xdaA\tform-data\xbaGD*\x16白名单列表-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/white_list/delete\x12\xd8\x01\n" +
	"\vAddDarkList\x12*.papegames.sparrow.risk.AddDarkListRequest\x1a#.papegames.sparrow.risk.AddResponse\"x\xdaA\tform-data\xbaGA*\x13黑产列表-添加j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/risk/tob/dark_list/create\x12\xcc\x01\n" +
	"\fEditDarkList\x12+.papegames.sparrow.risk.EditDarkListRequest\x1a\x15.papegames.type.Empty\"x\xdaA\tform-data\xbaGA*\x13黑产列表-修改j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/risk/tob/dark_list/update\x12\xca\x01\n" +
	"\x0eDeleteDarkList\x12'.papegames.sparrow.risk.ClientIdRequest\x1a\x15.papegames.type.Empty\"x\xdaA\tform-data\xbaGA*\x13黑产列表-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/risk/tob/dark_list/delete\x12\xdb\x01\n" +
	"\fAddConfigApp\x12+.papegames.sparrow.risk.AddConfigAppRequest\x1a#.papegames.sparrow.risk.AddResponse\"y\xdaA\tform-data\xbaGA*\x13风控配置-添加j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/config_app/create\x12\xcf\x01\n" +
	"\rEditConfigApp\x12,.papegames.sparrow.risk.EditConfigAppRequest\x1a\x15.papegames.type.Empty\"y\xdaA\tform-data\xbaGA*\x13风控配置-修改j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/config_app/update\x12\xcc\x01\n" +
	"\x0fDeleteConfigApp\x12'.papegames.sparrow.risk.ClientIdRequest\x1a\x15.papegames.type.Empty\"y\xdaA\tform-data\xbaGA*\x13风控配置-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/risk/tob/config_app/delete\x12\xc1\x01\n" +
	"\fSDKSendComet\x12+.papegames.sparrow.risk.SDKSendCometRequest\x1a\x15.papegames.type.Empty\"m\xdaA\tform-data\xbaG@*\x12发送长链消息j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/risk/comet/send\x12\xe5\x01\n" +
	"\x13ReloadGeetestConfig\x122.papegames.sparrow.risk.ReloadGeetestConfigRequest\x1a\x15.papegames.type.Empty\"\x82\x01\xdaA\tform-data\xbaGF*\x18重新加载极验配置j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/risk/tob/geetest_config/reload\x12\xd6\x01\n" +
	"\x0eDeleteApproved\x12-.papegames.sparrow.risk.DeleteApprovedRequest\x1a\x15.papegames.type.Empty\"~\xdaA\tform-data\xbaGH*\x1a允许账号/设备-删除j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/risk/tob/approved/delete\x12\xd1\x01\n" +
	"\n" +
	"DecodeData\x12).papegames.sparrow.risk.DecodeDataRequest\x1a*.papegames.sparrow.risk.DecodeDataResponse\"l\xdaA\tform-data\xbaGC*\x12加密数据解密j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/risk/decode\x12\xd1\x01\n" +
	"\n" +
	"encodeData\x12).papegames.sparrow.risk.DecodeDataRequest\x1a*.papegames.sparrow.risk.DecodeDataResponse\"l\xdaA\tform-data\xbaGC*\x12加密数据解密j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/risk/encode\x12\xd6\x01\n" +
	"\n" +
	"checkAppId\x12).papegames.sparrow.risk.CheckAppIdRequest\x1a).papegames.sparrow.risk.CheckAppIdRequest\"r\xdaA\tform-data\xbaGC*\x12加密数据解密j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/v1/risk/app_id/check\x12\xd5\x01\n" +
	"\fSDKDOIDCheck\x12+.papegames.sparrow.risk.SDKDOIDCheckRequest\x1a,.papegames.sparrow.risk.SDKDOIDCheckResponse\"j\xdaA\tform-data\xbaG?*\x0eSDK DOID验证j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/risk/ds/check\x12\xdd\x01\n" +
	"\fShumeiDecode\x12+.papegames.sparrow.risk.ShumeiDecodeRequest\x1a,.papegames.sparrow.risk.ShumeiDecodeResponse\"r\xdaA\tform-data\xbaGB*\x11数美boxId解密j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/risk/shumei/decode\x12\xc4\x01\n" +
	"\n" +
	"BindNotice\x12).papegames.sparrow.risk.BindNoticeRequest\x1a\x15.papegames.type.Empty\"t\xdaA\tform-data\xbaGF*\x15绑定手机号通知j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/bind/notice\x12\xd4\x01\n" +
	"\rSDKDOIDToDOID\x12&.papegames.sparrow.risk.SDKDOIDRequest\x1a'.papegames.sparrow.risk.SDKDOIDResponse\"r\xdaA\tform-data\xbaG@*\x0fSDK DOID转DOIDj\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/v1/risk/device/sdk_doid\x12\xc2\x01\n" +
	"\n" +
	"FaceStatus\x12).papegames.sparrow.risk.FaceStatusRequest\x1a*.papegames.sparrow.risk.FaceStatusResponse\"]\xdaA\tform-data\xbaG/j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/face/status\x12\xf2\x01\n" +
	"\x0eFaceCodeForWeb\x12-.papegames.sparrow.risk.FaceCodeForWebRequest\x1a..papegames.sparrow.risk.FaceCodeForWebResponse\"\x80\x01\xdaA\tform-data\xbaGP*\"人脸验证初始化（Server端)j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/risk/face/code/web\x12\xf5\x01\n" +
	"\x0eQrCodeGenerate\x12-.papegames.sparrow.risk.QrCodeGenerateRequest\x1a..papegames.sparrow.risk.QrCodeGenerateResponse\"\x83\x01\xdaA\tform-data\xbaGL*\x1b二维码初始化（PC端)j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/risk/face/qrcode/generate\x12\xd8\x01\n" +
	"\n" +
	"QrCodeScan\x12).papegames.sparrow.risk.QrCodeScanRequest\x1a*.papegames.sparrow.risk.QrCodeScanResponse\"s\xdaA\tform-data\xbaG@*\x0f二维码扫码j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/risk/face/qrcode/scan\x12\x95\x02\n" +
	"\x17FaceStatusQueryByQrCode\x126.papegames.sparrow.risk.FaceStatusQueryByQrCodeRequest\x1a7.papegames.sparrow.risk.FaceStatusQueryByQrCodeResponse\"\x88\x01\xdaA\tform-data\xbaGS*\"使用code查询人脸认证状态j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/risk/face/qrcode/status\x1a\x15\xcaA\x12risk.papegames.comB;\n" +
	"\x1acom.papegames.sparrow.riskB\tRiskProtoP\x01Z\x10risk/proto;protob\x06proto3"

var (
	file_proto_server_proto_rawDescOnce sync.Once
	file_proto_server_proto_rawDescData []byte
)

func file_proto_server_proto_rawDescGZIP() []byte {
	file_proto_server_proto_rawDescOnce.Do(func() {
		file_proto_server_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_server_proto_rawDesc), len(file_proto_server_proto_rawDesc)))
	})
	return file_proto_server_proto_rawDescData
}

var file_proto_server_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_proto_server_proto_goTypes = []any{
	(*ClientIdRequest)(nil),                 // 0: papegames.sparrow.risk.ClientIdRequest
	(*DeleteApprovedRequest)(nil),           // 1: papegames.sparrow.risk.DeleteApprovedRequest
	(*AddBlackListRequest)(nil),             // 2: papegames.sparrow.risk.AddBlackListRequest
	(*EditBlackListRequest)(nil),            // 3: papegames.sparrow.risk.EditBlackListRequest
	(*AddWhiteListRequest)(nil),             // 4: papegames.sparrow.risk.AddWhiteListRequest
	(*EditWhiteListRequest)(nil),            // 5: papegames.sparrow.risk.EditWhiteListRequest
	(*AddDarkListRequest)(nil),              // 6: papegames.sparrow.risk.AddDarkListRequest
	(*EditDarkListRequest)(nil),             // 7: papegames.sparrow.risk.EditDarkListRequest
	(*AddConfigAppRequest)(nil),             // 8: papegames.sparrow.risk.AddConfigAppRequest
	(*EditConfigAppRequest)(nil),            // 9: papegames.sparrow.risk.EditConfigAppRequest
	(*RiskCheckResponse)(nil),               // 10: papegames.sparrow.risk.RiskCheckResponse
	(*HandlerRequest)(nil),                  // 11: papegames.sparrow.risk.HandlerRequest
	(*Device)(nil),                          // 12: papegames.sparrow.risk.Device
	(*AppRiskCheckRequest)(nil),             // 13: papegames.sparrow.risk.AppRiskCheckRequest
	(*AccountRiskCheckRequest)(nil),         // 14: papegames.sparrow.risk.AccountRiskCheckRequest
	(*DeviceIds)(nil),                       // 15: papegames.sparrow.risk.DeviceIds
	(*CaptchaTokenCheckRequest)(nil),        // 16: papegames.sparrow.risk.CaptchaTokenCheckRequest
	(*CaptchaTokenCheckResponse)(nil),       // 17: papegames.sparrow.risk.CaptchaTokenCheckResponse
	(*DeviceRiskRequest)(nil),               // 18: papegames.sparrow.risk.DeviceRiskRequest
	(*DeviceRiskResponse)(nil),              // 19: papegames.sparrow.risk.DeviceRiskResponse
	(*AddResponse)(nil),                     // 20: papegames.sparrow.risk.AddResponse
	(*PayRiskCheckRequest)(nil),             // 21: papegames.sparrow.risk.PayRiskCheckRequest
	(*SDKSendCometRequest)(nil),             // 22: papegames.sparrow.risk.SDKSendCometRequest
	(*ReloadGeetestConfigRequest)(nil),      // 23: papegames.sparrow.risk.ReloadGeetestConfigRequest
	(*DecodeDataRequest)(nil),               // 24: papegames.sparrow.risk.DecodeDataRequest
	(*DecodeDataResponse)(nil),              // 25: papegames.sparrow.risk.DecodeDataResponse
	(*CheckAppIdRequest)(nil),               // 26: papegames.sparrow.risk.CheckAppIdRequest
	(*SDKDOIDCheckRequest)(nil),             // 27: papegames.sparrow.risk.SDKDOIDCheckRequest
	(*SDKDOIDCheckResponse)(nil),            // 28: papegames.sparrow.risk.SDKDOIDCheckResponse
	(*ShumeiDecodeRequest)(nil),             // 29: papegames.sparrow.risk.ShumeiDecodeRequest
	(*ShumeiDecodeResponse)(nil),            // 30: papegames.sparrow.risk.ShumeiDecodeResponse
	(*BindNoticeRequest)(nil),               // 31: papegames.sparrow.risk.BindNoticeRequest
	(*SDKDOIDRequest)(nil),                  // 32: papegames.sparrow.risk.SDKDOIDRequest
	(*SDKDOIDResponse)(nil),                 // 33: papegames.sparrow.risk.SDKDOIDResponse
	(*FaceStatusRequest)(nil),               // 34: papegames.sparrow.risk.FaceStatusRequest
	(*FaceStatusResponse)(nil),              // 35: papegames.sparrow.risk.FaceStatusResponse
	(*FaceCodeForWebRequest)(nil),           // 36: papegames.sparrow.risk.FaceCodeForWebRequest
	(*FaceCodeForWebResponse)(nil),          // 37: papegames.sparrow.risk.FaceCodeForWebResponse
	(*QrCodeGenerateRequest)(nil),           // 38: papegames.sparrow.risk.QrCodeGenerateRequest
	(*QrCodeGenerateResponse)(nil),          // 39: papegames.sparrow.risk.QrCodeGenerateResponse
	(*QrCodeScanRequest)(nil),               // 40: papegames.sparrow.risk.QrCodeScanRequest
	(*QrCodeScanResponse)(nil),              // 41: papegames.sparrow.risk.QrCodeScanResponse
	(*FaceStatusQueryByQrCodeRequest)(nil),  // 42: papegames.sparrow.risk.FaceStatusQueryByQrCodeRequest
	(*FaceStatusQueryByQrCodeResponse)(nil), // 43: papegames.sparrow.risk.FaceStatusQueryByQrCodeResponse
	(*xtype.RawMessage)(nil),                // 44: papegames.type.RawMessage
	(*FaceServiceData)(nil),                 // 45: papegames.sparrow.risk.FaceServiceData
	(*xtype.Empty)(nil),                     // 46: papegames.type.Empty
}
var file_proto_server_proto_depIdxs = []int32{
	11, // 0: papegames.sparrow.risk.RiskCheckResponse.captcha:type_name -> papegames.sparrow.risk.HandlerRequest
	44, // 1: papegames.sparrow.risk.DecodeDataResponse.value:type_name -> papegames.type.RawMessage
	45, // 2: papegames.sparrow.risk.FaceCodeForWebResponse.service:type_name -> papegames.sparrow.risk.FaceServiceData
	16, // 3: papegames.sparrow.risk.RiskServerService.CaptchaTokenCheck:input_type -> papegames.sparrow.risk.CaptchaTokenCheckRequest
	13, // 4: papegames.sparrow.risk.RiskServerService.AppCheckRisk:input_type -> papegames.sparrow.risk.AppRiskCheckRequest
	14, // 5: papegames.sparrow.risk.RiskServerService.AccountCheckRisk:input_type -> papegames.sparrow.risk.AccountRiskCheckRequest
	21, // 6: papegames.sparrow.risk.RiskServerService.PayCheckRisk:input_type -> papegames.sparrow.risk.PayRiskCheckRequest
	18, // 7: papegames.sparrow.risk.RiskServerService.DeviceRisk:input_type -> papegames.sparrow.risk.DeviceRiskRequest
	2,  // 8: papegames.sparrow.risk.RiskServerService.AddBlackList:input_type -> papegames.sparrow.risk.AddBlackListRequest
	3,  // 9: papegames.sparrow.risk.RiskServerService.EditBlackList:input_type -> papegames.sparrow.risk.EditBlackListRequest
	0,  // 10: papegames.sparrow.risk.RiskServerService.DeleteBlackList:input_type -> papegames.sparrow.risk.ClientIdRequest
	4,  // 11: papegames.sparrow.risk.RiskServerService.AddWhiteList:input_type -> papegames.sparrow.risk.AddWhiteListRequest
	5,  // 12: papegames.sparrow.risk.RiskServerService.EditWhiteList:input_type -> papegames.sparrow.risk.EditWhiteListRequest
	0,  // 13: papegames.sparrow.risk.RiskServerService.DeleteWhiteList:input_type -> papegames.sparrow.risk.ClientIdRequest
	6,  // 14: papegames.sparrow.risk.RiskServerService.AddDarkList:input_type -> papegames.sparrow.risk.AddDarkListRequest
	7,  // 15: papegames.sparrow.risk.RiskServerService.EditDarkList:input_type -> papegames.sparrow.risk.EditDarkListRequest
	0,  // 16: papegames.sparrow.risk.RiskServerService.DeleteDarkList:input_type -> papegames.sparrow.risk.ClientIdRequest
	8,  // 17: papegames.sparrow.risk.RiskServerService.AddConfigApp:input_type -> papegames.sparrow.risk.AddConfigAppRequest
	9,  // 18: papegames.sparrow.risk.RiskServerService.EditConfigApp:input_type -> papegames.sparrow.risk.EditConfigAppRequest
	0,  // 19: papegames.sparrow.risk.RiskServerService.DeleteConfigApp:input_type -> papegames.sparrow.risk.ClientIdRequest
	22, // 20: papegames.sparrow.risk.RiskServerService.SDKSendComet:input_type -> papegames.sparrow.risk.SDKSendCometRequest
	23, // 21: papegames.sparrow.risk.RiskServerService.ReloadGeetestConfig:input_type -> papegames.sparrow.risk.ReloadGeetestConfigRequest
	1,  // 22: papegames.sparrow.risk.RiskServerService.DeleteApproved:input_type -> papegames.sparrow.risk.DeleteApprovedRequest
	24, // 23: papegames.sparrow.risk.RiskServerService.DecodeData:input_type -> papegames.sparrow.risk.DecodeDataRequest
	24, // 24: papegames.sparrow.risk.RiskServerService.encodeData:input_type -> papegames.sparrow.risk.DecodeDataRequest
	26, // 25: papegames.sparrow.risk.RiskServerService.checkAppId:input_type -> papegames.sparrow.risk.CheckAppIdRequest
	27, // 26: papegames.sparrow.risk.RiskServerService.SDKDOIDCheck:input_type -> papegames.sparrow.risk.SDKDOIDCheckRequest
	29, // 27: papegames.sparrow.risk.RiskServerService.ShumeiDecode:input_type -> papegames.sparrow.risk.ShumeiDecodeRequest
	31, // 28: papegames.sparrow.risk.RiskServerService.BindNotice:input_type -> papegames.sparrow.risk.BindNoticeRequest
	32, // 29: papegames.sparrow.risk.RiskServerService.SDKDOIDToDOID:input_type -> papegames.sparrow.risk.SDKDOIDRequest
	34, // 30: papegames.sparrow.risk.RiskServerService.FaceStatus:input_type -> papegames.sparrow.risk.FaceStatusRequest
	36, // 31: papegames.sparrow.risk.RiskServerService.FaceCodeForWeb:input_type -> papegames.sparrow.risk.FaceCodeForWebRequest
	38, // 32: papegames.sparrow.risk.RiskServerService.QrCodeGenerate:input_type -> papegames.sparrow.risk.QrCodeGenerateRequest
	40, // 33: papegames.sparrow.risk.RiskServerService.QrCodeScan:input_type -> papegames.sparrow.risk.QrCodeScanRequest
	42, // 34: papegames.sparrow.risk.RiskServerService.FaceStatusQueryByQrCode:input_type -> papegames.sparrow.risk.FaceStatusQueryByQrCodeRequest
	17, // 35: papegames.sparrow.risk.RiskServerService.CaptchaTokenCheck:output_type -> papegames.sparrow.risk.CaptchaTokenCheckResponse
	11, // 36: papegames.sparrow.risk.RiskServerService.AppCheckRisk:output_type -> papegames.sparrow.risk.HandlerRequest
	11, // 37: papegames.sparrow.risk.RiskServerService.AccountCheckRisk:output_type -> papegames.sparrow.risk.HandlerRequest
	11, // 38: papegames.sparrow.risk.RiskServerService.PayCheckRisk:output_type -> papegames.sparrow.risk.HandlerRequest
	19, // 39: papegames.sparrow.risk.RiskServerService.DeviceRisk:output_type -> papegames.sparrow.risk.DeviceRiskResponse
	20, // 40: papegames.sparrow.risk.RiskServerService.AddBlackList:output_type -> papegames.sparrow.risk.AddResponse
	46, // 41: papegames.sparrow.risk.RiskServerService.EditBlackList:output_type -> papegames.type.Empty
	46, // 42: papegames.sparrow.risk.RiskServerService.DeleteBlackList:output_type -> papegames.type.Empty
	20, // 43: papegames.sparrow.risk.RiskServerService.AddWhiteList:output_type -> papegames.sparrow.risk.AddResponse
	46, // 44: papegames.sparrow.risk.RiskServerService.EditWhiteList:output_type -> papegames.type.Empty
	46, // 45: papegames.sparrow.risk.RiskServerService.DeleteWhiteList:output_type -> papegames.type.Empty
	20, // 46: papegames.sparrow.risk.RiskServerService.AddDarkList:output_type -> papegames.sparrow.risk.AddResponse
	46, // 47: papegames.sparrow.risk.RiskServerService.EditDarkList:output_type -> papegames.type.Empty
	46, // 48: papegames.sparrow.risk.RiskServerService.DeleteDarkList:output_type -> papegames.type.Empty
	20, // 49: papegames.sparrow.risk.RiskServerService.AddConfigApp:output_type -> papegames.sparrow.risk.AddResponse
	46, // 50: papegames.sparrow.risk.RiskServerService.EditConfigApp:output_type -> papegames.type.Empty
	46, // 51: papegames.sparrow.risk.RiskServerService.DeleteConfigApp:output_type -> papegames.type.Empty
	46, // 52: papegames.sparrow.risk.RiskServerService.SDKSendComet:output_type -> papegames.type.Empty
	46, // 53: papegames.sparrow.risk.RiskServerService.ReloadGeetestConfig:output_type -> papegames.type.Empty
	46, // 54: papegames.sparrow.risk.RiskServerService.DeleteApproved:output_type -> papegames.type.Empty
	25, // 55: papegames.sparrow.risk.RiskServerService.DecodeData:output_type -> papegames.sparrow.risk.DecodeDataResponse
	25, // 56: papegames.sparrow.risk.RiskServerService.encodeData:output_type -> papegames.sparrow.risk.DecodeDataResponse
	26, // 57: papegames.sparrow.risk.RiskServerService.checkAppId:output_type -> papegames.sparrow.risk.CheckAppIdRequest
	28, // 58: papegames.sparrow.risk.RiskServerService.SDKDOIDCheck:output_type -> papegames.sparrow.risk.SDKDOIDCheckResponse
	30, // 59: papegames.sparrow.risk.RiskServerService.ShumeiDecode:output_type -> papegames.sparrow.risk.ShumeiDecodeResponse
	46, // 60: papegames.sparrow.risk.RiskServerService.BindNotice:output_type -> papegames.type.Empty
	33, // 61: papegames.sparrow.risk.RiskServerService.SDKDOIDToDOID:output_type -> papegames.sparrow.risk.SDKDOIDResponse
	35, // 62: papegames.sparrow.risk.RiskServerService.FaceStatus:output_type -> papegames.sparrow.risk.FaceStatusResponse
	37, // 63: papegames.sparrow.risk.RiskServerService.FaceCodeForWeb:output_type -> papegames.sparrow.risk.FaceCodeForWebResponse
	39, // 64: papegames.sparrow.risk.RiskServerService.QrCodeGenerate:output_type -> papegames.sparrow.risk.QrCodeGenerateResponse
	41, // 65: papegames.sparrow.risk.RiskServerService.QrCodeScan:output_type -> papegames.sparrow.risk.QrCodeScanResponse
	43, // 66: papegames.sparrow.risk.RiskServerService.FaceStatusQueryByQrCode:output_type -> papegames.sparrow.risk.FaceStatusQueryByQrCodeResponse
	35, // [35:67] is the sub-list for method output_type
	3,  // [3:35] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_proto_server_proto_init() }
func file_proto_server_proto_init() {
	if File_proto_server_proto != nil {
		return
	}
	file_proto_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_server_proto_rawDesc), len(file_proto_server_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_server_proto_goTypes,
		DependencyIndexes: file_proto_server_proto_depIdxs,
		MessageInfos:      file_proto_server_proto_msgTypes,
	}.Build()
	File_proto_server_proto = out.File
	file_proto_server_proto_goTypes = nil
	file_proto_server_proto_depIdxs = nil
}

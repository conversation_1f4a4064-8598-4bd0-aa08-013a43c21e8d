// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/server.proto

package proto

import (
	xstring "gitlab.papegames.com/fringe/sparrow/pkg/xstring"
)

func (x *ClientIdRequest) Validate() error {
	if x.GetClientId() < 100 {
		return ClientIdRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if x.GetId() < 1 {
		return ClientIdRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *DeleteApprovedRequest) Validate() error {
	return nil
}

func (x *AddBlackListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return AddBlackListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if len(x.GetAppId()) == 0 {
		return AddBlackListRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetUid()) == 0 {
		return AddBlackListRequestValidationError{
			field:   "Uid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *EditBlackListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return EditBlackListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if x.GetId() < 1 {
		return EditBlackListRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *AddWhiteListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return AddWhiteListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if len(x.GetAppId()) == 0 {
		return AddWhiteListRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetUid()) == 0 {
		return AddWhiteListRequestValidationError{
			field:   "Uid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *EditWhiteListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return EditWhiteListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if x.GetId() < 1 {
		return EditWhiteListRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *AddDarkListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return AddDarkListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if len(x.GetAppId()) == 0 {
		return AddDarkListRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetUid()) == 0 {
		return AddDarkListRequestValidationError{
			field:   "Uid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *EditDarkListRequest) Validate() error {
	if x.GetClientId() < 100 {
		return EditDarkListRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if x.GetId() < 1 {
		return EditDarkListRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *AddConfigAppRequest) Validate() error {
	if len(x.GetAppId()) == 0 {
		return AddConfigAppRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetFrequency()) == 0 {
		return AddConfigAppRequestValidationError{
			field:   "Frequency",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *EditConfigAppRequest) Validate() error {
	if x.GetId() < 1 {
		return EditConfigAppRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *RiskCheckResponse) Validate() error {
	if v, ok := interface{}(x.GetCaptcha()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskCheckResponseValidationError{
				field:   "Captcha",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *HandlerRequest) Validate() error {
	return nil
}

func (x *Device) Validate() error {
	return nil
}

func (x *AppRiskCheckRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return AppRiskCheckRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetIp()) == 0 {
		return AppRiskCheckRequestValidationError{
			field:   "Ip",
			reason:  "required",
			message: "value is required",
		}
	}
	if !xstring.IsIPv4(x.GetIp()) {
		return AppRiskCheckRequestValidationError{
			field:   "Ip",
			reason:  "pattern",
			message: "value must be a valid ipv4",
		}
	}
	if len(x.GetAction()) == 0 {
		return AppRiskCheckRequestValidationError{
			field:   "Action",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return AppRiskCheckRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *AccountRiskCheckRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return AccountRiskCheckRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetIp()) == 0 {
		return AccountRiskCheckRequestValidationError{
			field:   "Ip",
			reason:  "required",
			message: "value is required",
		}
	}
	if !xstring.IsIPv4(x.GetIp()) {
		return AccountRiskCheckRequestValidationError{
			field:   "Ip",
			reason:  "pattern",
			message: "value must be a valid ipv4",
		}
	}
	if len(x.GetAction()) == 0 {
		return AccountRiskCheckRequestValidationError{
			field:   "Action",
			reason:  "required",
			message: "value is required",
		}
	}
	if x.GetClientId() < 100 {
		return AccountRiskCheckRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	return nil
}

func (x *DeviceIds) Validate() error {
	return nil
}

func (x *CaptchaTokenCheckRequest) Validate() error {
	if x.GetClientId() < 100 {
		return CaptchaTokenCheckRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if len(x.GetPassToken()) == 0 {
		return CaptchaTokenCheckRequestValidationError{
			field:   "PassToken",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *CaptchaTokenCheckResponse) Validate() error {
	return nil
}

func (x *DeviceRiskRequest) Validate() error {
	if len(x.GetDOID()) == 0 {
		return DeviceRiskRequestValidationError{
			field:   "DOID",
			reason:  "required",
			message: "value is required",
		}
	}
	if x.GetClientId() < 100 {
		return DeviceRiskRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	return nil
}

func (x *DeviceRiskResponse) Validate() error {
	return nil
}

func (x *AddResponse) Validate() error {
	return nil
}

func (x *PayRiskCheckRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return PayRiskCheckRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAction()) == 0 {
		return PayRiskCheckRequestValidationError{
			field:   "Action",
			reason:  "required",
			message: "value is required",
		}
	}
	if x.GetClientId() < 100 {
		return PayRiskCheckRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	return nil
}

func (x *SDKSendCometRequest) Validate() error {
	if len(x.GetClientid()) == 0 {
		return SDKSendCometRequestValidationError{
			field:   "Clientid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetNid()) == 0 {
		return SDKSendCometRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *ReloadGeetestConfigRequest) Validate() error {
	return nil
}

func (x *DecodeDataRequest) Validate() error {
	return nil
}

func (x *DecodeDataResponse) Validate() error {
	return nil
}

func (x *CheckAppIdRequest) Validate() error {
	return nil
}

func (x *SDKDOIDCheckRequest) Validate() error {
	if len(x.GetDOID()) == 0 {
		return SDKDOIDCheckRequestValidationError{
			field:   "DOID",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SDKDOIDCheckResponse) Validate() error {
	return nil
}

func (x *ShumeiDecodeRequest) Validate() error {
	return nil
}

func (x *ShumeiDecodeResponse) Validate() error {
	return nil
}

func (x *BindNoticeRequest) Validate() error {
	return nil
}

func (x *SDKDOIDRequest) Validate() error {
	return nil
}

func (x *SDKDOIDResponse) Validate() error {
	return nil
}

func (x *FaceStatusRequest) Validate() error {
	return nil
}

func (x *FaceStatusResponse) Validate() error {
	return nil
}

func (x *FaceCodeForWebRequest) Validate() error {
	if len(x.GetScene()) == 0 {
		return FaceCodeForWebRequestValidationError{
			field:   "Scene",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetVendor()) == 0 {
		return FaceCodeForWebRequestValidationError{
			field:   "Vendor",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetDOID()) == 0 {
		return FaceCodeForWebRequestValidationError{
			field:   "DOID",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetReturnUrl()) == 0 {
		return FaceCodeForWebRequestValidationError{
			field:   "ReturnUrl",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return FaceCodeForWebRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *FaceCodeForWebResponse) Validate() error {
	if v, ok := interface{}(x.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceCodeForWebResponseValidationError{
				field:   "Service",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *QrCodeGenerateRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetToken()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "Token",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetScene()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "Scene",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetVendor()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "Vendor",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetDoid()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "Doid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return QrCodeGenerateRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *QrCodeGenerateResponse) Validate() error {
	return nil
}

func (x *QrCodeScanRequest) Validate() error {
	if len(x.GetQrcodeId()) == 0 {
		return QrCodeScanRequestValidationError{
			field:   "QrcodeId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *QrCodeScanResponse) Validate() error {
	if len(x.GetNid()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetToken()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "Token",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetScene()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "Scene",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetVendor()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "Vendor",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetDoid()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "Doid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return QrCodeScanResponseValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *FaceStatusQueryByQrCodeRequest) Validate() error {
	if len(x.GetQrcodeId()) == 0 {
		return FaceStatusQueryByQrCodeRequestValidationError{
			field:   "QrcodeId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *FaceStatusQueryByQrCodeResponse) Validate() error {
	return nil
}

type ClientIdRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ClientIdRequestValidationError) Field() string { return e.field }

func (e ClientIdRequestValidationError) Reason() string { return e.reason }

func (e ClientIdRequestValidationError) Message() string { return e.message }

func (e ClientIdRequestValidationError) Cause() error { return e.cause }

func (e ClientIdRequestValidationError) ErrorName() string { return "ClientIdRequestValidationError" }

func (e ClientIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ClientIdRequest." + e.field + ": " + e.message + cause
}

type DeleteApprovedRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeleteApprovedRequestValidationError) Field() string { return e.field }

func (e DeleteApprovedRequestValidationError) Reason() string { return e.reason }

func (e DeleteApprovedRequestValidationError) Message() string { return e.message }

func (e DeleteApprovedRequestValidationError) Cause() error { return e.cause }

func (e DeleteApprovedRequestValidationError) ErrorName() string {
	return "DeleteApprovedRequestValidationError"
}

func (e DeleteApprovedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeleteApprovedRequest." + e.field + ": " + e.message + cause
}

type AddBlackListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AddBlackListRequestValidationError) Field() string { return e.field }

func (e AddBlackListRequestValidationError) Reason() string { return e.reason }

func (e AddBlackListRequestValidationError) Message() string { return e.message }

func (e AddBlackListRequestValidationError) Cause() error { return e.cause }

func (e AddBlackListRequestValidationError) ErrorName() string {
	return "AddBlackListRequestValidationError"
}

func (e AddBlackListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AddBlackListRequest." + e.field + ": " + e.message + cause
}

type EditBlackListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e EditBlackListRequestValidationError) Field() string { return e.field }

func (e EditBlackListRequestValidationError) Reason() string { return e.reason }

func (e EditBlackListRequestValidationError) Message() string { return e.message }

func (e EditBlackListRequestValidationError) Cause() error { return e.cause }

func (e EditBlackListRequestValidationError) ErrorName() string {
	return "EditBlackListRequestValidationError"
}

func (e EditBlackListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid EditBlackListRequest." + e.field + ": " + e.message + cause
}

type AddWhiteListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AddWhiteListRequestValidationError) Field() string { return e.field }

func (e AddWhiteListRequestValidationError) Reason() string { return e.reason }

func (e AddWhiteListRequestValidationError) Message() string { return e.message }

func (e AddWhiteListRequestValidationError) Cause() error { return e.cause }

func (e AddWhiteListRequestValidationError) ErrorName() string {
	return "AddWhiteListRequestValidationError"
}

func (e AddWhiteListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AddWhiteListRequest." + e.field + ": " + e.message + cause
}

type EditWhiteListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e EditWhiteListRequestValidationError) Field() string { return e.field }

func (e EditWhiteListRequestValidationError) Reason() string { return e.reason }

func (e EditWhiteListRequestValidationError) Message() string { return e.message }

func (e EditWhiteListRequestValidationError) Cause() error { return e.cause }

func (e EditWhiteListRequestValidationError) ErrorName() string {
	return "EditWhiteListRequestValidationError"
}

func (e EditWhiteListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid EditWhiteListRequest." + e.field + ": " + e.message + cause
}

type AddDarkListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AddDarkListRequestValidationError) Field() string { return e.field }

func (e AddDarkListRequestValidationError) Reason() string { return e.reason }

func (e AddDarkListRequestValidationError) Message() string { return e.message }

func (e AddDarkListRequestValidationError) Cause() error { return e.cause }

func (e AddDarkListRequestValidationError) ErrorName() string {
	return "AddDarkListRequestValidationError"
}

func (e AddDarkListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AddDarkListRequest." + e.field + ": " + e.message + cause
}

type EditDarkListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e EditDarkListRequestValidationError) Field() string { return e.field }

func (e EditDarkListRequestValidationError) Reason() string { return e.reason }

func (e EditDarkListRequestValidationError) Message() string { return e.message }

func (e EditDarkListRequestValidationError) Cause() error { return e.cause }

func (e EditDarkListRequestValidationError) ErrorName() string {
	return "EditDarkListRequestValidationError"
}

func (e EditDarkListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid EditDarkListRequest." + e.field + ": " + e.message + cause
}

type AddConfigAppRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AddConfigAppRequestValidationError) Field() string { return e.field }

func (e AddConfigAppRequestValidationError) Reason() string { return e.reason }

func (e AddConfigAppRequestValidationError) Message() string { return e.message }

func (e AddConfigAppRequestValidationError) Cause() error { return e.cause }

func (e AddConfigAppRequestValidationError) ErrorName() string {
	return "AddConfigAppRequestValidationError"
}

func (e AddConfigAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AddConfigAppRequest." + e.field + ": " + e.message + cause
}

type EditConfigAppRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e EditConfigAppRequestValidationError) Field() string { return e.field }

func (e EditConfigAppRequestValidationError) Reason() string { return e.reason }

func (e EditConfigAppRequestValidationError) Message() string { return e.message }

func (e EditConfigAppRequestValidationError) Cause() error { return e.cause }

func (e EditConfigAppRequestValidationError) ErrorName() string {
	return "EditConfigAppRequestValidationError"
}

func (e EditConfigAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid EditConfigAppRequest." + e.field + ": " + e.message + cause
}

type RiskCheckResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RiskCheckResponseValidationError) Field() string { return e.field }

func (e RiskCheckResponseValidationError) Reason() string { return e.reason }

func (e RiskCheckResponseValidationError) Message() string { return e.message }

func (e RiskCheckResponseValidationError) Cause() error { return e.cause }

func (e RiskCheckResponseValidationError) ErrorName() string {
	return "RiskCheckResponseValidationError"
}

func (e RiskCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RiskCheckResponse." + e.field + ": " + e.message + cause
}

type HandlerRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e HandlerRequestValidationError) Field() string { return e.field }

func (e HandlerRequestValidationError) Reason() string { return e.reason }

func (e HandlerRequestValidationError) Message() string { return e.message }

func (e HandlerRequestValidationError) Cause() error { return e.cause }

func (e HandlerRequestValidationError) ErrorName() string { return "HandlerRequestValidationError" }

func (e HandlerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid HandlerRequest." + e.field + ": " + e.message + cause
}

type DeviceValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceValidationError) Field() string { return e.field }

func (e DeviceValidationError) Reason() string { return e.reason }

func (e DeviceValidationError) Message() string { return e.message }

func (e DeviceValidationError) Cause() error { return e.cause }

func (e DeviceValidationError) ErrorName() string { return "DeviceValidationError" }

func (e DeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Device." + e.field + ": " + e.message + cause
}

type AppRiskCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AppRiskCheckRequestValidationError) Field() string { return e.field }

func (e AppRiskCheckRequestValidationError) Reason() string { return e.reason }

func (e AppRiskCheckRequestValidationError) Message() string { return e.message }

func (e AppRiskCheckRequestValidationError) Cause() error { return e.cause }

func (e AppRiskCheckRequestValidationError) ErrorName() string {
	return "AppRiskCheckRequestValidationError"
}

func (e AppRiskCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AppRiskCheckRequest." + e.field + ": " + e.message + cause
}

type AccountRiskCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AccountRiskCheckRequestValidationError) Field() string { return e.field }

func (e AccountRiskCheckRequestValidationError) Reason() string { return e.reason }

func (e AccountRiskCheckRequestValidationError) Message() string { return e.message }

func (e AccountRiskCheckRequestValidationError) Cause() error { return e.cause }

func (e AccountRiskCheckRequestValidationError) ErrorName() string {
	return "AccountRiskCheckRequestValidationError"
}

func (e AccountRiskCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AccountRiskCheckRequest." + e.field + ": " + e.message + cause
}

type DeviceIdsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceIdsValidationError) Field() string { return e.field }

func (e DeviceIdsValidationError) Reason() string { return e.reason }

func (e DeviceIdsValidationError) Message() string { return e.message }

func (e DeviceIdsValidationError) Cause() error { return e.cause }

func (e DeviceIdsValidationError) ErrorName() string { return "DeviceIdsValidationError" }

func (e DeviceIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceIds." + e.field + ": " + e.message + cause
}

type CaptchaTokenCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CaptchaTokenCheckRequestValidationError) Field() string { return e.field }

func (e CaptchaTokenCheckRequestValidationError) Reason() string { return e.reason }

func (e CaptchaTokenCheckRequestValidationError) Message() string { return e.message }

func (e CaptchaTokenCheckRequestValidationError) Cause() error { return e.cause }

func (e CaptchaTokenCheckRequestValidationError) ErrorName() string {
	return "CaptchaTokenCheckRequestValidationError"
}

func (e CaptchaTokenCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CaptchaTokenCheckRequest." + e.field + ": " + e.message + cause
}

type CaptchaTokenCheckResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CaptchaTokenCheckResponseValidationError) Field() string { return e.field }

func (e CaptchaTokenCheckResponseValidationError) Reason() string { return e.reason }

func (e CaptchaTokenCheckResponseValidationError) Message() string { return e.message }

func (e CaptchaTokenCheckResponseValidationError) Cause() error { return e.cause }

func (e CaptchaTokenCheckResponseValidationError) ErrorName() string {
	return "CaptchaTokenCheckResponseValidationError"
}

func (e CaptchaTokenCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CaptchaTokenCheckResponse." + e.field + ": " + e.message + cause
}

type DeviceRiskRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceRiskRequestValidationError) Field() string { return e.field }

func (e DeviceRiskRequestValidationError) Reason() string { return e.reason }

func (e DeviceRiskRequestValidationError) Message() string { return e.message }

func (e DeviceRiskRequestValidationError) Cause() error { return e.cause }

func (e DeviceRiskRequestValidationError) ErrorName() string {
	return "DeviceRiskRequestValidationError"
}

func (e DeviceRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceRiskRequest." + e.field + ": " + e.message + cause
}

type DeviceRiskResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceRiskResponseValidationError) Field() string { return e.field }

func (e DeviceRiskResponseValidationError) Reason() string { return e.reason }

func (e DeviceRiskResponseValidationError) Message() string { return e.message }

func (e DeviceRiskResponseValidationError) Cause() error { return e.cause }

func (e DeviceRiskResponseValidationError) ErrorName() string {
	return "DeviceRiskResponseValidationError"
}

func (e DeviceRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceRiskResponse." + e.field + ": " + e.message + cause
}

type AddResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AddResponseValidationError) Field() string { return e.field }

func (e AddResponseValidationError) Reason() string { return e.reason }

func (e AddResponseValidationError) Message() string { return e.message }

func (e AddResponseValidationError) Cause() error { return e.cause }

func (e AddResponseValidationError) ErrorName() string { return "AddResponseValidationError" }

func (e AddResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AddResponse." + e.field + ": " + e.message + cause
}

type PayRiskCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PayRiskCheckRequestValidationError) Field() string { return e.field }

func (e PayRiskCheckRequestValidationError) Reason() string { return e.reason }

func (e PayRiskCheckRequestValidationError) Message() string { return e.message }

func (e PayRiskCheckRequestValidationError) Cause() error { return e.cause }

func (e PayRiskCheckRequestValidationError) ErrorName() string {
	return "PayRiskCheckRequestValidationError"
}

func (e PayRiskCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PayRiskCheckRequest." + e.field + ": " + e.message + cause
}

type SDKSendCometRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKSendCometRequestValidationError) Field() string { return e.field }

func (e SDKSendCometRequestValidationError) Reason() string { return e.reason }

func (e SDKSendCometRequestValidationError) Message() string { return e.message }

func (e SDKSendCometRequestValidationError) Cause() error { return e.cause }

func (e SDKSendCometRequestValidationError) ErrorName() string {
	return "SDKSendCometRequestValidationError"
}

func (e SDKSendCometRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKSendCometRequest." + e.field + ": " + e.message + cause
}

type ReloadGeetestConfigRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ReloadGeetestConfigRequestValidationError) Field() string { return e.field }

func (e ReloadGeetestConfigRequestValidationError) Reason() string { return e.reason }

func (e ReloadGeetestConfigRequestValidationError) Message() string { return e.message }

func (e ReloadGeetestConfigRequestValidationError) Cause() error { return e.cause }

func (e ReloadGeetestConfigRequestValidationError) ErrorName() string {
	return "ReloadGeetestConfigRequestValidationError"
}

func (e ReloadGeetestConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ReloadGeetestConfigRequest." + e.field + ": " + e.message + cause
}

type DecodeDataRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DecodeDataRequestValidationError) Field() string { return e.field }

func (e DecodeDataRequestValidationError) Reason() string { return e.reason }

func (e DecodeDataRequestValidationError) Message() string { return e.message }

func (e DecodeDataRequestValidationError) Cause() error { return e.cause }

func (e DecodeDataRequestValidationError) ErrorName() string {
	return "DecodeDataRequestValidationError"
}

func (e DecodeDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DecodeDataRequest." + e.field + ": " + e.message + cause
}

type DecodeDataResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DecodeDataResponseValidationError) Field() string { return e.field }

func (e DecodeDataResponseValidationError) Reason() string { return e.reason }

func (e DecodeDataResponseValidationError) Message() string { return e.message }

func (e DecodeDataResponseValidationError) Cause() error { return e.cause }

func (e DecodeDataResponseValidationError) ErrorName() string {
	return "DecodeDataResponseValidationError"
}

func (e DecodeDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DecodeDataResponse." + e.field + ": " + e.message + cause
}

type CheckAppIdRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CheckAppIdRequestValidationError) Field() string { return e.field }

func (e CheckAppIdRequestValidationError) Reason() string { return e.reason }

func (e CheckAppIdRequestValidationError) Message() string { return e.message }

func (e CheckAppIdRequestValidationError) Cause() error { return e.cause }

func (e CheckAppIdRequestValidationError) ErrorName() string {
	return "CheckAppIdRequestValidationError"
}

func (e CheckAppIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CheckAppIdRequest." + e.field + ": " + e.message + cause
}

type SDKDOIDCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKDOIDCheckRequestValidationError) Field() string { return e.field }

func (e SDKDOIDCheckRequestValidationError) Reason() string { return e.reason }

func (e SDKDOIDCheckRequestValidationError) Message() string { return e.message }

func (e SDKDOIDCheckRequestValidationError) Cause() error { return e.cause }

func (e SDKDOIDCheckRequestValidationError) ErrorName() string {
	return "SDKDOIDCheckRequestValidationError"
}

func (e SDKDOIDCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKDOIDCheckRequest." + e.field + ": " + e.message + cause
}

type SDKDOIDCheckResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKDOIDCheckResponseValidationError) Field() string { return e.field }

func (e SDKDOIDCheckResponseValidationError) Reason() string { return e.reason }

func (e SDKDOIDCheckResponseValidationError) Message() string { return e.message }

func (e SDKDOIDCheckResponseValidationError) Cause() error { return e.cause }

func (e SDKDOIDCheckResponseValidationError) ErrorName() string {
	return "SDKDOIDCheckResponseValidationError"
}

func (e SDKDOIDCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKDOIDCheckResponse." + e.field + ": " + e.message + cause
}

type ShumeiDecodeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDecodeRequestValidationError) Field() string { return e.field }

func (e ShumeiDecodeRequestValidationError) Reason() string { return e.reason }

func (e ShumeiDecodeRequestValidationError) Message() string { return e.message }

func (e ShumeiDecodeRequestValidationError) Cause() error { return e.cause }

func (e ShumeiDecodeRequestValidationError) ErrorName() string {
	return "ShumeiDecodeRequestValidationError"
}

func (e ShumeiDecodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDecodeRequest." + e.field + ": " + e.message + cause
}

type ShumeiDecodeResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDecodeResponseValidationError) Field() string { return e.field }

func (e ShumeiDecodeResponseValidationError) Reason() string { return e.reason }

func (e ShumeiDecodeResponseValidationError) Message() string { return e.message }

func (e ShumeiDecodeResponseValidationError) Cause() error { return e.cause }

func (e ShumeiDecodeResponseValidationError) ErrorName() string {
	return "ShumeiDecodeResponseValidationError"
}

func (e ShumeiDecodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDecodeResponse." + e.field + ": " + e.message + cause
}

type BindNoticeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BindNoticeRequestValidationError) Field() string { return e.field }

func (e BindNoticeRequestValidationError) Reason() string { return e.reason }

func (e BindNoticeRequestValidationError) Message() string { return e.message }

func (e BindNoticeRequestValidationError) Cause() error { return e.cause }

func (e BindNoticeRequestValidationError) ErrorName() string {
	return "BindNoticeRequestValidationError"
}

func (e BindNoticeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BindNoticeRequest." + e.field + ": " + e.message + cause
}

type SDKDOIDRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKDOIDRequestValidationError) Field() string { return e.field }

func (e SDKDOIDRequestValidationError) Reason() string { return e.reason }

func (e SDKDOIDRequestValidationError) Message() string { return e.message }

func (e SDKDOIDRequestValidationError) Cause() error { return e.cause }

func (e SDKDOIDRequestValidationError) ErrorName() string { return "SDKDOIDRequestValidationError" }

func (e SDKDOIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKDOIDRequest." + e.field + ": " + e.message + cause
}

type SDKDOIDResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKDOIDResponseValidationError) Field() string { return e.field }

func (e SDKDOIDResponseValidationError) Reason() string { return e.reason }

func (e SDKDOIDResponseValidationError) Message() string { return e.message }

func (e SDKDOIDResponseValidationError) Cause() error { return e.cause }

func (e SDKDOIDResponseValidationError) ErrorName() string { return "SDKDOIDResponseValidationError" }

func (e SDKDOIDResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKDOIDResponse." + e.field + ": " + e.message + cause
}

type FaceStatusRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceStatusRequestValidationError) Field() string { return e.field }

func (e FaceStatusRequestValidationError) Reason() string { return e.reason }

func (e FaceStatusRequestValidationError) Message() string { return e.message }

func (e FaceStatusRequestValidationError) Cause() error { return e.cause }

func (e FaceStatusRequestValidationError) ErrorName() string {
	return "FaceStatusRequestValidationError"
}

func (e FaceStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceStatusRequest." + e.field + ": " + e.message + cause
}

type FaceStatusResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceStatusResponseValidationError) Field() string { return e.field }

func (e FaceStatusResponseValidationError) Reason() string { return e.reason }

func (e FaceStatusResponseValidationError) Message() string { return e.message }

func (e FaceStatusResponseValidationError) Cause() error { return e.cause }

func (e FaceStatusResponseValidationError) ErrorName() string {
	return "FaceStatusResponseValidationError"
}

func (e FaceStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceStatusResponse." + e.field + ": " + e.message + cause
}

type FaceCodeForWebRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceCodeForWebRequestValidationError) Field() string { return e.field }

func (e FaceCodeForWebRequestValidationError) Reason() string { return e.reason }

func (e FaceCodeForWebRequestValidationError) Message() string { return e.message }

func (e FaceCodeForWebRequestValidationError) Cause() error { return e.cause }

func (e FaceCodeForWebRequestValidationError) ErrorName() string {
	return "FaceCodeForWebRequestValidationError"
}

func (e FaceCodeForWebRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceCodeForWebRequest." + e.field + ": " + e.message + cause
}

type FaceCodeForWebResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceCodeForWebResponseValidationError) Field() string { return e.field }

func (e FaceCodeForWebResponseValidationError) Reason() string { return e.reason }

func (e FaceCodeForWebResponseValidationError) Message() string { return e.message }

func (e FaceCodeForWebResponseValidationError) Cause() error { return e.cause }

func (e FaceCodeForWebResponseValidationError) ErrorName() string {
	return "FaceCodeForWebResponseValidationError"
}

func (e FaceCodeForWebResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceCodeForWebResponse." + e.field + ": " + e.message + cause
}

type QrCodeGenerateRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QrCodeGenerateRequestValidationError) Field() string { return e.field }

func (e QrCodeGenerateRequestValidationError) Reason() string { return e.reason }

func (e QrCodeGenerateRequestValidationError) Message() string { return e.message }

func (e QrCodeGenerateRequestValidationError) Cause() error { return e.cause }

func (e QrCodeGenerateRequestValidationError) ErrorName() string {
	return "QrCodeGenerateRequestValidationError"
}

func (e QrCodeGenerateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QrCodeGenerateRequest." + e.field + ": " + e.message + cause
}

type QrCodeGenerateResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QrCodeGenerateResponseValidationError) Field() string { return e.field }

func (e QrCodeGenerateResponseValidationError) Reason() string { return e.reason }

func (e QrCodeGenerateResponseValidationError) Message() string { return e.message }

func (e QrCodeGenerateResponseValidationError) Cause() error { return e.cause }

func (e QrCodeGenerateResponseValidationError) ErrorName() string {
	return "QrCodeGenerateResponseValidationError"
}

func (e QrCodeGenerateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QrCodeGenerateResponse." + e.field + ": " + e.message + cause
}

type QrCodeScanRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QrCodeScanRequestValidationError) Field() string { return e.field }

func (e QrCodeScanRequestValidationError) Reason() string { return e.reason }

func (e QrCodeScanRequestValidationError) Message() string { return e.message }

func (e QrCodeScanRequestValidationError) Cause() error { return e.cause }

func (e QrCodeScanRequestValidationError) ErrorName() string {
	return "QrCodeScanRequestValidationError"
}

func (e QrCodeScanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QrCodeScanRequest." + e.field + ": " + e.message + cause
}

type QrCodeScanResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QrCodeScanResponseValidationError) Field() string { return e.field }

func (e QrCodeScanResponseValidationError) Reason() string { return e.reason }

func (e QrCodeScanResponseValidationError) Message() string { return e.message }

func (e QrCodeScanResponseValidationError) Cause() error { return e.cause }

func (e QrCodeScanResponseValidationError) ErrorName() string {
	return "QrCodeScanResponseValidationError"
}

func (e QrCodeScanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QrCodeScanResponse." + e.field + ": " + e.message + cause
}

type FaceStatusQueryByQrCodeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceStatusQueryByQrCodeRequestValidationError) Field() string { return e.field }

func (e FaceStatusQueryByQrCodeRequestValidationError) Reason() string { return e.reason }

func (e FaceStatusQueryByQrCodeRequestValidationError) Message() string { return e.message }

func (e FaceStatusQueryByQrCodeRequestValidationError) Cause() error { return e.cause }

func (e FaceStatusQueryByQrCodeRequestValidationError) ErrorName() string {
	return "FaceStatusQueryByQrCodeRequestValidationError"
}

func (e FaceStatusQueryByQrCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceStatusQueryByQrCodeRequest." + e.field + ": " + e.message + cause
}

type FaceStatusQueryByQrCodeResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceStatusQueryByQrCodeResponseValidationError) Field() string { return e.field }

func (e FaceStatusQueryByQrCodeResponseValidationError) Reason() string { return e.reason }

func (e FaceStatusQueryByQrCodeResponseValidationError) Message() string { return e.message }

func (e FaceStatusQueryByQrCodeResponseValidationError) Cause() error { return e.cause }

func (e FaceStatusQueryByQrCodeResponseValidationError) ErrorName() string {
	return "FaceStatusQueryByQrCodeResponseValidationError"
}

func (e FaceStatusQueryByQrCodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceStatusQueryByQrCodeResponse." + e.field + ": " + e.message + cause
}

// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.6
// protoc              v4.25.1
// source: proto/base.proto

package proto

func (x *FaceServiceData) Validate() error {
	return nil
}

func (x *FaceResultRequest) Validate() error {
	if len(x.GetCode()) == 0 {
		return FaceResultRequestValidationError{
			field:   "Code",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *FaceResultResponse) Validate() error {
	return nil
}

type FaceServiceDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceServiceDataValidationError) Field() string { return e.field }

func (e FaceServiceDataValidationError) Reason() string { return e.reason }

func (e FaceServiceDataValidationError) Message() string { return e.message }

func (e FaceServiceDataValidationError) Cause() error { return e.cause }

func (e FaceServiceDataValidationError) ErrorName() string { return "FaceServiceDataValidationError" }

func (e FaceServiceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceServiceData." + e.field + ": " + e.message + cause
}

type FaceResultRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceResultRequestValidationError) Field() string { return e.field }

func (e FaceResultRequestValidationError) Reason() string { return e.reason }

func (e FaceResultRequestValidationError) Message() string { return e.message }

func (e FaceResultRequestValidationError) Cause() error { return e.cause }

func (e FaceResultRequestValidationError) ErrorName() string {
	return "FaceResultRequestValidationError"
}

func (e FaceResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceResultRequest." + e.field + ": " + e.message + cause
}

type FaceResultResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceResultResponseValidationError) Field() string { return e.field }

func (e FaceResultResponseValidationError) Reason() string { return e.reason }

func (e FaceResultResponseValidationError) Message() string { return e.message }

func (e FaceResultResponseValidationError) Cause() error { return e.cause }

func (e FaceResultResponseValidationError) ErrorName() string {
	return "FaceResultResponseValidationError"
}

func (e FaceResultResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceResultResponse." + e.field + ": " + e.message + cause
}

syntax = "proto3";

package papegames.sparrow.risk;

import "tagger/tagger.proto";
import "google/api/field_behavior.proto";

option go_package = "risk/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "RiskProto";
option java_package = "com.papegames.sparrow.risk";


message FaceServiceData {
  //认证id
  string certify_id = 1;
}

message FaceResultRequest {
  //人脸识别初始化接口返回的code
  string code = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  //租户 ID
  int64 client_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];

  //DOID值
  string DOID = 5;
}

message FaceResultResponse {
  option (tagger.disable_omitempty) = true;
  int32  status = 1;
  string pass_token = 2;
}


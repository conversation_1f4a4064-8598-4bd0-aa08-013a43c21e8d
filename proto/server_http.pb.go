// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.5.0
// protoc          v4.25.1
// source: risk
package proto

import (
	context "context"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func RegisterRiskServerServiceGinServer(s *xgin.Server, srv RiskServerServiceServer) {
	eng := s.GetGinEngine()
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/captcha/token/check",
		_RiskServerServiceGin_CaptchaTokenCheck_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/captcha/token/check", "CaptchaTokenCheck")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/app/check",
		_RiskServerServiceGin_AppCheckRisk_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/app/check", "AppCheckRisk")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/account/check",
		_RiskServerServiceGin_AccountCheckRisk_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/account/check", "AccountCheckRisk")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/payment/check",
		_RiskServerServiceGin_PayCheckRisk_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/payment/check", "PayCheckRisk")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/device/risk",
		_RiskServerServiceGin_DeviceRisk_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/device/risk", "DeviceRisk")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/black_list/create",
		_RiskServerServiceGin_AddBlackList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/black_list/create", "AddBlackList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/black_list/update",
		_RiskServerServiceGin_EditBlackList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/black_list/update", "EditBlackList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/black_list/delete",
		_RiskServerServiceGin_DeleteBlackList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/black_list/delete", "DeleteBlackList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/white_list/create",
		_RiskServerServiceGin_AddWhiteList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/white_list/create", "AddWhiteList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/white_list/update",
		_RiskServerServiceGin_EditWhiteList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/white_list/update", "EditWhiteList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/white_list/delete",
		_RiskServerServiceGin_DeleteWhiteList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/white_list/delete", "DeleteWhiteList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/dark_list/create",
		_RiskServerServiceGin_AddDarkList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/dark_list/create", "AddDarkList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/dark_list/update",
		_RiskServerServiceGin_EditDarkList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/dark_list/update", "EditDarkList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/dark_list/delete",
		_RiskServerServiceGin_DeleteDarkList_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/dark_list/delete", "DeleteDarkList")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/config_app/create",
		_RiskServerServiceGin_AddConfigApp_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/config_app/create", "AddConfigApp")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/config_app/update",
		_RiskServerServiceGin_EditConfigApp_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/config_app/update", "EditConfigApp")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/config_app/delete",
		_RiskServerServiceGin_DeleteConfigApp_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/config_app/delete", "DeleteConfigApp")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/comet/send",
		_RiskServerServiceGin_SDKSendComet_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/comet/send", "SDKSendComet")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/geetest_config/reload",
		_RiskServerServiceGin_ReloadGeetestConfig_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/geetest_config/reload", "ReloadGeetestConfig")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/tob/approved/delete",
		_RiskServerServiceGin_DeleteApproved_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/tob/approved/delete", "DeleteApproved")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/decode",
		_RiskServerServiceGin_DecodeData_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/decode", "DecodeData")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/encode",
		_RiskServerServiceGin_EncodeData_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/encode", "EncodeData")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/app_id/check",
		_RiskServerServiceGin_CheckAppId_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/app_id/check", "CheckAppId")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/ds/check",
		_RiskServerServiceGin_SDKDOIDCheck_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/ds/check", "SDKDOIDCheck")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/shumei/decode",
		_RiskServerServiceGin_ShumeiDecode_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/shumei/decode", "ShumeiDecode")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/bind/notice",
		_RiskServerServiceGin_BindNotice_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/bind/notice", "BindNotice")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/device/sdk_doid",
		_RiskServerServiceGin_SDKDOIDToDOID_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/device/sdk_doid", "SDKDOIDToDOID")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/face/status",
		_RiskServerServiceGin_FaceStatus_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/face/status", "FaceStatus")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/face/code/web",
		_RiskServerServiceGin_FaceCodeForWeb_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/face/code/web", "FaceCodeForWeb")
}

func _RiskServerServiceGin_CaptchaTokenCheck_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/captcha/token/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CaptchaTokenCheck(ctx, in.(*CaptchaTokenCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(CaptchaTokenCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CaptchaTokenCheck(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AppCheckRisk_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/app/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AppCheckRisk(ctx, in.(*AppRiskCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AppRiskCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AppCheckRisk(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AccountCheckRisk_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/account/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AccountCheckRisk(ctx, in.(*AccountRiskCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AccountRiskCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AccountCheckRisk(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_PayCheckRisk_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/payment/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.PayCheckRisk(ctx, in.(*PayRiskCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(PayRiskCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.PayCheckRisk(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeviceRisk_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/device/risk",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeviceRisk(ctx, in.(*DeviceRiskRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(DeviceRiskRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeviceRisk(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AddBlackList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/black_list/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AddBlackList(ctx, in.(*AddBlackListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AddBlackListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AddBlackList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_EditBlackList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/black_list/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.EditBlackList(ctx, in.(*EditBlackListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(EditBlackListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.EditBlackList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeleteBlackList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/black_list/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteBlackList(ctx, in.(*ClientIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ClientIdRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteBlackList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AddWhiteList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/white_list/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AddWhiteList(ctx, in.(*AddWhiteListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AddWhiteListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AddWhiteList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_EditWhiteList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/white_list/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.EditWhiteList(ctx, in.(*EditWhiteListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(EditWhiteListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.EditWhiteList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeleteWhiteList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/white_list/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteWhiteList(ctx, in.(*ClientIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ClientIdRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteWhiteList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AddDarkList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/dark_list/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AddDarkList(ctx, in.(*AddDarkListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AddDarkListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AddDarkList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_EditDarkList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/dark_list/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.EditDarkList(ctx, in.(*EditDarkListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(EditDarkListRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.EditDarkList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeleteDarkList_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/dark_list/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteDarkList(ctx, in.(*ClientIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ClientIdRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteDarkList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_AddConfigApp_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/config_app/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.AddConfigApp(ctx, in.(*AddConfigAppRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(AddConfigAppRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.AddConfigApp(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_EditConfigApp_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/config_app/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.EditConfigApp(ctx, in.(*EditConfigAppRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(EditConfigAppRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.EditConfigApp(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeleteConfigApp_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/config_app/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteConfigApp(ctx, in.(*ClientIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ClientIdRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteConfigApp(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_SDKSendComet_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/comet/send",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKSendComet(ctx, in.(*SDKSendCometRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKSendCometRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKSendComet(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_ReloadGeetestConfig_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/geetest_config/reload",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ReloadGeetestConfig(ctx, in.(*ReloadGeetestConfigRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ReloadGeetestConfigRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ReloadGeetestConfig(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DeleteApproved_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/tob/approved/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteApproved(ctx, in.(*DeleteApprovedRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(DeleteApprovedRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteApproved(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_DecodeData_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/decode",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DecodeData(ctx, in.(*DecodeDataRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(DecodeDataRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DecodeData(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_EncodeData_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/encode",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.EncodeData(ctx, in.(*DecodeDataRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(DecodeDataRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.EncodeData(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_CheckAppId_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/app_id/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CheckAppId(ctx, in.(*CheckAppIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(CheckAppIdRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CheckAppId(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_SDKDOIDCheck_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/ds/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKDOIDCheck(ctx, in.(*SDKDOIDCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKDOIDCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKDOIDCheck(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_ShumeiDecode_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/shumei/decode",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ShumeiDecode(ctx, in.(*ShumeiDecodeRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ShumeiDecodeRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ShumeiDecode(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_BindNotice_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/bind/notice",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.BindNotice(ctx, in.(*BindNoticeRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(BindNoticeRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.BindNotice(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_SDKDOIDToDOID_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/device/sdk_doid",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKDOIDToDOID(ctx, in.(*SDKDOIDRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKDOIDRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKDOIDToDOID(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_FaceStatus_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/face/status",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.FaceStatus(ctx, in.(*FaceStatusRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(FaceStatusRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.FaceStatus(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskServerServiceGin_FaceCodeForWeb_Handler(srv RiskServerServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/face/code/web",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.FaceCodeForWeb(ctx, in.(*FaceCodeForWebRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(FaceCodeForWebRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.FaceCodeForWeb(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

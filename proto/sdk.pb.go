// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: proto/sdk.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendCometRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      string                 `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Nid           string                 `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"`
	Code          uint64                 `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	DOID          string                 `protobuf:"bytes,4,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Ip            string                 `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	Account       string                 `protobuf:"bytes,6,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCometRequest) Reset() {
	*x = SendCometRequest{}
	mi := &file_proto_sdk_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCometRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCometRequest) ProtoMessage() {}

func (x *SendCometRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCometRequest.ProtoReflect.Descriptor instead.
func (*SendCometRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{0}
}

func (x *SendCometRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SendCometRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *SendCometRequest) GetCode() uint64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendCometRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *SendCometRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SendCometRequest) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type GCaptchaRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Number        string                 `protobuf:"bytes,1,opt,name=number,proto3" json:"number,omitempty"`
	Captcha       string                 `protobuf:"bytes,2,opt,name=captcha,proto3" json:"captcha,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	Time          string                 `protobuf:"bytes,4,opt,name=time,proto3" json:"time,omitempty"`
	CaptchaId     string                 `protobuf:"bytes,5,opt,name=captcha_id,json=captchaId,proto3" json:"captcha_id,omitempty"`
	Nonce         string                 `protobuf:"bytes,6,opt,name=nonce,proto3" json:"nonce,omitempty"`
	ClientId      uint32                 `protobuf:"varint,7,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,8,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	DOID          string                 `protobuf:"bytes,9,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GCaptchaRequest) Reset() {
	*x = GCaptchaRequest{}
	mi := &file_proto_sdk_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GCaptchaRequest) ProtoMessage() {}

func (x *GCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GCaptchaRequest.ProtoReflect.Descriptor instead.
func (*GCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{1}
}

func (x *GCaptchaRequest) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *GCaptchaRequest) GetCaptcha() string {
	if x != nil {
		return x.Captcha
	}
	return ""
}

func (x *GCaptchaRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GCaptchaRequest) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *GCaptchaRequest) GetCaptchaId() string {
	if x != nil {
		return x.CaptchaId
	}
	return ""
}

func (x *GCaptchaRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *GCaptchaRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *GCaptchaRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GCaptchaRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type GCaptchaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PassToken     string                 `protobuf:"bytes,1,opt,name=pass_token,json=passToken,proto3" json:"pass_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GCaptchaResponse) Reset() {
	*x = GCaptchaResponse{}
	mi := &file_proto_sdk_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GCaptchaResponse) ProtoMessage() {}

func (x *GCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GCaptchaResponse.ProtoReflect.Descriptor instead.
func (*GCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{2}
}

func (x *GCaptchaResponse) GetPassToken() string {
	if x != nil {
		return x.PassToken
	}
	return ""
}

type ShumeiDeviceGetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDeviceGetRequest) Reset() {
	*x = ShumeiDeviceGetRequest{}
	mi := &file_proto_sdk_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDeviceGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDeviceGetRequest) ProtoMessage() {}

func (x *ShumeiDeviceGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDeviceGetRequest.ProtoReflect.Descriptor instead.
func (*ShumeiDeviceGetRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{3}
}

type ShumeiDeviceRequestData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Organization  string                 `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	Os            string                 `protobuf:"bytes,2,opt,name=os,proto3" json:"os,omitempty"`
	AppId         string                 `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Encode        int32                  `protobuf:"varint,4,opt,name=encode,proto3" json:"encode,omitempty"`
	Compress      int32                  `protobuf:"varint,5,opt,name=compress,proto3" json:"compress,omitempty"`
	Tn            string                 `protobuf:"bytes,6,opt,name=tn,proto3" json:"tn,omitempty"`
	Ep            string                 `protobuf:"bytes,7,opt,name=ep,proto3" json:"ep,omitempty"`
	Retry         int32                  `protobuf:"varint,8,opt,name=retry,proto3" json:"retry,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDeviceRequestData) Reset() {
	*x = ShumeiDeviceRequestData{}
	mi := &file_proto_sdk_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDeviceRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDeviceRequestData) ProtoMessage() {}

func (x *ShumeiDeviceRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDeviceRequestData.ProtoReflect.Descriptor instead.
func (*ShumeiDeviceRequestData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{4}
}

func (x *ShumeiDeviceRequestData) GetOrganization() string {
	if x != nil {
		return x.Organization
	}
	return ""
}

func (x *ShumeiDeviceRequestData) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *ShumeiDeviceRequestData) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ShumeiDeviceRequestData) GetEncode() int32 {
	if x != nil {
		return x.Encode
	}
	return 0
}

func (x *ShumeiDeviceRequestData) GetCompress() int32 {
	if x != nil {
		return x.Compress
	}
	return 0
}

func (x *ShumeiDeviceRequestData) GetTn() string {
	if x != nil {
		return x.Tn
	}
	return ""
}

func (x *ShumeiDeviceRequestData) GetEp() string {
	if x != nil {
		return x.Ep
	}
	return ""
}

func (x *ShumeiDeviceRequestData) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

type ShumeiDeviceResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExtraInfo     *SDKDeviceInfo         `protobuf:"bytes,31,opt,name=extraInfo,proto3" json:"extraInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDeviceResponseData) Reset() {
	*x = ShumeiDeviceResponseData{}
	mi := &file_proto_sdk_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDeviceResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDeviceResponseData) ProtoMessage() {}

func (x *ShumeiDeviceResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDeviceResponseData.ProtoReflect.Descriptor instead.
func (*ShumeiDeviceResponseData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{5}
}

func (x *ShumeiDeviceResponseData) GetExtraInfo() *SDKDeviceInfo {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

type DeviceGetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Information   string                 `protobuf:"bytes,2,opt,name=information,proto3" json:"information,omitempty"`
	ThirdDeviceId string                 `protobuf:"bytes,3,opt,name=third_device_id,json=thirdDeviceId,proto3" json:"third_device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceGetRequest) Reset() {
	*x = DeviceGetRequest{}
	mi := &file_proto_sdk_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceGetRequest) ProtoMessage() {}

func (x *DeviceGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceGetRequest.ProtoReflect.Descriptor instead.
func (*DeviceGetRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{6}
}

func (x *DeviceGetRequest) GetInformation() string {
	if x != nil {
		return x.Information
	}
	return ""
}

func (x *DeviceGetRequest) GetThirdDeviceId() string {
	if x != nil {
		return x.ThirdDeviceId
	}
	return ""
}

type ThirdDeviceIdData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fingerprint   string                 `protobuf:"bytes,1,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	Shumei        string                 `protobuf:"bytes,2,opt,name=shumei,proto3" json:"shumei,omitempty"`
	Sdk_DOID      string                 `protobuf:"bytes,3,opt,name=sdk_DOID,json=sdkDOID,proto3" json:"sdk_DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThirdDeviceIdData) Reset() {
	*x = ThirdDeviceIdData{}
	mi := &file_proto_sdk_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThirdDeviceIdData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdDeviceIdData) ProtoMessage() {}

func (x *ThirdDeviceIdData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdDeviceIdData.ProtoReflect.Descriptor instead.
func (*ThirdDeviceIdData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{7}
}

func (x *ThirdDeviceIdData) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

func (x *ThirdDeviceIdData) GetShumei() string {
	if x != nil {
		return x.Shumei
	}
	return ""
}

func (x *ThirdDeviceIdData) GetSdk_DOID() string {
	if x != nil {
		return x.Sdk_DOID
	}
	return ""
}

type DeviceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ThirdInfo     string                 `protobuf:"bytes,3,opt,name=third_info,json=thirdInfo,proto3" json:"third_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	mi := &file_proto_sdk_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{8}
}

func (x *DeviceInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceInfo) GetThirdInfo() string {
	if x != nil {
		return x.ThirdInfo
	}
	return ""
}

type DeviceGetResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DOID             string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	ProviderDeviceId string                 `protobuf:"bytes,2,opt,name=provider_device_id,json=providerDeviceId,proto3" json:"provider_device_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DeviceGetResponse) Reset() {
	*x = DeviceGetResponse{}
	mi := &file_proto_sdk_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceGetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceGetResponse) ProtoMessage() {}

func (x *DeviceGetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceGetResponse.ProtoReflect.Descriptor instead.
func (*DeviceGetResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{9}
}

func (x *DeviceGetResponse) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *DeviceGetResponse) GetProviderDeviceId() string {
	if x != nil {
		return x.ProviderDeviceId
	}
	return ""
}

type DeviceGetResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Detail        *Detail                `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceGetResponseData) Reset() {
	*x = DeviceGetResponseData{}
	mi := &file_proto_sdk_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceGetResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceGetResponseData) ProtoMessage() {}

func (x *DeviceGetResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceGetResponseData.ProtoReflect.Descriptor instead.
func (*DeviceGetResponseData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{10}
}

func (x *DeviceGetResponseData) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceGetResponseData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DeviceGetResponseData) GetDetail() *Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type Detail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Detail) Reset() {
	*x = Detail{}
	mi := &file_proto_sdk_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail) ProtoMessage() {}

func (x *Detail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail.ProtoReflect.Descriptor instead.
func (*Detail) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{11}
}

func (x *Detail) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *Detail) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type DeviceLogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Information   string                 `protobuf:"bytes,2,opt,name=information,proto3" json:"information,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceLogsRequest) Reset() {
	*x = DeviceLogsRequest{}
	mi := &file_proto_sdk_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceLogsRequest) ProtoMessage() {}

func (x *DeviceLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceLogsRequest.ProtoReflect.Descriptor instead.
func (*DeviceLogsRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{12}
}

func (x *DeviceLogsRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *DeviceLogsRequest) GetInformation() string {
	if x != nil {
		return x.Information
	}
	return ""
}

type BizInitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      uint32                 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	IncludeKeys   string                 `protobuf:"bytes,2,opt,name=include_keys,json=includeKeys,proto3" json:"include_keys,omitempty"`
	ExcludeKeys   string                 `protobuf:"bytes,3,opt,name=exclude_keys,json=excludeKeys,proto3" json:"exclude_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BizInitRequest) Reset() {
	*x = BizInitRequest{}
	mi := &file_proto_sdk_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BizInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizInitRequest) ProtoMessage() {}

func (x *BizInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizInitRequest.ProtoReflect.Descriptor instead.
func (*BizInitRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{13}
}

func (x *BizInitRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *BizInitRequest) GetIncludeKeys() string {
	if x != nil {
		return x.IncludeKeys
	}
	return ""
}

func (x *BizInitRequest) GetExcludeKeys() string {
	if x != nil {
		return x.ExcludeKeys
	}
	return ""
}

type BizInitResponse struct {
	state          protoimpl.MessageState       `protogen:"open.v1"`
	Captcha        *BizInitCaptchaData          `protobuf:"bytes,1,opt,name=captcha,proto3" json:"captcha,omitempty"`
	Oae            bool                         `protobuf:"varint,2,opt,name=oae,proto3" json:"oae,omitempty"`
	Ccr            bool                         `protobuf:"varint,3,opt,name=ccr,proto3" json:"ccr,omitempty"`
	InteractiveMsg string                       `protobuf:"bytes,4,opt,name=interactive_msg,json=interactiveMsg,proto3" json:"interactive_msg,omitempty"`
	Options        map[string]*xtype.RawMessage `protobuf:"bytes,5,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BizInitResponse) Reset() {
	*x = BizInitResponse{}
	mi := &file_proto_sdk_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BizInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizInitResponse) ProtoMessage() {}

func (x *BizInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizInitResponse.ProtoReflect.Descriptor instead.
func (*BizInitResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{14}
}

func (x *BizInitResponse) GetCaptcha() *BizInitCaptchaData {
	if x != nil {
		return x.Captcha
	}
	return nil
}

func (x *BizInitResponse) GetOae() bool {
	if x != nil {
		return x.Oae
	}
	return false
}

func (x *BizInitResponse) GetCcr() bool {
	if x != nil {
		return x.Ccr
	}
	return false
}

func (x *BizInitResponse) GetInteractiveMsg() string {
	if x != nil {
		return x.InteractiveMsg
	}
	return ""
}

func (x *BizInitResponse) GetOptions() map[string]*xtype.RawMessage {
	if x != nil {
		return x.Options
	}
	return nil
}

type BizInitCaptchaData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaptchaId     string                 `protobuf:"bytes,1,opt,name=captcha_id,json=captchaId,proto3" json:"captcha_id,omitempty"`
	Close         bool                   `protobuf:"varint,2,opt,name=close,proto3" json:"close,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BizInitCaptchaData) Reset() {
	*x = BizInitCaptchaData{}
	mi := &file_proto_sdk_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BizInitCaptchaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizInitCaptchaData) ProtoMessage() {}

func (x *BizInitCaptchaData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizInitCaptchaData.ProtoReflect.Descriptor instead.
func (*BizInitCaptchaData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{15}
}

func (x *BizInitCaptchaData) GetCaptchaId() string {
	if x != nil {
		return x.CaptchaId
	}
	return ""
}

func (x *BizInitCaptchaData) GetClose() bool {
	if x != nil {
		return x.Close
	}
	return false
}

type HealthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthRequest) Reset() {
	*x = HealthRequest{}
	mi := &file_proto_sdk_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthRequest) ProtoMessage() {}

func (x *HealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthRequest.ProtoReflect.Descriptor instead.
func (*HealthRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{16}
}

type ThirdDeviceData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThirdDeviceId string                 `protobuf:"bytes,1,opt,name=third_device_id,json=thirdDeviceId,proto3" json:"third_device_id,omitempty"`
	ThirdData     string                 `protobuf:"bytes,2,opt,name=third_data,json=thirdData,proto3" json:"third_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThirdDeviceData) Reset() {
	*x = ThirdDeviceData{}
	mi := &file_proto_sdk_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThirdDeviceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdDeviceData) ProtoMessage() {}

func (x *ThirdDeviceData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdDeviceData.ProtoReflect.Descriptor instead.
func (*ThirdDeviceData) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{17}
}

func (x *ThirdDeviceData) GetThirdDeviceId() string {
	if x != nil {
		return x.ThirdDeviceId
	}
	return ""
}

func (x *ThirdDeviceData) GetThirdData() string {
	if x != nil {
		return x.ThirdData
	}
	return ""
}

type ShumeiDeviceConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDeviceConfigRequest) Reset() {
	*x = ShumeiDeviceConfigRequest{}
	mi := &file_proto_sdk_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDeviceConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDeviceConfigRequest) ProtoMessage() {}

func (x *ShumeiDeviceConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDeviceConfigRequest.ProtoReflect.Descriptor instead.
func (*ShumeiDeviceConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{18}
}

type ShumeiDeviceConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiDeviceConfigResponse) Reset() {
	*x = ShumeiDeviceConfigResponse{}
	mi := &file_proto_sdk_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiDeviceConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiDeviceConfigResponse) ProtoMessage() {}

func (x *ShumeiDeviceConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiDeviceConfigResponse.ProtoReflect.Descriptor instead.
func (*ShumeiDeviceConfigResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{19}
}

type SDKCheckPhoneRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nonce         string                 `protobuf:"bytes,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Phone         string                 `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	Mock          int32                  `protobuf:"varint,3,opt,name=mock,proto3" json:"mock,omitempty"`
	ClientId      uint32                 `protobuf:"varint,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKCheckPhoneRequest) Reset() {
	*x = SDKCheckPhoneRequest{}
	mi := &file_proto_sdk_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKCheckPhoneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKCheckPhoneRequest) ProtoMessage() {}

func (x *SDKCheckPhoneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKCheckPhoneRequest.ProtoReflect.Descriptor instead.
func (*SDKCheckPhoneRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{20}
}

func (x *SDKCheckPhoneRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *SDKCheckPhoneRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SDKCheckPhoneRequest) GetMock() int32 {
	if x != nil {
		return x.Mock
	}
	return 0
}

func (x *SDKCheckPhoneRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SDKCheckPhoneRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type SDKCheckPhoneResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKCheckPhoneResponse) Reset() {
	*x = SDKCheckPhoneResponse{}
	mi := &file_proto_sdk_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKCheckPhoneResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKCheckPhoneResponse) ProtoMessage() {}

func (x *SDKCheckPhoneResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKCheckPhoneResponse.ProtoReflect.Descriptor instead.
func (*SDKCheckPhoneResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{21}
}

func (x *SDKCheckPhoneResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type SDKPhoneCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nonce         string                 `protobuf:"bytes,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	ClientId      uint32                 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKPhoneCodeRequest) Reset() {
	*x = SDKPhoneCodeRequest{}
	mi := &file_proto_sdk_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKPhoneCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKPhoneCodeRequest) ProtoMessage() {}

func (x *SDKPhoneCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKPhoneCodeRequest.ProtoReflect.Descriptor instead.
func (*SDKPhoneCodeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{22}
}

func (x *SDKPhoneCodeRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *SDKPhoneCodeRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SDKPhoneCodeRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type SDKCheckCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nonce         string                 `protobuf:"bytes,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	ClientId      uint32                 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AppId         string                 `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	DOID          string                 `protobuf:"bytes,5,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKCheckCodeRequest) Reset() {
	*x = SDKCheckCodeRequest{}
	mi := &file_proto_sdk_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKCheckCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKCheckCodeRequest) ProtoMessage() {}

func (x *SDKCheckCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKCheckCodeRequest.ProtoReflect.Descriptor instead.
func (*SDKCheckCodeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{23}
}

func (x *SDKCheckCodeRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *SDKCheckCodeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SDKCheckCodeRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SDKCheckCodeRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SDKCheckCodeRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type SDKCheckEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nonce         string                 `protobuf:"bytes,1,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKCheckEmailRequest) Reset() {
	*x = SDKCheckEmailRequest{}
	mi := &file_proto_sdk_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKCheckEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKCheckEmailRequest) ProtoMessage() {}

func (x *SDKCheckEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKCheckEmailRequest.ProtoReflect.Descriptor instead.
func (*SDKCheckEmailRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{24}
}

func (x *SDKCheckEmailRequest) GetNonce() string {
	if x != nil {
		return x.Nonce
	}
	return ""
}

func (x *SDKCheckEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type SDKDeviceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Os            string                 `protobuf:"bytes,1,opt,name=os,proto3" json:"os,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	AndroidId     string                 `protobuf:"bytes,3,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	Oaid          string                 `protobuf:"bytes,4,opt,name=oaid,proto3" json:"oaid,omitempty"`
	Idfv          string                 `protobuf:"bytes,5,opt,name=idfv,proto3" json:"idfv,omitempty"`
	Idfa          string                 `protobuf:"bytes,6,opt,name=idfa,proto3" json:"idfa,omitempty"`
	PaperDeviceId string                 `protobuf:"bytes,7,opt,name=paper_device_id,json=paperDeviceId,proto3" json:"paper_device_id,omitempty"`
	IsDark        bool                   `protobuf:"varint,8,opt,name=is_dark,json=isDark,proto3" json:"is_dark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SDKDeviceInfo) Reset() {
	*x = SDKDeviceInfo{}
	mi := &file_proto_sdk_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SDKDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKDeviceInfo) ProtoMessage() {}

func (x *SDKDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKDeviceInfo.ProtoReflect.Descriptor instead.
func (*SDKDeviceInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{25}
}

func (x *SDKDeviceInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SDKDeviceInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SDKDeviceInfo) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *SDKDeviceInfo) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *SDKDeviceInfo) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *SDKDeviceInfo) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *SDKDeviceInfo) GetPaperDeviceId() string {
	if x != nil {
		return x.PaperDeviceId
	}
	return ""
}

func (x *SDKDeviceInfo) GetIsDark() bool {
	if x != nil {
		return x.IsDark
	}
	return false
}

type CreateDOID struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SDKDeviceInfo     *SDKDeviceInfo         `protobuf:"bytes,1,opt,name=SDKDeviceInfo,proto3" json:"SDKDeviceInfo,omitempty"`
	DOID              string                 `protobuf:"bytes,3,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Ip                string                 `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip,omitempty"`
	ProviderName      string                 `protobuf:"bytes,5,opt,name=providerName,proto3" json:"providerName,omitempty"`
	ThirdDeviceId     string                 `protobuf:"bytes,6,opt,name=thirdDeviceId,proto3" json:"thirdDeviceId,omitempty"`
	ThirdDeviceIdData *ThirdDeviceIdData     `protobuf:"bytes,7,opt,name=thirdDeviceIdData,proto3" json:"thirdDeviceIdData,omitempty"`
	DeviceId          string                 `protobuf:"bytes,8,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateDOID) Reset() {
	*x = CreateDOID{}
	mi := &file_proto_sdk_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDOID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDOID) ProtoMessage() {}

func (x *CreateDOID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDOID.ProtoReflect.Descriptor instead.
func (*CreateDOID) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{26}
}

func (x *CreateDOID) GetSDKDeviceInfo() *SDKDeviceInfo {
	if x != nil {
		return x.SDKDeviceInfo
	}
	return nil
}

func (x *CreateDOID) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *CreateDOID) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateDOID) GetProviderName() string {
	if x != nil {
		return x.ProviderName
	}
	return ""
}

func (x *CreateDOID) GetThirdDeviceId() string {
	if x != nil {
		return x.ThirdDeviceId
	}
	return ""
}

func (x *CreateDOID) GetThirdDeviceIdData() *ThirdDeviceIdData {
	if x != nil {
		return x.ThirdDeviceIdData
	}
	return nil
}

func (x *CreateDOID) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CreateSdKDOID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DOID          string                 `protobuf:"bytes,1,opt,name=DOID,proto3" json:"DOID,omitempty"`
	SDKDOID       string                 `protobuf:"bytes,2,opt,name=SDKDOID,proto3" json:"SDKDOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSdKDOID) Reset() {
	*x = CreateSdKDOID{}
	mi := &file_proto_sdk_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSdKDOID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSdKDOID) ProtoMessage() {}

func (x *CreateSdKDOID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSdKDOID.ProtoReflect.Descriptor instead.
func (*CreateSdKDOID) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{27}
}

func (x *CreateSdKDOID) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *CreateSdKDOID) GetSDKDOID() string {
	if x != nil {
		return x.SDKDOID
	}
	return ""
}

type ShumeiRiskResponse struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Code         uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message      string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ProfileExist uint32                 `protobuf:"varint,3,opt,name=profileExist,proto3" json:"profileExist,omitempty"`
	RequestId    string                 `protobuf:"bytes,4,opt,name=requestId,proto3" json:"requestId,omitempty"`
	// Tokenlabels tokenLabels = 5;
	DeviceLabels  *ShumeiRiskResponse_Devicelabels `protobuf:"bytes,6,opt,name=deviceLabels,proto3" json:"deviceLabels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse) Reset() {
	*x = ShumeiRiskResponse{}
	mi := &file_proto_sdk_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse) ProtoMessage() {}

func (x *ShumeiRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28}
}

func (x *ShumeiRiskResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiRiskResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ShumeiRiskResponse) GetProfileExist() uint32 {
	if x != nil {
		return x.ProfileExist
	}
	return 0
}

func (x *ShumeiRiskResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiRiskResponse) GetDeviceLabels() *ShumeiRiskResponse_Devicelabels {
	if x != nil {
		return x.DeviceLabels
	}
	return nil
}

type NIDCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           string                 `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	ClientId      uint32                 `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DOID          string                 `protobuf:"bytes,6,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Sig           string                 `protobuf:"bytes,7,opt,name=sig,proto3" json:"sig,omitempty"`
	Timestamp     int64                  `protobuf:"varint,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NIDCheckRequest) Reset() {
	*x = NIDCheckRequest{}
	mi := &file_proto_sdk_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NIDCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NIDCheckRequest) ProtoMessage() {}

func (x *NIDCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NIDCheckRequest.ProtoReflect.Descriptor instead.
func (*NIDCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{29}
}

func (x *NIDCheckRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *NIDCheckRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NIDCheckRequest) GetClientId() uint32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *NIDCheckRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *NIDCheckRequest) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *NIDCheckRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type NIDCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RiskScore     int64                  `protobuf:"varint,1,opt,name=risk_score,json=riskScore,proto3" json:"risk_score"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NIDCheckResponse) Reset() {
	*x = NIDCheckResponse{}
	mi := &file_proto_sdk_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NIDCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NIDCheckResponse) ProtoMessage() {}

func (x *NIDCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NIDCheckResponse.ProtoReflect.Descriptor instead.
func (*NIDCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{30}
}

func (x *NIDCheckResponse) GetRiskScore() int64 {
	if x != nil {
		return x.RiskScore
	}
	return 0
}

type FaceCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nid           string                 `protobuf:"bytes,1,opt,name=nid,proto3" json:"nid,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Scene         string                 `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"`
	Vendor        string                 `protobuf:"bytes,4,opt,name=vendor,proto3" json:"vendor,omitempty"`
	Extra         string                 `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	ClientId      int64                  `protobuf:"varint,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DOID          string                 `protobuf:"bytes,7,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceCodeRequest) Reset() {
	*x = FaceCodeRequest{}
	mi := &file_proto_sdk_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceCodeRequest) ProtoMessage() {}

func (x *FaceCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceCodeRequest.ProtoReflect.Descriptor instead.
func (*FaceCodeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{31}
}

func (x *FaceCodeRequest) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *FaceCodeRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *FaceCodeRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *FaceCodeRequest) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FaceCodeRequest) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *FaceCodeRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FaceCodeRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type FaceCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Vendor        string                 `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	Service       *FaceServiceData       `protobuf:"bytes,3,opt,name=service,proto3" json:"service"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceCodeResponse) Reset() {
	*x = FaceCodeResponse{}
	mi := &file_proto_sdk_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceCodeResponse) ProtoMessage() {}

func (x *FaceCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceCodeResponse.ProtoReflect.Descriptor instead.
func (*FaceCodeResponse) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{32}
}

func (x *FaceCodeResponse) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FaceCodeResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FaceCodeResponse) GetService() *FaceServiceData {
	if x != nil {
		return x.Service
	}
	return nil
}

type FaceCodeVal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       string                 `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Nid           string                 `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"`
	Scene         string                 `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"`
	Vendor        string                 `protobuf:"bytes,4,opt,name=vendor,proto3" json:"vendor,omitempty"`
	IdCard        string                 `protobuf:"bytes,6,opt,name=id_card,json=idCard,proto3" json:"id_card,omitempty"`
	RealName      string                 `protobuf:"bytes,7,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceCodeVal) Reset() {
	*x = FaceCodeVal{}
	mi := &file_proto_sdk_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceCodeVal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceCodeVal) ProtoMessage() {}

func (x *FaceCodeVal) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceCodeVal.ProtoReflect.Descriptor instead.
func (*FaceCodeVal) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{33}
}

func (x *FaceCodeVal) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *FaceCodeVal) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *FaceCodeVal) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *FaceCodeVal) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FaceCodeVal) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *FaceCodeVal) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

type FaceRdbTokeVal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      int64                  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DOID          string                 `protobuf:"bytes,2,opt,name=DOID,proto3" json:"DOID,omitempty"`
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Nid           string                 `protobuf:"bytes,4,opt,name=nid,proto3" json:"nid,omitempty"`
	Scene         string                 `protobuf:"bytes,5,opt,name=scene,proto3" json:"scene,omitempty"`
	IdCard        string                 `protobuf:"bytes,6,opt,name=id_card,json=idCard,proto3" json:"id_card,omitempty"`
	RealName      string                 `protobuf:"bytes,7,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceRdbTokeVal) Reset() {
	*x = FaceRdbTokeVal{}
	mi := &file_proto_sdk_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceRdbTokeVal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceRdbTokeVal) ProtoMessage() {}

func (x *FaceRdbTokeVal) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceRdbTokeVal.ProtoReflect.Descriptor instead.
func (*FaceRdbTokeVal) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{34}
}

func (x *FaceRdbTokeVal) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FaceRdbTokeVal) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

func (x *FaceRdbTokeVal) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FaceRdbTokeVal) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *FaceRdbTokeVal) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *FaceRdbTokeVal) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *FaceRdbTokeVal) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

type ShumeiRiskResponse_MachineAccountRisk struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	BMachineControlTokenid       uint32                 `protobuf:"varint,1,opt,name=b_machine_control_tokenid,json=bMachineControlTokenid,proto3" json:"b_machine_control_tokenid,omitempty"`
	BMachineControlTokenidLastTs uint64                 `protobuf:"varint,2,opt,name=b_machine_control_tokenid_last_ts,json=bMachineControlTokenidLastTs,proto3" json:"b_machine_control_tokenid_last_ts,omitempty"`
	BOfferWallTokenid            uint32                 `protobuf:"varint,3,opt,name=b_offer_wall_tokenid,json=bOfferWallTokenid,proto3" json:"b_offer_wall_tokenid,omitempty"`
	BOfferWallTokenidLastTs      uint64                 `protobuf:"varint,4,opt,name=b_offer_wall_tokenid_last_ts,json=bOfferWallTokenidLastTs,proto3" json:"b_offer_wall_tokenid_last_ts,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_MachineAccountRisk) Reset() {
	*x = ShumeiRiskResponse_MachineAccountRisk{}
	mi := &file_proto_sdk_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_MachineAccountRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_MachineAccountRisk) ProtoMessage() {}

func (x *ShumeiRiskResponse_MachineAccountRisk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_MachineAccountRisk.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_MachineAccountRisk) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 0}
}

func (x *ShumeiRiskResponse_MachineAccountRisk) GetBMachineControlTokenid() uint32 {
	if x != nil {
		return x.BMachineControlTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_MachineAccountRisk) GetBMachineControlTokenidLastTs() uint64 {
	if x != nil {
		return x.BMachineControlTokenidLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_MachineAccountRisk) GetBOfferWallTokenid() uint32 {
	if x != nil {
		return x.BOfferWallTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_MachineAccountRisk) GetBOfferWallTokenidLastTs() uint64 {
	if x != nil {
		return x.BOfferWallTokenidLastTs
	}
	return 0
}

type ShumeiRiskResponse_UgcAccountRisk struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	BPoliticsRiskTokenid        uint32                 `protobuf:"varint,1,opt,name=b_politics_risk_tokenid,json=bPoliticsRiskTokenid,proto3" json:"b_politics_risk_tokenid,omitempty"`
	BPoliticsRiskTokenidLastTs  uint64                 `protobuf:"varint,2,opt,name=b_politics_risk_tokenid_last_ts,json=bPoliticsRiskTokenidLastTs,proto3" json:"b_politics_risk_tokenid_last_ts,omitempty"`
	BSexyRiskTokenid            uint32                 `protobuf:"varint,3,opt,name=b_sexy_risk_tokenid,json=bSexyRiskTokenid,proto3" json:"b_sexy_risk_tokenid,omitempty"`
	BSexyRiskTokenidLastTs      uint64                 `protobuf:"varint,4,opt,name=b_sexy_risk_tokenid_last_ts,json=bSexyRiskTokenidLastTs,proto3" json:"b_sexy_risk_tokenid_last_ts,omitempty"`
	BAdvertiseRiskTokenid       uint32                 `protobuf:"varint,5,opt,name=b_advertise_risk_tokenid,json=bAdvertiseRiskTokenid,proto3" json:"b_advertise_risk_tokenid,omitempty"`
	BAdvertiseRiskTokenidLastTs uint64                 `protobuf:"varint,6,opt,name=b_advertise_risk_tokenid_last_ts,json=bAdvertiseRiskTokenidLastTs,proto3" json:"b_advertise_risk_tokenid_last_ts,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_UgcAccountRisk) Reset() {
	*x = ShumeiRiskResponse_UgcAccountRisk{}
	mi := &file_proto_sdk_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_UgcAccountRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_UgcAccountRisk) ProtoMessage() {}

func (x *ShumeiRiskResponse_UgcAccountRisk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_UgcAccountRisk.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_UgcAccountRisk) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 1}
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBPoliticsRiskTokenid() uint32 {
	if x != nil {
		return x.BPoliticsRiskTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBPoliticsRiskTokenidLastTs() uint64 {
	if x != nil {
		return x.BPoliticsRiskTokenidLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBSexyRiskTokenid() uint32 {
	if x != nil {
		return x.BSexyRiskTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBSexyRiskTokenidLastTs() uint64 {
	if x != nil {
		return x.BSexyRiskTokenidLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBAdvertiseRiskTokenid() uint32 {
	if x != nil {
		return x.BAdvertiseRiskTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_UgcAccountRisk) GetBAdvertiseRiskTokenidLastTs() uint64 {
	if x != nil {
		return x.BAdvertiseRiskTokenidLastTs
	}
	return 0
}

type ShumeiRiskResponse_SceneAccountRisk struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	IToutRiskTokenid       uint32                 `protobuf:"varint,1,opt,name=i_tout_risk_tokenid,json=iToutRiskTokenid,proto3" json:"i_tout_risk_tokenid,omitempty"`
	IToutRiskTokenidLastTs uint64                 `protobuf:"varint,2,opt,name=i_tout_risk_tokenid_last_ts,json=iToutRiskTokenidLastTs,proto3" json:"i_tout_risk_tokenid_last_ts,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_SceneAccountRisk) Reset() {
	*x = ShumeiRiskResponse_SceneAccountRisk{}
	mi := &file_proto_sdk_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_SceneAccountRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_SceneAccountRisk) ProtoMessage() {}

func (x *ShumeiRiskResponse_SceneAccountRisk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_SceneAccountRisk.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_SceneAccountRisk) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 2}
}

func (x *ShumeiRiskResponse_SceneAccountRisk) GetIToutRiskTokenid() uint32 {
	if x != nil {
		return x.IToutRiskTokenid
	}
	return 0
}

func (x *ShumeiRiskResponse_SceneAccountRisk) GetIToutRiskTokenidLastTs() uint64 {
	if x != nil {
		return x.IToutRiskTokenidLastTs
	}
	return 0
}

type ShumeiRiskResponse_AccountActiveInfo struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	ITokenidFirstActiveTimestamp uint64                 `protobuf:"varint,1,opt,name=i_tokenid_first_active_timestamp,json=iTokenidFirstActiveTimestamp,proto3" json:"i_tokenid_first_active_timestamp,omitempty"`
	ITokenidActiveDays_7D        uint32                 `protobuf:"varint,2,opt,name=i_tokenid_active_days_7d,json=iTokenidActiveDays7d,proto3" json:"i_tokenid_active_days_7d,omitempty"`
	ITokenidActiveDays_4W        uint32                 `protobuf:"varint,3,opt,name=i_tokenid_active_days_4w,json=iTokenidActiveDays4w,proto3" json:"i_tokenid_active_days_4w,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_AccountActiveInfo) Reset() {
	*x = ShumeiRiskResponse_AccountActiveInfo{}
	mi := &file_proto_sdk_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_AccountActiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_AccountActiveInfo) ProtoMessage() {}

func (x *ShumeiRiskResponse_AccountActiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_AccountActiveInfo.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_AccountActiveInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 3}
}

func (x *ShumeiRiskResponse_AccountActiveInfo) GetITokenidFirstActiveTimestamp() uint64 {
	if x != nil {
		return x.ITokenidFirstActiveTimestamp
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountActiveInfo) GetITokenidActiveDays_7D() uint32 {
	if x != nil {
		return x.ITokenidActiveDays_7D
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountActiveInfo) GetITokenidActiveDays_4W() uint32 {
	if x != nil {
		return x.ITokenidActiveDays_4W
	}
	return 0
}

type ShumeiRiskResponse_AccountFreqInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ITokenidLoginCnt_1D uint32                 `protobuf:"varint,1,opt,name=i_tokenid_login_cnt_1d,json=iTokenidLoginCnt1d,proto3" json:"i_tokenid_login_cnt_1d,omitempty"`
	ITokenidLoginCnt_7D uint32                 `protobuf:"varint,2,opt,name=i_tokenid_login_cnt_7d,json=iTokenidLoginCnt7d,proto3" json:"i_tokenid_login_cnt_7d,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_AccountFreqInfo) Reset() {
	*x = ShumeiRiskResponse_AccountFreqInfo{}
	mi := &file_proto_sdk_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_AccountFreqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_AccountFreqInfo) ProtoMessage() {}

func (x *ShumeiRiskResponse_AccountFreqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_AccountFreqInfo.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_AccountFreqInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 4}
}

func (x *ShumeiRiskResponse_AccountFreqInfo) GetITokenidLoginCnt_1D() uint32 {
	if x != nil {
		return x.ITokenidLoginCnt_1D
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountFreqInfo) GetITokenidLoginCnt_7D() uint32 {
	if x != nil {
		return x.ITokenidLoginCnt_7D
	}
	return 0
}

type ShumeiRiskResponse_AccountRelateInfo struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	ITokenidRelateSmidCnt_1D   uint32                 `protobuf:"varint,1,opt,name=i_tokenid_relate_smid_cnt_1d,json=iTokenidRelateSmidCnt1d,proto3" json:"i_tokenid_relate_smid_cnt_1d,omitempty"`
	ITokenidRelateSmidCnt_7D   uint32                 `protobuf:"varint,2,opt,name=i_tokenid_relate_smid_cnt_7d,json=iTokenidRelateSmidCnt7d,proto3" json:"i_tokenid_relate_smid_cnt_7d,omitempty"`
	ITokenidRelateIpCityCnt_1D uint32                 `protobuf:"varint,3,opt,name=i_tokenid_relate_ip_city_cnt_1d,json=iTokenidRelateIpCityCnt1d,proto3" json:"i_tokenid_relate_ip_city_cnt_1d,omitempty"`
	ITokenidRelateIpCityCnt_7D uint32                 `protobuf:"varint,4,opt,name=i_tokenid_relate_ip_city_cnt_7d,json=iTokenidRelateIpCityCnt7d,proto3" json:"i_tokenid_relate_ip_city_cnt_7d,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_AccountRelateInfo) Reset() {
	*x = ShumeiRiskResponse_AccountRelateInfo{}
	mi := &file_proto_sdk_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_AccountRelateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_AccountRelateInfo) ProtoMessage() {}

func (x *ShumeiRiskResponse_AccountRelateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_AccountRelateInfo.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_AccountRelateInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 5}
}

func (x *ShumeiRiskResponse_AccountRelateInfo) GetITokenidRelateSmidCnt_1D() uint32 {
	if x != nil {
		return x.ITokenidRelateSmidCnt_1D
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountRelateInfo) GetITokenidRelateSmidCnt_7D() uint32 {
	if x != nil {
		return x.ITokenidRelateSmidCnt_7D
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountRelateInfo) GetITokenidRelateIpCityCnt_1D() uint32 {
	if x != nil {
		return x.ITokenidRelateIpCityCnt_1D
	}
	return 0
}

func (x *ShumeiRiskResponse_AccountRelateInfo) GetITokenidRelateIpCityCnt_7D() uint32 {
	if x != nil {
		return x.ITokenidRelateIpCityCnt_7D
	}
	return 0
}

type ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Smid          string                 `protobuf:"bytes,1,opt,name=smid,proto3" json:"smid,omitempty"`
	Days          string                 `protobuf:"bytes,2,opt,name=days,proto3" json:"days,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) Reset() {
	*x = ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W{}
	mi := &file_proto_sdk_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) ProtoMessage() {}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 6}
}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) GetSmid() string {
	if x != nil {
		return x.Smid
	}
	return ""
}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) GetDays() string {
	if x != nil {
		return x.Days
	}
	return ""
}

type ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	City          string                 `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	Days          string                 `protobuf:"bytes,2,opt,name=days,proto3" json:"days,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) Reset() {
	*x = ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W{}
	mi := &file_proto_sdk_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) ProtoMessage() {}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 7}
}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) GetDays() string {
	if x != nil {
		return x.Days
	}
	return ""
}

type ShumeiRiskResponse_AccountCommonInfo struct {
	state                          protoimpl.MessageState                               `protogen:"open.v1"`
	STokenidRelateSmidInfoMap_4W   []*ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W   `protobuf:"bytes,1,rep,name=s_tokenid_relate_smid_info_map_4w,json=sTokenidRelateSmidInfoMap4w,proto3" json:"s_tokenid_relate_smid_info_map_4w,omitempty"`
	STokenidRelateIpCityInfoMap_4W []*ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W `protobuf:"bytes,2,rep,name=s_tokenid_relate_ip_city_info_map_4w,json=sTokenidRelateIpCityInfoMap4w,proto3" json:"s_tokenid_relate_ip_city_info_map_4w,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_AccountCommonInfo) Reset() {
	*x = ShumeiRiskResponse_AccountCommonInfo{}
	mi := &file_proto_sdk_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_AccountCommonInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_AccountCommonInfo) ProtoMessage() {}

func (x *ShumeiRiskResponse_AccountCommonInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_AccountCommonInfo.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_AccountCommonInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 8}
}

func (x *ShumeiRiskResponse_AccountCommonInfo) GetSTokenidRelateSmidInfoMap_4W() []*ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W {
	if x != nil {
		return x.STokenidRelateSmidInfoMap_4W
	}
	return nil
}

func (x *ShumeiRiskResponse_AccountCommonInfo) GetSTokenidRelateIpCityInfoMap_4W() []*ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W {
	if x != nil {
		return x.STokenidRelateIpCityInfoMap_4W
	}
	return nil
}

type ShumeiRiskResponse_Tokenlabels struct {
	state              protoimpl.MessageState                 `protogen:"open.v1"`
	MachineAccountRisk *ShumeiRiskResponse_MachineAccountRisk `protobuf:"bytes,1,opt,name=machine_account_risk,json=machineAccountRisk,proto3" json:"machine_account_risk,omitempty"`
	UGCAccountRisk     *ShumeiRiskResponse_UgcAccountRisk     `protobuf:"bytes,2,opt,name=UGC_account_risk,json=UGCAccountRisk,proto3" json:"UGC_account_risk,omitempty"`
	SceneAccountRisk   *ShumeiRiskResponse_SceneAccountRisk   `protobuf:"bytes,3,opt,name=scene_account_risk,json=sceneAccountRisk,proto3" json:"scene_account_risk,omitempty"`
	AccountActiveInfo  *ShumeiRiskResponse_AccountActiveInfo  `protobuf:"bytes,4,opt,name=account_active_info,json=accountActiveInfo,proto3" json:"account_active_info,omitempty"`
	AccountFreqInfo    *ShumeiRiskResponse_AccountFreqInfo    `protobuf:"bytes,5,opt,name=account_freq_info,json=accountFreqInfo,proto3" json:"account_freq_info,omitempty"`
	AccountRelateInfo  *ShumeiRiskResponse_AccountRelateInfo  `protobuf:"bytes,6,opt,name=account_relate_info,json=accountRelateInfo,proto3" json:"account_relate_info,omitempty"`
	AccountCommonInfo  *ShumeiRiskResponse_AccountCommonInfo  `protobuf:"bytes,7,opt,name=account_common_info,json=accountCommonInfo,proto3" json:"account_common_info,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Tokenlabels) Reset() {
	*x = ShumeiRiskResponse_Tokenlabels{}
	mi := &file_proto_sdk_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Tokenlabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Tokenlabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_Tokenlabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Tokenlabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Tokenlabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 9}
}

func (x *ShumeiRiskResponse_Tokenlabels) GetMachineAccountRisk() *ShumeiRiskResponse_MachineAccountRisk {
	if x != nil {
		return x.MachineAccountRisk
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetUGCAccountRisk() *ShumeiRiskResponse_UgcAccountRisk {
	if x != nil {
		return x.UGCAccountRisk
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetSceneAccountRisk() *ShumeiRiskResponse_SceneAccountRisk {
	if x != nil {
		return x.SceneAccountRisk
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetAccountActiveInfo() *ShumeiRiskResponse_AccountActiveInfo {
	if x != nil {
		return x.AccountActiveInfo
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetAccountFreqInfo() *ShumeiRiskResponse_AccountFreqInfo {
	if x != nil {
		return x.AccountFreqInfo
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetAccountRelateInfo() *ShumeiRiskResponse_AccountRelateInfo {
	if x != nil {
		return x.AccountRelateInfo
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) GetAccountCommonInfo() *ShumeiRiskResponse_AccountCommonInfo {
	if x != nil {
		return x.AccountCommonInfo
	}
	return nil
}

type ShumeiRiskResponse_Other struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BMismatch       uint32                 `protobuf:"varint,1,opt,name=b_mismatch,json=bMismatch,proto3" json:"b_mismatch,omitempty"`
	BMismatchLastTs uint64                 `protobuf:"varint,2,opt,name=b_mismatch_last_ts,json=bMismatchLastTs,proto3" json:"b_mismatch_last_ts,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Other) Reset() {
	*x = ShumeiRiskResponse_Other{}
	mi := &file_proto_sdk_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Other) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Other) ProtoMessage() {}

func (x *ShumeiRiskResponse_Other) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Other.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Other) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 10}
}

func (x *ShumeiRiskResponse_Other) GetBMismatch() uint32 {
	if x != nil {
		return x.BMismatch
	}
	return 0
}

func (x *ShumeiRiskResponse_Other) GetBMismatchLastTs() uint64 {
	if x != nil {
		return x.BMismatchLastTs
	}
	return 0
}

type ShumeiRiskResponse_FakeDevice struct {
	state                   protoimpl.MessageState    `protogen:"open.v1"`
	BPcEmulator             uint32                    `protobuf:"varint,1,opt,name=b_pc_emulator,json=bPcEmulator,proto3" json:"b_pc_emulator,omitempty"`
	BPcEmulatorLastTs       uint64                    `protobuf:"varint,2,opt,name=b_pc_emulator_last_ts,json=bPcEmulatorLastTs,proto3" json:"b_pc_emulator_last_ts,omitempty"`
	BPcEmulatorPcId         string                    `protobuf:"bytes,3,opt,name=b_pc_emulator_pc_id,json=bPcEmulatorPcId,proto3" json:"b_pc_emulator_pc_id,omitempty"`
	BCloudDevice            uint32                    `protobuf:"varint,4,opt,name=b_cloud_device,json=bCloudDevice,proto3" json:"b_cloud_device,omitempty"`
	BCloudDeviceLastTs      uint64                    `protobuf:"varint,5,opt,name=b_cloud_device_last_ts,json=bCloudDeviceLastTs,proto3" json:"b_cloud_device_last_ts,omitempty"`
	BAltered                uint32                    `protobuf:"varint,6,opt,name=b_altered,json=bAltered,proto3" json:"b_altered,omitempty"`
	BAlteredLastTs          uint64                    `protobuf:"varint,7,opt,name=b_altered_last_ts,json=bAlteredLastTs,proto3" json:"b_altered_last_ts,omitempty"`
	BMultiBoxing            uint32                    `protobuf:"varint,8,opt,name=b_multi_boxing,json=bMultiBoxing,proto3" json:"b_multi_boxing,omitempty"`
	BMultiBoxingLastTs      uint64                    `protobuf:"varint,9,opt,name=b_multi_boxing_last_ts,json=bMultiBoxingLastTs,proto3" json:"b_multi_boxing_last_ts,omitempty"`
	BMultiBoxingByOs        uint32                    `protobuf:"varint,10,opt,name=b_multi_boxing_by_os,json=bMultiBoxingByOs,proto3" json:"b_multi_boxing_by_os,omitempty"`
	BMultiBoxingByOsLastTs  uint64                    `protobuf:"varint,11,opt,name=b_multi_boxing_by_os_last_ts,json=bMultiBoxingByOsLastTs,proto3" json:"b_multi_boxing_by_os_last_ts,omitempty"`
	BMultiBoxingByApp       uint32                    `protobuf:"varint,12,opt,name=b_multi_boxing_by_app,json=bMultiBoxingByApp,proto3" json:"b_multi_boxing_by_app,omitempty"`
	BMultiBoxingByAppLastTs uint64                    `protobuf:"varint,13,opt,name=b_multi_boxing_by_app_last_ts,json=bMultiBoxingByAppLastTs,proto3" json:"b_multi_boxing_by_app_last_ts,omitempty"`
	BFaker                  uint32                    `protobuf:"varint,14,opt,name=b_faker,json=bFaker,proto3" json:"b_faker,omitempty"`
	BFakerLastTs            uint64                    `protobuf:"varint,15,opt,name=b_faker_last_ts,json=bFakerLastTs,proto3" json:"b_faker_last_ts,omitempty"`
	BFarmer                 uint32                    `protobuf:"varint,16,opt,name=b_farmer,json=bFarmer,proto3" json:"b_farmer,omitempty"`
	BFarmerLastTs           uint64                    `protobuf:"varint,17,opt,name=b_farmer_last_ts,json=bFarmerLastTs,proto3" json:"b_farmer_last_ts,omitempty"`
	BOfferwall              uint32                    `protobuf:"varint,18,opt,name=b_offerwall,json=bOfferwall,proto3" json:"b_offerwall,omitempty"`
	BOfferwallLastTs        uint64                    `protobuf:"varint,19,opt,name=b_offerwall_last_ts,json=bOfferwallLastTs,proto3" json:"b_offerwall_last_ts,omitempty"`
	Other                   *ShumeiRiskResponse_Other `protobuf:"bytes,20,opt,name=other,proto3" json:"other,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_FakeDevice) Reset() {
	*x = ShumeiRiskResponse_FakeDevice{}
	mi := &file_proto_sdk_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_FakeDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_FakeDevice) ProtoMessage() {}

func (x *ShumeiRiskResponse_FakeDevice) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_FakeDevice.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_FakeDevice) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 11}
}

func (x *ShumeiRiskResponse_FakeDevice) GetBPcEmulator() uint32 {
	if x != nil {
		return x.BPcEmulator
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBPcEmulatorLastTs() uint64 {
	if x != nil {
		return x.BPcEmulatorLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBPcEmulatorPcId() string {
	if x != nil {
		return x.BPcEmulatorPcId
	}
	return ""
}

func (x *ShumeiRiskResponse_FakeDevice) GetBCloudDevice() uint32 {
	if x != nil {
		return x.BCloudDevice
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBCloudDeviceLastTs() uint64 {
	if x != nil {
		return x.BCloudDeviceLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBAltered() uint32 {
	if x != nil {
		return x.BAltered
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBAlteredLastTs() uint64 {
	if x != nil {
		return x.BAlteredLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxing() uint32 {
	if x != nil {
		return x.BMultiBoxing
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxingLastTs() uint64 {
	if x != nil {
		return x.BMultiBoxingLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxingByOs() uint32 {
	if x != nil {
		return x.BMultiBoxingByOs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxingByOsLastTs() uint64 {
	if x != nil {
		return x.BMultiBoxingByOsLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxingByApp() uint32 {
	if x != nil {
		return x.BMultiBoxingByApp
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBMultiBoxingByAppLastTs() uint64 {
	if x != nil {
		return x.BMultiBoxingByAppLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBFaker() uint32 {
	if x != nil {
		return x.BFaker
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBFakerLastTs() uint64 {
	if x != nil {
		return x.BFakerLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBFarmer() uint32 {
	if x != nil {
		return x.BFarmer
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBFarmerLastTs() uint64 {
	if x != nil {
		return x.BFarmerLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBOfferwall() uint32 {
	if x != nil {
		return x.BOfferwall
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetBOfferwallLastTs() uint64 {
	if x != nil {
		return x.BOfferwallLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_FakeDevice) GetOther() *ShumeiRiskResponse_Other {
	if x != nil {
		return x.Other
	}
	return nil
}

type ShumeiRiskResponse_DeviceSuspiciousLabels struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	BRoot                    uint32                 `protobuf:"varint,1,opt,name=b_root,json=bRoot,proto3" json:"b_root,omitempty"`
	BRootLastTs              uint64                 `protobuf:"varint,2,opt,name=b_root_last_ts,json=bRootLastTs,proto3" json:"b_root_last_ts,omitempty"`
	BSim                     uint32                 `protobuf:"varint,3,opt,name=b_sim,json=bSim,proto3" json:"b_sim,omitempty"`
	BSimLastTs               uint64                 `protobuf:"varint,4,opt,name=b_sim_last_ts,json=bSimLastTs,proto3" json:"b_sim_last_ts,omitempty"`
	BDebuggable              uint32                 `protobuf:"varint,5,opt,name=b_debuggable,json=bDebuggable,proto3" json:"b_debuggable,omitempty"`
	BDebuggableLastTs        uint64                 `protobuf:"varint,6,opt,name=b_debuggable_last_ts,json=bDebuggableLastTs,proto3" json:"b_debuggable_last_ts,omitempty"`
	BVpn                     uint32                 `protobuf:"varint,7,opt,name=b_vpn,json=bVpn,proto3" json:"b_vpn,omitempty"`
	BVpnLastTs               uint64                 `protobuf:"varint,8,opt,name=b_vpn_last_ts,json=bVpnLastTs,proto3" json:"b_vpn_last_ts,omitempty"`
	BMonkeyApps              uint32                 `protobuf:"varint,9,opt,name=b_monkey_apps,json=bMonkeyApps,proto3" json:"b_monkey_apps,omitempty"`
	BMonkeyAppsLastTs        uint64                 `protobuf:"varint,10,opt,name=b_monkey_apps_last_ts,json=bMonkeyAppsLastTs,proto3" json:"b_monkey_apps_last_ts,omitempty"`
	BAcc                     uint32                 `protobuf:"varint,11,opt,name=b_acc,json=bAcc,proto3" json:"b_acc,omitempty"`
	BAccLastTs               uint64                 `protobuf:"varint,12,opt,name=b_acc_last_ts,json=bAccLastTs,proto3" json:"b_acc_last_ts,omitempty"`
	BMultiBoxingApps         uint32                 `protobuf:"varint,13,opt,name=b_multi_boxing_apps,json=bMultiBoxingApps,proto3" json:"b_multi_boxing_apps,omitempty"`
	BMultiBoxingAppsLastTs   uint64                 `protobuf:"varint,14,opt,name=b_multi_boxing_apps_last_ts,json=bMultiBoxingAppsLastTs,proto3" json:"b_multi_boxing_apps_last_ts,omitempty"`
	BHook                    uint32                 `protobuf:"varint,15,opt,name=b_hook,json=bHook,proto3" json:"b_hook,omitempty"`
	BHookLastTs              uint64                 `protobuf:"varint,16,opt,name=b_hook_last_ts,json=bHookLastTs,proto3" json:"b_hook_last_ts,omitempty"`
	BVpnApps                 uint32                 `protobuf:"varint,17,opt,name=b_vpn_apps,json=bVpnApps,proto3" json:"b_vpn_apps,omitempty"`
	BVpnAppsLastTs           uint64                 `protobuf:"varint,18,opt,name=b_vpn_apps_last_ts,json=bVpnAppsLastTs,proto3" json:"b_vpn_apps_last_ts,omitempty"`
	BManufacture             uint32                 `protobuf:"varint,19,opt,name=b_manufacture,json=bManufacture,proto3" json:"b_manufacture,omitempty"`
	BManufactureLastTs       uint64                 `protobuf:"varint,20,opt,name=b_manufacture_last_ts,json=bManufactureLastTs,proto3" json:"b_manufacture_last_ts,omitempty"`
	BIcloud                  uint32                 `protobuf:"varint,21,opt,name=b_icloud,json=bIcloud,proto3" json:"b_icloud,omitempty"`
	BIcloudLastTs            uint64                 `protobuf:"varint,22,opt,name=b_icloud_last_ts,json=bIcloudLastTs,proto3" json:"b_icloud_last_ts,omitempty"`
	BWxCode                  uint32                 `protobuf:"varint,23,opt,name=b_wx_code,json=bWxCode,proto3" json:"b_wx_code,omitempty"`
	BWxCodeLastTs            uint64                 `protobuf:"varint,24,opt,name=b_wx_code_last_ts,json=bWxCodeLastTs,proto3" json:"b_wx_code_last_ts,omitempty"`
	BSmsCode                 uint32                 `protobuf:"varint,25,opt,name=b_sms_code,json=bSmsCode,proto3" json:"b_sms_code,omitempty"`
	BSmsCodeLastTs           uint64                 `protobuf:"varint,26,opt,name=b_sms_code_last_ts,json=bSmsCodeLastTs,proto3" json:"b_sms_code_last_ts,omitempty"`
	BLowOsver                uint32                 `protobuf:"varint,27,opt,name=b_low_osver,json=bLowOsver,proto3" json:"b_low_osver,omitempty"`
	BLowOsverLastTs          uint64                 `protobuf:"varint,28,opt,name=b_low_osver_last_ts,json=bLowOsverLastTs,proto3" json:"b_low_osver_last_ts,omitempty"`
	BRemoteControlApps       uint32                 `protobuf:"varint,29,opt,name=b_remote_control_apps,json=bRemoteControlApps,proto3" json:"b_remote_control_apps,omitempty"`
	BRemoteControlAppsLastTs uint64                 `protobuf:"varint,30,opt,name=b_remote_control_apps_last_ts,json=bRemoteControlAppsLastTs,proto3" json:"b_remote_control_apps_last_ts,omitempty"`
	BRepackage               uint32                 `protobuf:"varint,31,opt,name=b_repackage,json=bRepackage,proto3" json:"b_repackage,omitempty"`
	BRepackageLastTs         uint64                 `protobuf:"varint,32,opt,name=b_repackage_last_ts,json=bRepackageLastTs,proto3" json:"b_repackage_last_ts,omitempty"`
	BReset                   uint32                 `protobuf:"varint,33,opt,name=b_reset,json=bReset,proto3" json:"b_reset,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) Reset() {
	*x = ShumeiRiskResponse_DeviceSuspiciousLabels{}
	mi := &file_proto_sdk_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_DeviceSuspiciousLabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_DeviceSuspiciousLabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_DeviceSuspiciousLabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 12}
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRoot() uint32 {
	if x != nil {
		return x.BRoot
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRootLastTs() uint64 {
	if x != nil {
		return x.BRootLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBSim() uint32 {
	if x != nil {
		return x.BSim
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBSimLastTs() uint64 {
	if x != nil {
		return x.BSimLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBDebuggable() uint32 {
	if x != nil {
		return x.BDebuggable
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBDebuggableLastTs() uint64 {
	if x != nil {
		return x.BDebuggableLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBVpn() uint32 {
	if x != nil {
		return x.BVpn
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBVpnLastTs() uint64 {
	if x != nil {
		return x.BVpnLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBMonkeyApps() uint32 {
	if x != nil {
		return x.BMonkeyApps
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBMonkeyAppsLastTs() uint64 {
	if x != nil {
		return x.BMonkeyAppsLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBAcc() uint32 {
	if x != nil {
		return x.BAcc
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBAccLastTs() uint64 {
	if x != nil {
		return x.BAccLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBMultiBoxingApps() uint32 {
	if x != nil {
		return x.BMultiBoxingApps
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBMultiBoxingAppsLastTs() uint64 {
	if x != nil {
		return x.BMultiBoxingAppsLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBHook() uint32 {
	if x != nil {
		return x.BHook
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBHookLastTs() uint64 {
	if x != nil {
		return x.BHookLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBVpnApps() uint32 {
	if x != nil {
		return x.BVpnApps
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBVpnAppsLastTs() uint64 {
	if x != nil {
		return x.BVpnAppsLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBManufacture() uint32 {
	if x != nil {
		return x.BManufacture
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBManufactureLastTs() uint64 {
	if x != nil {
		return x.BManufactureLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBIcloud() uint32 {
	if x != nil {
		return x.BIcloud
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBIcloudLastTs() uint64 {
	if x != nil {
		return x.BIcloudLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBWxCode() uint32 {
	if x != nil {
		return x.BWxCode
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBWxCodeLastTs() uint64 {
	if x != nil {
		return x.BWxCodeLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBSmsCode() uint32 {
	if x != nil {
		return x.BSmsCode
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBSmsCodeLastTs() uint64 {
	if x != nil {
		return x.BSmsCodeLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBLowOsver() uint32 {
	if x != nil {
		return x.BLowOsver
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBLowOsverLastTs() uint64 {
	if x != nil {
		return x.BLowOsverLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRemoteControlApps() uint32 {
	if x != nil {
		return x.BRemoteControlApps
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRemoteControlAppsLastTs() uint64 {
	if x != nil {
		return x.BRemoteControlAppsLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRepackage() uint32 {
	if x != nil {
		return x.BRepackage
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBRepackageLastTs() uint64 {
	if x != nil {
		return x.BRepackageLastTs
	}
	return 0
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) GetBReset() uint32 {
	if x != nil {
		return x.BReset
	}
	return 0
}

type ShumeiRiskResponse_DeviceActiveInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ISmidBootTimestamp uint64                 `protobuf:"varint,1,opt,name=i_smid_boot_timestamp,json=iSmidBootTimestamp,proto3" json:"i_smid_boot_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_DeviceActiveInfo) Reset() {
	*x = ShumeiRiskResponse_DeviceActiveInfo{}
	mi := &file_proto_sdk_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_DeviceActiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_DeviceActiveInfo) ProtoMessage() {}

func (x *ShumeiRiskResponse_DeviceActiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_DeviceActiveInfo.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_DeviceActiveInfo) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 13}
}

func (x *ShumeiRiskResponse_DeviceActiveInfo) GetISmidBootTimestamp() uint64 {
	if x != nil {
		return x.ISmidBootTimestamp
	}
	return 0
}

type ShumeiRiskResponse_Common struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BMonkeyApps       uint32                 `protobuf:"varint,1,opt,name=b_monkey_apps,json=bMonkeyApps,proto3" json:"b_monkey_apps,omitempty"`
	BMonkeyAppsLastTs uint64                 `protobuf:"varint,2,opt,name=b_monkey_apps_last_ts,json=bMonkeyAppsLastTs,proto3" json:"b_monkey_apps_last_ts,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Common) Reset() {
	*x = ShumeiRiskResponse_Common{}
	mi := &file_proto_sdk_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Common) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Common) ProtoMessage() {}

func (x *ShumeiRiskResponse_Common) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Common.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Common) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 14}
}

func (x *ShumeiRiskResponse_Common) GetBMonkeyApps() uint32 {
	if x != nil {
		return x.BMonkeyApps
	}
	return 0
}

func (x *ShumeiRiskResponse_Common) GetBMonkeyAppsLastTs() uint64 {
	if x != nil {
		return x.BMonkeyAppsLastTs
	}
	return 0
}

type ShumeiRiskResponse_MonkeyGame struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	BMonkeyGameApps       uint32                 `protobuf:"varint,1,opt,name=b_monkey_game_apps,json=bMonkeyGameApps,proto3" json:"b_monkey_game_apps,omitempty"`
	BMonkeyGameAppsLastTs uint64                 `protobuf:"varint,2,opt,name=b_monkey_game_apps_last_ts,json=bMonkeyGameAppsLastTs,proto3" json:"b_monkey_game_apps_last_ts,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_MonkeyGame) Reset() {
	*x = ShumeiRiskResponse_MonkeyGame{}
	mi := &file_proto_sdk_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_MonkeyGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_MonkeyGame) ProtoMessage() {}

func (x *ShumeiRiskResponse_MonkeyGame) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_MonkeyGame.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_MonkeyGame) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 15}
}

func (x *ShumeiRiskResponse_MonkeyGame) GetBMonkeyGameApps() uint32 {
	if x != nil {
		return x.BMonkeyGameApps
	}
	return 0
}

func (x *ShumeiRiskResponse_MonkeyGame) GetBMonkeyGameAppsLastTs() uint64 {
	if x != nil {
		return x.BMonkeyGameAppsLastTs
	}
	return 0
}

type ShumeiRiskResponse_MonkeyRead struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	BMonkeyReadApps       uint32                 `protobuf:"varint,1,opt,name=b_monkey_read_apps,json=bMonkeyReadApps,proto3" json:"b_monkey_read_apps,omitempty"`
	BMonkeyReadAppsLastTs uint64                 `protobuf:"varint,2,opt,name=b_monkey_read_apps_last_ts,json=bMonkeyReadAppsLastTs,proto3" json:"b_monkey_read_apps_last_ts,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_MonkeyRead) Reset() {
	*x = ShumeiRiskResponse_MonkeyRead{}
	mi := &file_proto_sdk_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_MonkeyRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_MonkeyRead) ProtoMessage() {}

func (x *ShumeiRiskResponse_MonkeyRead) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_MonkeyRead.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_MonkeyRead) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 16}
}

func (x *ShumeiRiskResponse_MonkeyRead) GetBMonkeyReadApps() uint32 {
	if x != nil {
		return x.BMonkeyReadApps
	}
	return 0
}

func (x *ShumeiRiskResponse_MonkeyRead) GetBMonkeyReadAppsLastTs() uint64 {
	if x != nil {
		return x.BMonkeyReadAppsLastTs
	}
	return 0
}

type ShumeiRiskResponse_MonkeyDevice struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Common        *ShumeiRiskResponse_Common     `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	MonkeyGame    *ShumeiRiskResponse_MonkeyGame `protobuf:"bytes,2,opt,name=monkey_game,json=monkeyGame,proto3" json:"monkey_game,omitempty"`
	MonkeyRead    *ShumeiRiskResponse_MonkeyRead `protobuf:"bytes,3,opt,name=monkey_read,json=monkeyRead,proto3" json:"monkey_read,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_MonkeyDevice) Reset() {
	*x = ShumeiRiskResponse_MonkeyDevice{}
	mi := &file_proto_sdk_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_MonkeyDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_MonkeyDevice) ProtoMessage() {}

func (x *ShumeiRiskResponse_MonkeyDevice) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_MonkeyDevice.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_MonkeyDevice) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 17}
}

func (x *ShumeiRiskResponse_MonkeyDevice) GetCommon() *ShumeiRiskResponse_Common {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *ShumeiRiskResponse_MonkeyDevice) GetMonkeyGame() *ShumeiRiskResponse_MonkeyGame {
	if x != nil {
		return x.MonkeyGame
	}
	return nil
}

func (x *ShumeiRiskResponse_MonkeyDevice) GetMonkeyRead() *ShumeiRiskResponse_MonkeyRead {
	if x != nil {
		return x.MonkeyRead
	}
	return nil
}

type ShumeiRiskResponse_Devicelabels struct {
	state                  protoimpl.MessageState                     `protogen:"open.v1"`
	Id                     string                                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FakeDevice             *ShumeiRiskResponse_FakeDevice             `protobuf:"bytes,2,opt,name=fake_device,json=fakeDevice,proto3" json:"fake_device,omitempty"`
	DeviceSuspiciousLabels *ShumeiRiskResponse_DeviceSuspiciousLabels `protobuf:"bytes,3,opt,name=device_suspicious_labels,json=deviceSuspiciousLabels,proto3" json:"device_suspicious_labels,omitempty"`
	DeviceActiveInfo       *ShumeiRiskResponse_DeviceActiveInfo       `protobuf:"bytes,4,opt,name=device_active_info,json=deviceActiveInfo,proto3" json:"device_active_info,omitempty"`
	MonkeyDevice           *ShumeiRiskResponse_MonkeyDevice           `protobuf:"bytes,5,opt,name=monkey_device,json=monkeyDevice,proto3" json:"monkey_device,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Devicelabels) Reset() {
	*x = ShumeiRiskResponse_Devicelabels{}
	mi := &file_proto_sdk_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Devicelabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Devicelabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_Devicelabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Devicelabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Devicelabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 18}
}

func (x *ShumeiRiskResponse_Devicelabels) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ShumeiRiskResponse_Devicelabels) GetFakeDevice() *ShumeiRiskResponse_FakeDevice {
	if x != nil {
		return x.FakeDevice
	}
	return nil
}

func (x *ShumeiRiskResponse_Devicelabels) GetDeviceSuspiciousLabels() *ShumeiRiskResponse_DeviceSuspiciousLabels {
	if x != nil {
		return x.DeviceSuspiciousLabels
	}
	return nil
}

func (x *ShumeiRiskResponse_Devicelabels) GetDeviceActiveInfo() *ShumeiRiskResponse_DeviceActiveInfo {
	if x != nil {
		return x.DeviceActiveInfo
	}
	return nil
}

func (x *ShumeiRiskResponse_Devicelabels) GetMonkeyDevice() *ShumeiRiskResponse_MonkeyDevice {
	if x != nil {
		return x.MonkeyDevice
	}
	return nil
}

type ShumeiRiskResponse_Detail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Detail) Reset() {
	*x = ShumeiRiskResponse_Detail{}
	mi := &file_proto_sdk_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Detail) ProtoMessage() {}

func (x *ShumeiRiskResponse_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Detail.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Detail) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 19}
}

type ShumeiRiskResponse_Tokenprofilelabels struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Label1        string                     `protobuf:"bytes,1,opt,name=label1,proto3" json:"label1,omitempty"`
	Lable2        string                     `protobuf:"bytes,2,opt,name=lable2,proto3" json:"lable2,omitempty"`
	Label3        string                     `protobuf:"bytes,3,opt,name=label3,proto3" json:"label3,omitempty"`
	Description   string                     `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Timestamp     uint64                     `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Detail        *ShumeiRiskResponse_Detail `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) Reset() {
	*x = ShumeiRiskResponse_Tokenprofilelabels{}
	mi := &file_proto_sdk_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Tokenprofilelabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_Tokenprofilelabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Tokenprofilelabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Tokenprofilelabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 20}
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetLabel1() string {
	if x != nil {
		return x.Label1
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetLable2() string {
	if x != nil {
		return x.Lable2
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetLabel3() string {
	if x != nil {
		return x.Label3
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) GetDetail() *ShumeiRiskResponse_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type ShumeiRiskResponse_Tokenrisklabels struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Label1        string                     `protobuf:"bytes,1,opt,name=label1,proto3" json:"label1,omitempty"`
	Label2        string                     `protobuf:"bytes,2,opt,name=label2,proto3" json:"label2,omitempty"`
	Label3        string                     `protobuf:"bytes,3,opt,name=label3,proto3" json:"label3,omitempty"`
	Description   string                     `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Timestamp     uint64                     `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Detail        *ShumeiRiskResponse_Detail `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Tokenrisklabels) Reset() {
	*x = ShumeiRiskResponse_Tokenrisklabels{}
	mi := &file_proto_sdk_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Tokenrisklabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Tokenrisklabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_Tokenrisklabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Tokenrisklabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Tokenrisklabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 21}
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetLabel1() string {
	if x != nil {
		return x.Label1
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetLabel2() string {
	if x != nil {
		return x.Label2
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetLabel3() string {
	if x != nil {
		return x.Label3
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ShumeiRiskResponse_Tokenrisklabels) GetDetail() *ShumeiRiskResponse_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type ShumeiRiskResponse_Devicerisklabels struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Label1        string                     `protobuf:"bytes,1,opt,name=label1,proto3" json:"label1,omitempty"`
	Label2        string                     `protobuf:"bytes,2,opt,name=label2,proto3" json:"label2,omitempty"`
	Label3        string                     `protobuf:"bytes,3,opt,name=label3,proto3" json:"label3,omitempty"`
	Description   string                     `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Timestamp     uint64                     `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Detail        *ShumeiRiskResponse_Detail `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiRiskResponse_Devicerisklabels) Reset() {
	*x = ShumeiRiskResponse_Devicerisklabels{}
	mi := &file_proto_sdk_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiRiskResponse_Devicerisklabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiRiskResponse_Devicerisklabels) ProtoMessage() {}

func (x *ShumeiRiskResponse_Devicerisklabels) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sdk_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiRiskResponse_Devicerisklabels.ProtoReflect.Descriptor instead.
func (*ShumeiRiskResponse_Devicerisklabels) Descriptor() ([]byte, []int) {
	return file_proto_sdk_proto_rawDescGZIP(), []int{28, 22}
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetLabel1() string {
	if x != nil {
		return x.Label1
	}
	return ""
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetLabel2() string {
	if x != nil {
		return x.Label2
	}
	return ""
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetLabel3() string {
	if x != nil {
		return x.Label3
	}
	return ""
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ShumeiRiskResponse_Devicerisklabels) GetDetail() *ShumeiRiskResponse_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

var File_proto_sdk_proto protoreflect.FileDescriptor

const file_proto_sdk_proto_rawDesc = "" +
	"\n" +
	"\x0fproto/sdk.proto\x12\x16papegames.sparrow.risk\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bopenapiv3/annotations.proto\x1a\x1apapegames/type/empty.proto\x1a papegames/type/raw_message.proto\x1a\x13tagger/tagger.proto\x1a\x10proto/base.proto\"\x9f\x01\n" +
	"\x10SendCometRequest\x12!\n" +
	"\tclient_id\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\bclientId\x12\x16\n" +
	"\x03nid\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x12\n" +
	"\x04code\x18\x03 \x01(\x04R\x04code\x12\x12\n" +
	"\x04DOID\x18\x04 \x01(\tR\x04DOID\x12\x0e\n" +
	"\x02ip\x18\x05 \x01(\tR\x02ip\x12\x18\n" +
	"\aaccount\x18\x06 \x01(\tR\aaccount\"\xa6\x02\n" +
	"\x0fGCaptchaRequest\x12\x1c\n" +
	"\x06number\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x06number\x12\x1e\n" +
	"\acaptcha\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\acaptcha\x12\x1a\n" +
	"\x05token\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x05token\x12\x18\n" +
	"\x04time\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x04time\x12#\n" +
	"\n" +
	"captcha_id\x18\x05 \x01(\tB\x04\xe2A\x01\x02R\tcaptchaId\x12\x1a\n" +
	"\x05nonce\x18\x06 \x01(\tB\x04\xe2A\x01\x02R\x05nonce\x12-\n" +
	"\tclient_id\x18\a \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\b \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x12\n" +
	"\x04DOID\x18\t \x01(\tR\x04DOID\"1\n" +
	"\x10GCaptchaResponse\x12\x1d\n" +
	"\n" +
	"pass_token\x18\x01 \x01(\tR\tpassToken\"\x18\n" +
	"\x16ShumeiDeviceGetRequest\"\xce\x01\n" +
	"\x17ShumeiDeviceRequestData\x12\"\n" +
	"\forganization\x18\x01 \x01(\tR\forganization\x12\x0e\n" +
	"\x02os\x18\x02 \x01(\tR\x02os\x12\x15\n" +
	"\x06app_id\x18\x03 \x01(\tR\x05appId\x12\x16\n" +
	"\x06encode\x18\x04 \x01(\x05R\x06encode\x12\x1a\n" +
	"\bcompress\x18\x05 \x01(\x05R\bcompress\x12\x0e\n" +
	"\x02tn\x18\x06 \x01(\tR\x02tn\x12\x0e\n" +
	"\x02ep\x18\a \x01(\tR\x02ep\x12\x14\n" +
	"\x05retry\x18\b \x01(\x05R\x05retry\"_\n" +
	"\x18ShumeiDeviceResponseData\x12C\n" +
	"\textraInfo\x18\x1f \x01(\v2%.papegames.sparrow.risk.SDKDeviceInfoR\textraInfo\"h\n" +
	"\x10DeviceGetRequest\x12&\n" +
	"\vinformation\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\vinformation\x12,\n" +
	"\x0fthird_device_id\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\rthirdDeviceId\"h\n" +
	"\x11ThirdDeviceIdData\x12 \n" +
	"\vfingerprint\x18\x01 \x01(\tR\vfingerprint\x12\x16\n" +
	"\x06shumei\x18\x02 \x01(\tR\x06shumei\x12\x19\n" +
	"\bsdk_DOID\x18\x03 \x01(\tR\asdkDOID\"H\n" +
	"\n" +
	"DeviceInfo\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"third_info\x18\x03 \x01(\tR\tthirdInfo\"U\n" +
	"\x11DeviceGetResponse\x12\x12\n" +
	"\x04DOID\x18\x01 \x01(\tR\x04DOID\x12,\n" +
	"\x12provider_device_id\x18\x02 \x01(\tR\x10providerDeviceId\"\x81\x01\n" +
	"\x15DeviceGetResponseData\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x05R\x04code\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x126\n" +
	"\x06detail\x18\x04 \x01(\v2\x1e.papegames.sparrow.risk.DetailR\x06detail\"8\n" +
	"\x06Detail\x12\x12\n" +
	"\x04DOID\x18\x01 \x01(\tR\x04DOID\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\"I\n" +
	"\x11DeviceLogsRequest\x12\x12\n" +
	"\x04DOID\x18\x01 \x01(\tR\x04DOID\x12 \n" +
	"\vinformation\x18\x02 \x01(\tR\vinformation\"\x85\x01\n" +
	"\x0eBizInitRequest\x12-\n" +
	"\tclient_id\x18\x01 \x01(\rB\x10\xe2A\x01\x02\xbaG\ti\x00\x00\x00\x00\x00\x00Y@R\bclientId\x12!\n" +
	"\finclude_keys\x18\x02 \x01(\tR\vincludeKeys\x12!\n" +
	"\fexclude_keys\x18\x03 \x01(\tR\vexcludeKeys\"\xcc\x02\n" +
	"\x0fBizInitResponse\x12D\n" +
	"\acaptcha\x18\x01 \x01(\v2*.papegames.sparrow.risk.BizInitCaptchaDataR\acaptcha\x12\x10\n" +
	"\x03oae\x18\x02 \x01(\bR\x03oae\x12\x10\n" +
	"\x03ccr\x18\x03 \x01(\bR\x03ccr\x12'\n" +
	"\x0finteractive_msg\x18\x04 \x01(\tR\x0einteractiveMsg\x12N\n" +
	"\aoptions\x18\x05 \x03(\v24.papegames.sparrow.risk.BizInitResponse.OptionsEntryR\aoptions\x1aV\n" +
	"\fOptionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.papegames.type.RawMessageR\x05value:\x028\x01\"I\n" +
	"\x12BizInitCaptchaData\x12\x1d\n" +
	"\n" +
	"captcha_id\x18\x01 \x01(\tR\tcaptchaId\x12\x14\n" +
	"\x05close\x18\x02 \x01(\bR\x05close\"\x0f\n" +
	"\rHealthRequest\"X\n" +
	"\x0fThirdDeviceData\x12&\n" +
	"\x0fthird_device_id\x18\x01 \x01(\tR\rthirdDeviceId\x12\x1d\n" +
	"\n" +
	"third_data\x18\x02 \x01(\tR\tthirdData\"\x1b\n" +
	"\x19ShumeiDeviceConfigRequest\"\x1c\n" +
	"\x1aShumeiDeviceConfigResponse\"\xa2\x01\n" +
	"\x14SDKCheckPhoneRequest\x12\x1a\n" +
	"\x05nonce\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x05nonce\x12\x1a\n" +
	"\x05phone\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05phone\x12\x12\n" +
	"\x04mock\x18\x03 \x01(\x05R\x04mock\x12!\n" +
	"\tclient_id\x18\x05 \x01(\rB\x04\xe2A\x01\x02R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x05appId\"+\n" +
	"\x15SDKCheckPhoneResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\"q\n" +
	"\x13SDKPhoneCodeRequest\x12\x1a\n" +
	"\x05nonce\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x05nonce\x12!\n" +
	"\tclient_id\x18\x03 \x01(\rB\x04\xe2A\x01\x02R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x05appId\"\x9f\x01\n" +
	"\x13SDKCheckCodeRequest\x12\x1a\n" +
	"\x05nonce\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x05nonce\x12\x18\n" +
	"\x04code\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x04code\x12!\n" +
	"\tclient_id\x18\x03 \x01(\rB\x04\xe2A\x01\x02R\bclientId\x12\x1b\n" +
	"\x06app_id\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x05appId\x12\x12\n" +
	"\x04DOID\x18\x05 \x01(\tR\x04DOID\"N\n" +
	"\x14SDKCheckEmailRequest\x12\x1a\n" +
	"\x05nonce\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x05nonce\x12\x1a\n" +
	"\x05email\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05email\"\xd8\x01\n" +
	"\rSDKDeviceInfo\x12\x0e\n" +
	"\x02os\x18\x01 \x01(\tR\x02os\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"android_id\x18\x03 \x01(\tR\tandroidId\x12\x12\n" +
	"\x04oaid\x18\x04 \x01(\tR\x04oaid\x12\x12\n" +
	"\x04idfv\x18\x05 \x01(\tR\x04idfv\x12\x12\n" +
	"\x04idfa\x18\x06 \x01(\tR\x04idfa\x12&\n" +
	"\x0fpaper_device_id\x18\a \x01(\tR\rpaperDeviceId\x12\x17\n" +
	"\ais_dark\x18\b \x01(\bR\x06isDark\"\xbc\x02\n" +
	"\n" +
	"CreateDOID\x12K\n" +
	"\rSDKDeviceInfo\x18\x01 \x01(\v2%.papegames.sparrow.risk.SDKDeviceInfoR\rSDKDeviceInfo\x12\x12\n" +
	"\x04DOID\x18\x03 \x01(\tR\x04DOID\x12\x0e\n" +
	"\x02ip\x18\x04 \x01(\tR\x02ip\x12\"\n" +
	"\fproviderName\x18\x05 \x01(\tR\fproviderName\x12$\n" +
	"\rthirdDeviceId\x18\x06 \x01(\tR\rthirdDeviceId\x12W\n" +
	"\x11thirdDeviceIdData\x18\a \x01(\v2).papegames.sparrow.risk.ThirdDeviceIdDataR\x11thirdDeviceIdData\x12\x1a\n" +
	"\bdeviceId\x18\b \x01(\tR\bdeviceId\"=\n" +
	"\rCreateSdKDOID\x12\x12\n" +
	"\x04DOID\x18\x01 \x01(\tR\x04DOID\x12\x18\n" +
	"\aSDKDOID\x18\x02 \x01(\tR\aSDKDOID\"\xd86\n" +
	"\x12ShumeiRiskResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\fprofileExist\x18\x03 \x01(\rR\fprofileExist\x12\x1c\n" +
	"\trequestId\x18\x04 \x01(\tR\trequestId\x12[\n" +
	"\fdeviceLabels\x18\x06 \x01(\v27.papegames.sparrow.risk.ShumeiRiskResponse.DevicelabelsR\fdeviceLabels\x1a\x8a\x02\n" +
	"\x14Machine_account_risk\x129\n" +
	"\x19b_machine_control_tokenid\x18\x01 \x01(\rR\x16bMachineControlTokenid\x12G\n" +
	"!b_machine_control_tokenid_last_ts\x18\x02 \x01(\x04R\x1cbMachineControlTokenidLastTs\x12/\n" +
	"\x14b_offer_wall_tokenid\x18\x03 \x01(\rR\x11bOfferWallTokenid\x12=\n" +
	"\x1cb_offer_wall_tokenid_last_ts\x18\x04 \x01(\x04R\x17bOfferWallTokenidLastTs\x1a\xfa\x02\n" +
	"\x10Ugc_account_risk\x125\n" +
	"\x17b_politics_risk_tokenid\x18\x01 \x01(\rR\x14bPoliticsRiskTokenid\x12C\n" +
	"\x1fb_politics_risk_tokenid_last_ts\x18\x02 \x01(\x04R\x1abPoliticsRiskTokenidLastTs\x12-\n" +
	"\x13b_sexy_risk_tokenid\x18\x03 \x01(\rR\x10bSexyRiskTokenid\x12;\n" +
	"\x1bb_sexy_risk_tokenid_last_ts\x18\x04 \x01(\x04R\x16bSexyRiskTokenidLastTs\x127\n" +
	"\x18b_advertise_risk_tokenid\x18\x05 \x01(\rR\x15bAdvertiseRiskTokenid\x12E\n" +
	" b_advertise_risk_tokenid_last_ts\x18\x06 \x01(\x04R\x1bbAdvertiseRiskTokenidLastTs\x1a\x80\x01\n" +
	"\x12Scene_account_risk\x12-\n" +
	"\x13i_tout_risk_tokenid\x18\x01 \x01(\rR\x10iToutRiskTokenid\x12;\n" +
	"\x1bi_tout_risk_tokenid_last_ts\x18\x02 \x01(\x04R\x16iToutRiskTokenidLastTs\x1a\xcd\x01\n" +
	"\x13Account_active_info\x12F\n" +
	" i_tokenid_first_active_timestamp\x18\x01 \x01(\x04R\x1ciTokenidFirstActiveTimestamp\x126\n" +
	"\x18i_tokenid_active_days_7d\x18\x02 \x01(\rR\x14iTokenidActiveDays7d\x126\n" +
	"\x18i_tokenid_active_days_4w\x18\x03 \x01(\rR\x14iTokenidActiveDays4w\x1a{\n" +
	"\x11Account_freq_info\x122\n" +
	"\x16i_tokenid_login_cnt_1d\x18\x01 \x01(\rR\x12iTokenidLoginCnt1d\x122\n" +
	"\x16i_tokenid_login_cnt_7d\x18\x02 \x01(\rR\x12iTokenidLoginCnt7d\x1a\x9b\x02\n" +
	"\x13Account_relate_info\x12=\n" +
	"\x1ci_tokenid_relate_smid_cnt_1d\x18\x01 \x01(\rR\x17iTokenidRelateSmidCnt1d\x12=\n" +
	"\x1ci_tokenid_relate_smid_cnt_7d\x18\x02 \x01(\rR\x17iTokenidRelateSmidCnt7d\x12B\n" +
	"\x1fi_tokenid_relate_ip_city_cnt_1d\x18\x03 \x01(\rR\x19iTokenidRelateIpCityCnt1d\x12B\n" +
	"\x1fi_tokenid_relate_ip_city_cnt_7d\x18\x04 \x01(\rR\x19iTokenidRelateIpCityCnt7d\x1aK\n" +
	"!S_tokenid_relate_smid_info_map_4w\x12\x12\n" +
	"\x04smid\x18\x01 \x01(\tR\x04smid\x12\x12\n" +
	"\x04days\x18\x02 \x01(\tR\x04days\x1aN\n" +
	"$S_tokenid_relate_ip_city_info_map_4w\x12\x12\n" +
	"\x04city\x18\x01 \x01(\tR\x04city\x12\x12\n" +
	"\x04days\x18\x02 \x01(\tR\x04days\x1a\xcb\x02\n" +
	"\x13Account_common_info\x12\x94\x01\n" +
	"!s_tokenid_relate_smid_info_map_4w\x18\x01 \x03(\v2L.papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_smid_info_map_4wR\x1bsTokenidRelateSmidInfoMap4w\x12\x9c\x01\n" +
	"$s_tokenid_relate_ip_city_info_map_4w\x18\x02 \x03(\v2O.papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_ip_city_info_map_4wR\x1dsTokenidRelateIpCityInfoMap4w\x1a\x8e\x06\n" +
	"\vTokenlabels\x12q\n" +
	"\x14machine_account_risk\x18\x01 \x01(\v2?.papegames.sparrow.risk.ShumeiRiskResponse.Machine_account_riskR\x12machineAccountRisk\x12e\n" +
	"\x10UGC_account_risk\x18\x02 \x01(\v2;.papegames.sparrow.risk.ShumeiRiskResponse.Ugc_account_riskR\x0eUGCAccountRisk\x12k\n" +
	"\x12scene_account_risk\x18\x03 \x01(\v2=.papegames.sparrow.risk.ShumeiRiskResponse.Scene_account_riskR\x10sceneAccountRisk\x12n\n" +
	"\x13account_active_info\x18\x04 \x01(\v2>.papegames.sparrow.risk.ShumeiRiskResponse.Account_active_infoR\x11accountActiveInfo\x12h\n" +
	"\x11account_freq_info\x18\x05 \x01(\v2<.papegames.sparrow.risk.ShumeiRiskResponse.Account_freq_infoR\x0faccountFreqInfo\x12n\n" +
	"\x13account_relate_info\x18\x06 \x01(\v2>.papegames.sparrow.risk.ShumeiRiskResponse.Account_relate_infoR\x11accountRelateInfo\x12n\n" +
	"\x13account_common_info\x18\a \x01(\v2>.papegames.sparrow.risk.ShumeiRiskResponse.Account_common_infoR\x11accountCommonInfo\x1aS\n" +
	"\x05Other\x12\x1d\n" +
	"\n" +
	"b_mismatch\x18\x01 \x01(\rR\tbMismatch\x12+\n" +
	"\x12b_mismatch_last_ts\x18\x02 \x01(\x04R\x0fbMismatchLastTs\x1a\x89\a\n" +
	"\vFake_device\x12\"\n" +
	"\rb_pc_emulator\x18\x01 \x01(\rR\vbPcEmulator\x120\n" +
	"\x15b_pc_emulator_last_ts\x18\x02 \x01(\x04R\x11bPcEmulatorLastTs\x12,\n" +
	"\x13b_pc_emulator_pc_id\x18\x03 \x01(\tR\x0fbPcEmulatorPcId\x12$\n" +
	"\x0eb_cloud_device\x18\x04 \x01(\rR\fbCloudDevice\x122\n" +
	"\x16b_cloud_device_last_ts\x18\x05 \x01(\x04R\x12bCloudDeviceLastTs\x12\x1b\n" +
	"\tb_altered\x18\x06 \x01(\rR\bbAltered\x12)\n" +
	"\x11b_altered_last_ts\x18\a \x01(\x04R\x0ebAlteredLastTs\x12$\n" +
	"\x0eb_multi_boxing\x18\b \x01(\rR\fbMultiBoxing\x122\n" +
	"\x16b_multi_boxing_last_ts\x18\t \x01(\x04R\x12bMultiBoxingLastTs\x12.\n" +
	"\x14b_multi_boxing_by_os\x18\n" +
	" \x01(\rR\x10bMultiBoxingByOs\x12<\n" +
	"\x1cb_multi_boxing_by_os_last_ts\x18\v \x01(\x04R\x16bMultiBoxingByOsLastTs\x120\n" +
	"\x15b_multi_boxing_by_app\x18\f \x01(\rR\x11bMultiBoxingByApp\x12>\n" +
	"\x1db_multi_boxing_by_app_last_ts\x18\r \x01(\x04R\x17bMultiBoxingByAppLastTs\x12\x17\n" +
	"\ab_faker\x18\x0e \x01(\rR\x06bFaker\x12%\n" +
	"\x0fb_faker_last_ts\x18\x0f \x01(\x04R\fbFakerLastTs\x12\x19\n" +
	"\bb_farmer\x18\x10 \x01(\rR\abFarmer\x12'\n" +
	"\x10b_farmer_last_ts\x18\x11 \x01(\x04R\rbFarmerLastTs\x12\x1f\n" +
	"\vb_offerwall\x18\x12 \x01(\rR\n" +
	"bOfferwall\x12-\n" +
	"\x13b_offerwall_last_ts\x18\x13 \x01(\x04R\x10bOfferwallLastTs\x12F\n" +
	"\x05other\x18\x14 \x01(\v20.papegames.sparrow.risk.ShumeiRiskResponse.OtherR\x05other\x1a\xf1\t\n" +
	"\x18Device_suspicious_labels\x12\x15\n" +
	"\x06b_root\x18\x01 \x01(\rR\x05bRoot\x12#\n" +
	"\x0eb_root_last_ts\x18\x02 \x01(\x04R\vbRootLastTs\x12\x13\n" +
	"\x05b_sim\x18\x03 \x01(\rR\x04bSim\x12!\n" +
	"\rb_sim_last_ts\x18\x04 \x01(\x04R\n" +
	"bSimLastTs\x12!\n" +
	"\fb_debuggable\x18\x05 \x01(\rR\vbDebuggable\x12/\n" +
	"\x14b_debuggable_last_ts\x18\x06 \x01(\x04R\x11bDebuggableLastTs\x12\x13\n" +
	"\x05b_vpn\x18\a \x01(\rR\x04bVpn\x12!\n" +
	"\rb_vpn_last_ts\x18\b \x01(\x04R\n" +
	"bVpnLastTs\x12\"\n" +
	"\rb_monkey_apps\x18\t \x01(\rR\vbMonkeyApps\x120\n" +
	"\x15b_monkey_apps_last_ts\x18\n" +
	" \x01(\x04R\x11bMonkeyAppsLastTs\x12\x13\n" +
	"\x05b_acc\x18\v \x01(\rR\x04bAcc\x12!\n" +
	"\rb_acc_last_ts\x18\f \x01(\x04R\n" +
	"bAccLastTs\x12-\n" +
	"\x13b_multi_boxing_apps\x18\r \x01(\rR\x10bMultiBoxingApps\x12;\n" +
	"\x1bb_multi_boxing_apps_last_ts\x18\x0e \x01(\x04R\x16bMultiBoxingAppsLastTs\x12\x15\n" +
	"\x06b_hook\x18\x0f \x01(\rR\x05bHook\x12#\n" +
	"\x0eb_hook_last_ts\x18\x10 \x01(\x04R\vbHookLastTs\x12\x1c\n" +
	"\n" +
	"b_vpn_apps\x18\x11 \x01(\rR\bbVpnApps\x12*\n" +
	"\x12b_vpn_apps_last_ts\x18\x12 \x01(\x04R\x0ebVpnAppsLastTs\x12#\n" +
	"\rb_manufacture\x18\x13 \x01(\rR\fbManufacture\x121\n" +
	"\x15b_manufacture_last_ts\x18\x14 \x01(\x04R\x12bManufactureLastTs\x12\x19\n" +
	"\bb_icloud\x18\x15 \x01(\rR\abIcloud\x12'\n" +
	"\x10b_icloud_last_ts\x18\x16 \x01(\x04R\rbIcloudLastTs\x12\x1a\n" +
	"\tb_wx_code\x18\x17 \x01(\rR\abWxCode\x12(\n" +
	"\x11b_wx_code_last_ts\x18\x18 \x01(\x04R\rbWxCodeLastTs\x12\x1c\n" +
	"\n" +
	"b_sms_code\x18\x19 \x01(\rR\bbSmsCode\x12*\n" +
	"\x12b_sms_code_last_ts\x18\x1a \x01(\x04R\x0ebSmsCodeLastTs\x12\x1e\n" +
	"\vb_low_osver\x18\x1b \x01(\rR\tbLowOsver\x12,\n" +
	"\x13b_low_osver_last_ts\x18\x1c \x01(\x04R\x0fbLowOsverLastTs\x121\n" +
	"\x15b_remote_control_apps\x18\x1d \x01(\rR\x12bRemoteControlApps\x12?\n" +
	"\x1db_remote_control_apps_last_ts\x18\x1e \x01(\x04R\x18bRemoteControlAppsLastTs\x12\x1f\n" +
	"\vb_repackage\x18\x1f \x01(\rR\n" +
	"bRepackage\x12-\n" +
	"\x13b_repackage_last_ts\x18  \x01(\x04R\x10bRepackageLastTs\x12\x17\n" +
	"\ab_reset\x18! \x01(\rR\x06bReset\x1aG\n" +
	"\x12Device_active_info\x121\n" +
	"\x15i_smid_boot_timestamp\x18\x01 \x01(\x04R\x12iSmidBootTimestamp\x1a^\n" +
	"\x06Common\x12\"\n" +
	"\rb_monkey_apps\x18\x01 \x01(\rR\vbMonkeyApps\x120\n" +
	"\x15b_monkey_apps_last_ts\x18\x02 \x01(\x04R\x11bMonkeyAppsLastTs\x1au\n" +
	"\vMonkey_game\x12+\n" +
	"\x12b_monkey_game_apps\x18\x01 \x01(\rR\x0fbMonkeyGameApps\x129\n" +
	"\x1ab_monkey_game_apps_last_ts\x18\x02 \x01(\x04R\x15bMonkeyGameAppsLastTs\x1au\n" +
	"\vMonkey_read\x12+\n" +
	"\x12b_monkey_read_apps\x18\x01 \x01(\rR\x0fbMonkeyReadApps\x129\n" +
	"\x1ab_monkey_read_apps_last_ts\x18\x02 \x01(\x04R\x15bMonkeyReadAppsLastTs\x1a\x8c\x02\n" +
	"\rMonkey_device\x12I\n" +
	"\x06common\x18\x01 \x01(\v21.papegames.sparrow.risk.ShumeiRiskResponse.CommonR\x06common\x12W\n" +
	"\vmonkey_game\x18\x02 \x01(\v26.papegames.sparrow.risk.ShumeiRiskResponse.Monkey_gameR\n" +
	"monkeyGame\x12W\n" +
	"\vmonkey_read\x18\x03 \x01(\v26.papegames.sparrow.risk.ShumeiRiskResponse.Monkey_readR\n" +
	"monkeyRead\x1a\xc2\x03\n" +
	"\fDevicelabels\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12W\n" +
	"\vfake_device\x18\x02 \x01(\v26.papegames.sparrow.risk.ShumeiRiskResponse.Fake_deviceR\n" +
	"fakeDevice\x12}\n" +
	"\x18device_suspicious_labels\x18\x03 \x01(\v2C.papegames.sparrow.risk.ShumeiRiskResponse.Device_suspicious_labelsR\x16deviceSuspiciousLabels\x12k\n" +
	"\x12device_active_info\x18\x04 \x01(\v2=.papegames.sparrow.risk.ShumeiRiskResponse.Device_active_infoR\x10deviceActiveInfo\x12]\n" +
	"\rmonkey_device\x18\x05 \x01(\v28.papegames.sparrow.risk.ShumeiRiskResponse.Monkey_deviceR\fmonkeyDevice\x1a\b\n" +
	"\x06Detail\x1a\xe7\x01\n" +
	"\x12Tokenprofilelabels\x12\x16\n" +
	"\x06label1\x18\x01 \x01(\tR\x06label1\x12\x16\n" +
	"\x06lable2\x18\x02 \x01(\tR\x06lable2\x12\x16\n" +
	"\x06label3\x18\x03 \x01(\tR\x06label3\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1c\n" +
	"\ttimestamp\x18\x05 \x01(\x04R\ttimestamp\x12I\n" +
	"\x06detail\x18\x06 \x01(\v21.papegames.sparrow.risk.ShumeiRiskResponse.DetailR\x06detail\x1a\xe4\x01\n" +
	"\x0fTokenrisklabels\x12\x16\n" +
	"\x06label1\x18\x01 \x01(\tR\x06label1\x12\x16\n" +
	"\x06label2\x18\x02 \x01(\tR\x06label2\x12\x16\n" +
	"\x06label3\x18\x03 \x01(\tR\x06label3\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1c\n" +
	"\ttimestamp\x18\x05 \x01(\x04R\ttimestamp\x12I\n" +
	"\x06detail\x18\x06 \x01(\v21.papegames.sparrow.risk.ShumeiRiskResponse.DetailR\x06detail\x1a\xe5\x01\n" +
	"\x10Devicerisklabels\x12\x16\n" +
	"\x06label1\x18\x01 \x01(\tR\x06label1\x12\x16\n" +
	"\x06label2\x18\x02 \x01(\tR\x06label2\x12\x16\n" +
	"\x06label3\x18\x03 \x01(\tR\x06label3\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1c\n" +
	"\ttimestamp\x18\x05 \x01(\x04R\ttimestamp\x12I\n" +
	"\x06detail\x18\x06 \x01(\v21.papegames.sparrow.risk.ShumeiRiskResponse.DetailR\x06detail\"\xac\x01\n" +
	"\x0fNIDCheckRequest\x12\x16\n" +
	"\x03nid\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12!\n" +
	"\tclient_id\x18\x04 \x01(\rB\x04\xe2A\x01\x02R\bclientId\x12\x12\n" +
	"\x04DOID\x18\x06 \x01(\tR\x04DOID\x12\x16\n" +
	"\x03sig\x18\a \x01(\tB\x04\xe2A\x01\x02R\x03sig\x12\"\n" +
	"\ttimestamp\x18\b \x01(\x03B\x04\xe2A\x01\x02R\ttimestamp\"8\n" +
	"\x10NIDCheckResponse\x12\x1d\n" +
	"\n" +
	"risk_score\x18\x01 \x01(\x03R\triskScore:\x05ȧ\x86\a\x01\"\xd2\x01\n" +
	"\x0fFaceCodeRequest\x12\x16\n" +
	"\x03nid\x18\x01 \x01(\tB\x04\xe2A\x01\x02R\x03nid\x12\x1a\n" +
	"\x05token\x18\x02 \x01(\tB\x04\xe2A\x01\x02R\x05token\x12\x1a\n" +
	"\x05scene\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x05scene\x12\x1c\n" +
	"\x06vendor\x18\x04 \x01(\tB\x04\xe2A\x01\x02R\x06vendor\x12\x14\n" +
	"\x05extra\x18\x05 \x01(\tR\x05extra\x12!\n" +
	"\tclient_id\x18\x06 \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x18\n" +
	"\x04DOID\x18\a \x01(\tB\x04\xe2A\x01\x02R\x04DOID\"\x88\x01\n" +
	"\x10FaceCodeResponse\x12\x16\n" +
	"\x06vendor\x18\x01 \x01(\tR\x06vendor\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12A\n" +
	"\aservice\x18\x03 \x01(\v2'.papegames.sparrow.risk.FaceServiceDataR\aservice:\x05ȧ\x86\a\x01\"\x9d\x01\n" +
	"\vFaceCodeVal\x12\x18\n" +
	"\aservice\x18\x01 \x01(\tR\aservice\x12\x10\n" +
	"\x03nid\x18\x02 \x01(\tR\x03nid\x12\x14\n" +
	"\x05scene\x18\x03 \x01(\tR\x05scene\x12\x16\n" +
	"\x06vendor\x18\x04 \x01(\tR\x06vendor\x12\x17\n" +
	"\aid_card\x18\x06 \x01(\tR\x06idCard\x12\x1b\n" +
	"\treal_name\x18\a \x01(\tR\brealName\"\xaf\x01\n" +
	"\x0eFaceRdbTokeVal\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\x03R\bclientId\x12\x12\n" +
	"\x04DOID\x18\x02 \x01(\tR\x04DOID\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\x12\x10\n" +
	"\x03nid\x18\x04 \x01(\tR\x03nid\x12\x14\n" +
	"\x05scene\x18\x05 \x01(\tR\x05scene\x12\x17\n" +
	"\aid_card\x18\x06 \x01(\tR\x06idCard\x12\x1b\n" +
	"\treal_name\x18\a \x01(\tR\brealName2\x80\x13\n" +
	"\x0eRiskSDKService\x12l\n" +
	"\x06Health\x12%.papegames.sparrow.risk.HealthRequest\x1a\x15.papegames.type.Empty\"$\xbaG\x0fj\r\n" +
	"\x05level\x12\x04\x12\x02p2\x82\xd3\xe4\x93\x02\f\x12\n" +
	"/v1/health\x12\xc3\x01\n" +
	"\tDeviceGet\x12(.papegames.sparrow.risk.DeviceGetRequest\x1a).papegames.sparrow.risk.DeviceGetResponse\"a\xdaA\tform-data\xbaG8*\n" +
	"获取DOIDj\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/risk/hd/get\x12\xc4\x01\n" +
	"\aBizInit\x12&.papegames.sparrow.risk.BizInitRequest\x1a'.papegames.sparrow.risk.BizInitResponse\"h\xdaA\tform-data\xbaG=*\x0f初始化接口j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/risk/biz/init\x12\xd1\x01\n" +
	"\bGCaptcha\x12'.papegames.sparrow.risk.GCaptchaRequest\x1a(.papegames.sparrow.risk.GCaptchaResponse\"r\xdaA\tform-data\xbaG@*\x12极验验证接口j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/v1/risk/captcha/g/check\x12\xdc\x01\n" +
	"\rSDKCheckPhone\x12,.papegames.sparrow.risk.SDKCheckPhoneRequest\x1a-.papegames.sparrow.risk.SDKCheckPhoneResponse\"n\xdaA\tform-data\xbaG@*\x12手机号码验证j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/phone/check\x12\xcb\x01\n" +
	"\fSDKPhoneCode\x12+.papegames.sparrow.risk.SDKPhoneCodeRequest\x1a\x15.papegames.type.Empty\"w\xdaA\tform-data\xbaGF*\x18手机号码单发短信j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/v1/risk/phone/sendcode\x12\xbe\x01\n" +
	"\x0fSDKCheckSMSCode\x12+.papegames.sparrow.risk.SDKCheckCodeRequest\x1a\x15.papegames.type.Empty\"g\xdaA\tform-data\xbaG:*\f短信验证j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/risk/code/check\x12\xc4\x01\n" +
	"\rSDKCheckEmail\x12,.papegames.sparrow.risk.SDKCheckEmailRequest\x1a\x15.papegames.type.Empty\"n\xdaA\tform-data\xbaG@*\x12邮箱地址验证j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/email/check\x12\xbb\x01\n" +
	"\tSendComet\x12(.papegames.sparrow.risk.SendCometRequest\x1a\x15.papegames.type.Empty\"m\xdaA\tform-data\xbaG@*\x12发送长链消息j\r\n" +
	"\x05level\x12\x04\x12\x02P2j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06ToB端\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/risk/comet/send\x12\xd8\x01\n" +
	"\aNIDRisk\x12'.papegames.sparrow.risk.NIDCheckRequest\x1a(.papegames.sparrow.risk.NIDCheckResponse\"z\xdaA\tform-data\xbaGO*\x1e游戏账号风险信息查询j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1e\n" +
	"\x0fx-apifox-folder\x12\v\x12\t服务端\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/risk/gs/check\x12\xdd\x01\n" +
	"\bFaceCode\x12'.papegames.sparrow.risk.FaceCodeRequest\x1a(.papegames.sparrow.risk.FaceCodeResponse\"~\xdaA\tform-data\xbaGR*$人脸验证初始化（移动端）j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/risk/face/code\x12\xd9\x01\n" +
	"\n" +
	"FaceResult\x12).papegames.sparrow.risk.FaceResultRequest\x1a*.papegames.sparrow.risk.FaceResultResponse\"t\xdaA\tform-data\xbaGF*\x18人脸验证认证结果j\r\n" +
	"\x05level\x12\x04\x12\x02P0j\x1b\n" +
	"\x0fx-apifox-folder\x12\b\x12\x06SDK端\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/risk/face/result\x1a\x15\xcaA\x12risk.papegames.comB;\n" +
	"\x1acom.papegames.sparrow.riskB\tRiskProtoP\x01Z\x10risk/proto;protob\x06proto3"

var (
	file_proto_sdk_proto_rawDescOnce sync.Once
	file_proto_sdk_proto_rawDescData []byte
)

func file_proto_sdk_proto_rawDescGZIP() []byte {
	file_proto_sdk_proto_rawDescOnce.Do(func() {
		file_proto_sdk_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_sdk_proto_rawDesc), len(file_proto_sdk_proto_rawDesc)))
	})
	return file_proto_sdk_proto_rawDescData
}

var file_proto_sdk_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_proto_sdk_proto_goTypes = []any{
	(*SendCometRequest)(nil),                                  // 0: papegames.sparrow.risk.SendCometRequest
	(*GCaptchaRequest)(nil),                                   // 1: papegames.sparrow.risk.GCaptchaRequest
	(*GCaptchaResponse)(nil),                                  // 2: papegames.sparrow.risk.GCaptchaResponse
	(*ShumeiDeviceGetRequest)(nil),                            // 3: papegames.sparrow.risk.ShumeiDeviceGetRequest
	(*ShumeiDeviceRequestData)(nil),                           // 4: papegames.sparrow.risk.ShumeiDeviceRequestData
	(*ShumeiDeviceResponseData)(nil),                          // 5: papegames.sparrow.risk.ShumeiDeviceResponseData
	(*DeviceGetRequest)(nil),                                  // 6: papegames.sparrow.risk.DeviceGetRequest
	(*ThirdDeviceIdData)(nil),                                 // 7: papegames.sparrow.risk.ThirdDeviceIdData
	(*DeviceInfo)(nil),                                        // 8: papegames.sparrow.risk.DeviceInfo
	(*DeviceGetResponse)(nil),                                 // 9: papegames.sparrow.risk.DeviceGetResponse
	(*DeviceGetResponseData)(nil),                             // 10: papegames.sparrow.risk.DeviceGetResponseData
	(*Detail)(nil),                                            // 11: papegames.sparrow.risk.Detail
	(*DeviceLogsRequest)(nil),                                 // 12: papegames.sparrow.risk.DeviceLogsRequest
	(*BizInitRequest)(nil),                                    // 13: papegames.sparrow.risk.BizInitRequest
	(*BizInitResponse)(nil),                                   // 14: papegames.sparrow.risk.BizInitResponse
	(*BizInitCaptchaData)(nil),                                // 15: papegames.sparrow.risk.BizInitCaptchaData
	(*HealthRequest)(nil),                                     // 16: papegames.sparrow.risk.HealthRequest
	(*ThirdDeviceData)(nil),                                   // 17: papegames.sparrow.risk.ThirdDeviceData
	(*ShumeiDeviceConfigRequest)(nil),                         // 18: papegames.sparrow.risk.ShumeiDeviceConfigRequest
	(*ShumeiDeviceConfigResponse)(nil),                        // 19: papegames.sparrow.risk.ShumeiDeviceConfigResponse
	(*SDKCheckPhoneRequest)(nil),                              // 20: papegames.sparrow.risk.SDKCheckPhoneRequest
	(*SDKCheckPhoneResponse)(nil),                             // 21: papegames.sparrow.risk.SDKCheckPhoneResponse
	(*SDKPhoneCodeRequest)(nil),                               // 22: papegames.sparrow.risk.SDKPhoneCodeRequest
	(*SDKCheckCodeRequest)(nil),                               // 23: papegames.sparrow.risk.SDKCheckCodeRequest
	(*SDKCheckEmailRequest)(nil),                              // 24: papegames.sparrow.risk.SDKCheckEmailRequest
	(*SDKDeviceInfo)(nil),                                     // 25: papegames.sparrow.risk.SDKDeviceInfo
	(*CreateDOID)(nil),                                        // 26: papegames.sparrow.risk.CreateDOID
	(*CreateSdKDOID)(nil),                                     // 27: papegames.sparrow.risk.CreateSdKDOID
	(*ShumeiRiskResponse)(nil),                                // 28: papegames.sparrow.risk.ShumeiRiskResponse
	(*NIDCheckRequest)(nil),                                   // 29: papegames.sparrow.risk.NIDCheckRequest
	(*NIDCheckResponse)(nil),                                  // 30: papegames.sparrow.risk.NIDCheckResponse
	(*FaceCodeRequest)(nil),                                   // 31: papegames.sparrow.risk.FaceCodeRequest
	(*FaceCodeResponse)(nil),                                  // 32: papegames.sparrow.risk.FaceCodeResponse
	(*FaceCodeVal)(nil),                                       // 33: papegames.sparrow.risk.FaceCodeVal
	(*FaceRdbTokeVal)(nil),                                    // 34: papegames.sparrow.risk.FaceRdbTokeVal
	nil,                                                       // 35: papegames.sparrow.risk.BizInitResponse.OptionsEntry
	(*ShumeiRiskResponse_MachineAccountRisk)(nil),             // 36: papegames.sparrow.risk.ShumeiRiskResponse.Machine_account_risk
	(*ShumeiRiskResponse_UgcAccountRisk)(nil),                 // 37: papegames.sparrow.risk.ShumeiRiskResponse.Ugc_account_risk
	(*ShumeiRiskResponse_SceneAccountRisk)(nil),               // 38: papegames.sparrow.risk.ShumeiRiskResponse.Scene_account_risk
	(*ShumeiRiskResponse_AccountActiveInfo)(nil),              // 39: papegames.sparrow.risk.ShumeiRiskResponse.Account_active_info
	(*ShumeiRiskResponse_AccountFreqInfo)(nil),                // 40: papegames.sparrow.risk.ShumeiRiskResponse.Account_freq_info
	(*ShumeiRiskResponse_AccountRelateInfo)(nil),              // 41: papegames.sparrow.risk.ShumeiRiskResponse.Account_relate_info
	(*ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W)(nil),   // 42: papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_smid_info_map_4w
	(*ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W)(nil), // 43: papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_ip_city_info_map_4w
	(*ShumeiRiskResponse_AccountCommonInfo)(nil),              // 44: papegames.sparrow.risk.ShumeiRiskResponse.Account_common_info
	(*ShumeiRiskResponse_Tokenlabels)(nil),                    // 45: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels
	(*ShumeiRiskResponse_Other)(nil),                          // 46: papegames.sparrow.risk.ShumeiRiskResponse.Other
	(*ShumeiRiskResponse_FakeDevice)(nil),                     // 47: papegames.sparrow.risk.ShumeiRiskResponse.Fake_device
	(*ShumeiRiskResponse_DeviceSuspiciousLabels)(nil),         // 48: papegames.sparrow.risk.ShumeiRiskResponse.Device_suspicious_labels
	(*ShumeiRiskResponse_DeviceActiveInfo)(nil),               // 49: papegames.sparrow.risk.ShumeiRiskResponse.Device_active_info
	(*ShumeiRiskResponse_Common)(nil),                         // 50: papegames.sparrow.risk.ShumeiRiskResponse.Common
	(*ShumeiRiskResponse_MonkeyGame)(nil),                     // 51: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_game
	(*ShumeiRiskResponse_MonkeyRead)(nil),                     // 52: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_read
	(*ShumeiRiskResponse_MonkeyDevice)(nil),                   // 53: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_device
	(*ShumeiRiskResponse_Devicelabels)(nil),                   // 54: papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels
	(*ShumeiRiskResponse_Detail)(nil),                         // 55: papegames.sparrow.risk.ShumeiRiskResponse.Detail
	(*ShumeiRiskResponse_Tokenprofilelabels)(nil),             // 56: papegames.sparrow.risk.ShumeiRiskResponse.Tokenprofilelabels
	(*ShumeiRiskResponse_Tokenrisklabels)(nil),                // 57: papegames.sparrow.risk.ShumeiRiskResponse.Tokenrisklabels
	(*ShumeiRiskResponse_Devicerisklabels)(nil),               // 58: papegames.sparrow.risk.ShumeiRiskResponse.Devicerisklabels
	(*FaceServiceData)(nil),                                   // 59: papegames.sparrow.risk.FaceServiceData
	(*xtype.RawMessage)(nil),                                  // 60: papegames.type.RawMessage
	(*FaceResultRequest)(nil),                                 // 61: papegames.sparrow.risk.FaceResultRequest
	(*xtype.Empty)(nil),                                       // 62: papegames.type.Empty
	(*FaceResultResponse)(nil),                                // 63: papegames.sparrow.risk.FaceResultResponse
}
var file_proto_sdk_proto_depIdxs = []int32{
	25, // 0: papegames.sparrow.risk.ShumeiDeviceResponseData.extraInfo:type_name -> papegames.sparrow.risk.SDKDeviceInfo
	11, // 1: papegames.sparrow.risk.DeviceGetResponseData.detail:type_name -> papegames.sparrow.risk.Detail
	15, // 2: papegames.sparrow.risk.BizInitResponse.captcha:type_name -> papegames.sparrow.risk.BizInitCaptchaData
	35, // 3: papegames.sparrow.risk.BizInitResponse.options:type_name -> papegames.sparrow.risk.BizInitResponse.OptionsEntry
	25, // 4: papegames.sparrow.risk.CreateDOID.SDKDeviceInfo:type_name -> papegames.sparrow.risk.SDKDeviceInfo
	7,  // 5: papegames.sparrow.risk.CreateDOID.thirdDeviceIdData:type_name -> papegames.sparrow.risk.ThirdDeviceIdData
	54, // 6: papegames.sparrow.risk.ShumeiRiskResponse.deviceLabels:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels
	59, // 7: papegames.sparrow.risk.FaceCodeResponse.service:type_name -> papegames.sparrow.risk.FaceServiceData
	60, // 8: papegames.sparrow.risk.BizInitResponse.OptionsEntry.value:type_name -> papegames.type.RawMessage
	42, // 9: papegames.sparrow.risk.ShumeiRiskResponse.Account_common_info.s_tokenid_relate_smid_info_map_4w:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_smid_info_map_4w
	43, // 10: papegames.sparrow.risk.ShumeiRiskResponse.Account_common_info.s_tokenid_relate_ip_city_info_map_4w:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.S_tokenid_relate_ip_city_info_map_4w
	36, // 11: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.machine_account_risk:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Machine_account_risk
	37, // 12: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.UGC_account_risk:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Ugc_account_risk
	38, // 13: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.scene_account_risk:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Scene_account_risk
	39, // 14: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.account_active_info:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Account_active_info
	40, // 15: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.account_freq_info:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Account_freq_info
	41, // 16: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.account_relate_info:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Account_relate_info
	44, // 17: papegames.sparrow.risk.ShumeiRiskResponse.Tokenlabels.account_common_info:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Account_common_info
	46, // 18: papegames.sparrow.risk.ShumeiRiskResponse.Fake_device.other:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Other
	50, // 19: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_device.common:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Common
	51, // 20: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_device.monkey_game:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Monkey_game
	52, // 21: papegames.sparrow.risk.ShumeiRiskResponse.Monkey_device.monkey_read:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Monkey_read
	47, // 22: papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels.fake_device:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Fake_device
	48, // 23: papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels.device_suspicious_labels:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Device_suspicious_labels
	49, // 24: papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels.device_active_info:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Device_active_info
	53, // 25: papegames.sparrow.risk.ShumeiRiskResponse.Devicelabels.monkey_device:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Monkey_device
	55, // 26: papegames.sparrow.risk.ShumeiRiskResponse.Tokenprofilelabels.detail:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Detail
	55, // 27: papegames.sparrow.risk.ShumeiRiskResponse.Tokenrisklabels.detail:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Detail
	55, // 28: papegames.sparrow.risk.ShumeiRiskResponse.Devicerisklabels.detail:type_name -> papegames.sparrow.risk.ShumeiRiskResponse.Detail
	16, // 29: papegames.sparrow.risk.RiskSDKService.Health:input_type -> papegames.sparrow.risk.HealthRequest
	6,  // 30: papegames.sparrow.risk.RiskSDKService.DeviceGet:input_type -> papegames.sparrow.risk.DeviceGetRequest
	13, // 31: papegames.sparrow.risk.RiskSDKService.BizInit:input_type -> papegames.sparrow.risk.BizInitRequest
	1,  // 32: papegames.sparrow.risk.RiskSDKService.GCaptcha:input_type -> papegames.sparrow.risk.GCaptchaRequest
	20, // 33: papegames.sparrow.risk.RiskSDKService.SDKCheckPhone:input_type -> papegames.sparrow.risk.SDKCheckPhoneRequest
	22, // 34: papegames.sparrow.risk.RiskSDKService.SDKPhoneCode:input_type -> papegames.sparrow.risk.SDKPhoneCodeRequest
	23, // 35: papegames.sparrow.risk.RiskSDKService.SDKCheckSMSCode:input_type -> papegames.sparrow.risk.SDKCheckCodeRequest
	24, // 36: papegames.sparrow.risk.RiskSDKService.SDKCheckEmail:input_type -> papegames.sparrow.risk.SDKCheckEmailRequest
	0,  // 37: papegames.sparrow.risk.RiskSDKService.SendComet:input_type -> papegames.sparrow.risk.SendCometRequest
	29, // 38: papegames.sparrow.risk.RiskSDKService.NIDRisk:input_type -> papegames.sparrow.risk.NIDCheckRequest
	31, // 39: papegames.sparrow.risk.RiskSDKService.FaceCode:input_type -> papegames.sparrow.risk.FaceCodeRequest
	61, // 40: papegames.sparrow.risk.RiskSDKService.FaceResult:input_type -> papegames.sparrow.risk.FaceResultRequest
	62, // 41: papegames.sparrow.risk.RiskSDKService.Health:output_type -> papegames.type.Empty
	9,  // 42: papegames.sparrow.risk.RiskSDKService.DeviceGet:output_type -> papegames.sparrow.risk.DeviceGetResponse
	14, // 43: papegames.sparrow.risk.RiskSDKService.BizInit:output_type -> papegames.sparrow.risk.BizInitResponse
	2,  // 44: papegames.sparrow.risk.RiskSDKService.GCaptcha:output_type -> papegames.sparrow.risk.GCaptchaResponse
	21, // 45: papegames.sparrow.risk.RiskSDKService.SDKCheckPhone:output_type -> papegames.sparrow.risk.SDKCheckPhoneResponse
	62, // 46: papegames.sparrow.risk.RiskSDKService.SDKPhoneCode:output_type -> papegames.type.Empty
	62, // 47: papegames.sparrow.risk.RiskSDKService.SDKCheckSMSCode:output_type -> papegames.type.Empty
	62, // 48: papegames.sparrow.risk.RiskSDKService.SDKCheckEmail:output_type -> papegames.type.Empty
	62, // 49: papegames.sparrow.risk.RiskSDKService.SendComet:output_type -> papegames.type.Empty
	30, // 50: papegames.sparrow.risk.RiskSDKService.NIDRisk:output_type -> papegames.sparrow.risk.NIDCheckResponse
	32, // 51: papegames.sparrow.risk.RiskSDKService.FaceCode:output_type -> papegames.sparrow.risk.FaceCodeResponse
	63, // 52: papegames.sparrow.risk.RiskSDKService.FaceResult:output_type -> papegames.sparrow.risk.FaceResultResponse
	41, // [41:53] is the sub-list for method output_type
	29, // [29:41] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_proto_sdk_proto_init() }
func file_proto_sdk_proto_init() {
	if File_proto_sdk_proto != nil {
		return
	}
	file_proto_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_sdk_proto_rawDesc), len(file_proto_sdk_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_sdk_proto_goTypes,
		DependencyIndexes: file_proto_sdk_proto_depIdxs,
		MessageInfos:      file_proto_sdk_proto_msgTypes,
	}.Build()
	File_proto_sdk_proto = out.File
	file_proto_sdk_proto_goTypes = nil
	file_proto_sdk_proto_depIdxs = nil
}

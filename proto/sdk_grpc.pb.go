// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: proto/sdk.proto

package proto

import (
	context "context"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RiskSDKService_Health_FullMethodName          = "/papegames.sparrow.risk.RiskSDKService/Health"
	RiskSDKService_DeviceGet_FullMethodName       = "/papegames.sparrow.risk.RiskSDKService/DeviceGet"
	RiskSDKService_BizInit_FullMethodName         = "/papegames.sparrow.risk.RiskSDKService/BizInit"
	RiskSDKService_GCaptcha_FullMethodName        = "/papegames.sparrow.risk.RiskSDKService/GCaptcha"
	RiskSDKService_SDKCheckPhone_FullMethodName   = "/papegames.sparrow.risk.RiskSDKService/SDKCheckPhone"
	RiskSDKService_SDKPhoneCode_FullMethodName    = "/papegames.sparrow.risk.RiskSDKService/SDKPhoneCode"
	RiskSDKService_SDKCheckSMSCode_FullMethodName = "/papegames.sparrow.risk.RiskSDKService/SDKCheckSMSCode"
	RiskSDKService_SDKCheckEmail_FullMethodName   = "/papegames.sparrow.risk.RiskSDKService/SDKCheckEmail"
	RiskSDKService_SendComet_FullMethodName       = "/papegames.sparrow.risk.RiskSDKService/SendComet"
	RiskSDKService_NIDRisk_FullMethodName         = "/papegames.sparrow.risk.RiskSDKService/NIDRisk"
	RiskSDKService_FaceCode_FullMethodName        = "/papegames.sparrow.risk.RiskSDKService/FaceCode"
	RiskSDKService_FaceResult_FullMethodName      = "/papegames.sparrow.risk.RiskSDKService/FaceResult"
)

// RiskSDKServiceClient is the client API for RiskSDKService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// This API represents pay-risk service.
type RiskSDKServiceClient interface {
	// 健康检查
	Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 获取设备号
	DeviceGet(ctx context.Context, in *DeviceGetRequest, opts ...grpc.CallOption) (*DeviceGetResponse, error)
	// init接口
	BizInit(ctx context.Context, in *BizInitRequest, opts ...grpc.CallOption) (*BizInitResponse, error)
	// 极验验证接口
	GCaptcha(ctx context.Context, in *GCaptchaRequest, opts ...grpc.CallOption) (*GCaptchaResponse, error)
	SDKCheckPhone(ctx context.Context, in *SDKCheckPhoneRequest, opts ...grpc.CallOption) (*SDKCheckPhoneResponse, error)
	SDKPhoneCode(ctx context.Context, in *SDKPhoneCodeRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	SDKCheckSMSCode(ctx context.Context, in *SDKCheckCodeRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	SDKCheckEmail(ctx context.Context, in *SDKCheckEmailRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	SendComet(ctx context.Context, in *SendCometRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 游戏账号风险信息查询
	NIDRisk(ctx context.Context, in *NIDCheckRequest, opts ...grpc.CallOption) (*NIDCheckResponse, error)
	FaceCode(ctx context.Context, in *FaceCodeRequest, opts ...grpc.CallOption) (*FaceCodeResponse, error)
	FaceResult(ctx context.Context, in *FaceResultRequest, opts ...grpc.CallOption) (*FaceResultResponse, error)
}

type riskSDKServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskSDKServiceClient(cc grpc.ClientConnInterface) RiskSDKServiceClient {
	return &riskSDKServiceClient{cc}
}

func (c *riskSDKServiceClient) Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskSDKService_Health_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) DeviceGet(ctx context.Context, in *DeviceGetRequest, opts ...grpc.CallOption) (*DeviceGetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceGetResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_DeviceGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) BizInit(ctx context.Context, in *BizInitRequest, opts ...grpc.CallOption) (*BizInitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BizInitResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_BizInit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) GCaptcha(ctx context.Context, in *GCaptchaRequest, opts ...grpc.CallOption) (*GCaptchaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GCaptchaResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_GCaptcha_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) SDKCheckPhone(ctx context.Context, in *SDKCheckPhoneRequest, opts ...grpc.CallOption) (*SDKCheckPhoneResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SDKCheckPhoneResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_SDKCheckPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) SDKPhoneCode(ctx context.Context, in *SDKPhoneCodeRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskSDKService_SDKPhoneCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) SDKCheckSMSCode(ctx context.Context, in *SDKCheckCodeRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskSDKService_SDKCheckSMSCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) SDKCheckEmail(ctx context.Context, in *SDKCheckEmailRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskSDKService_SDKCheckEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) SendComet(ctx context.Context, in *SendCometRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskSDKService_SendComet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) NIDRisk(ctx context.Context, in *NIDCheckRequest, opts ...grpc.CallOption) (*NIDCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NIDCheckResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_NIDRisk_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) FaceCode(ctx context.Context, in *FaceCodeRequest, opts ...grpc.CallOption) (*FaceCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaceCodeResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_FaceCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskSDKServiceClient) FaceResult(ctx context.Context, in *FaceResultRequest, opts ...grpc.CallOption) (*FaceResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaceResultResponse)
	err := c.cc.Invoke(ctx, RiskSDKService_FaceResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskSDKServiceServer is the server API for RiskSDKService service.
// All implementations must embed UnimplementedRiskSDKServiceServer
// for forward compatibility.
//
// This API represents pay-risk service.
type RiskSDKServiceServer interface {
	// 健康检查
	Health(context.Context, *HealthRequest) (*xtype.Empty, error)
	// 获取设备号
	DeviceGet(context.Context, *DeviceGetRequest) (*DeviceGetResponse, error)
	// init接口
	BizInit(context.Context, *BizInitRequest) (*BizInitResponse, error)
	// 极验验证接口
	GCaptcha(context.Context, *GCaptchaRequest) (*GCaptchaResponse, error)
	SDKCheckPhone(context.Context, *SDKCheckPhoneRequest) (*SDKCheckPhoneResponse, error)
	SDKPhoneCode(context.Context, *SDKPhoneCodeRequest) (*xtype.Empty, error)
	SDKCheckSMSCode(context.Context, *SDKCheckCodeRequest) (*xtype.Empty, error)
	SDKCheckEmail(context.Context, *SDKCheckEmailRequest) (*xtype.Empty, error)
	SendComet(context.Context, *SendCometRequest) (*xtype.Empty, error)
	// 游戏账号风险信息查询
	NIDRisk(context.Context, *NIDCheckRequest) (*NIDCheckResponse, error)
	FaceCode(context.Context, *FaceCodeRequest) (*FaceCodeResponse, error)
	FaceResult(context.Context, *FaceResultRequest) (*FaceResultResponse, error)
	mustEmbedUnimplementedRiskSDKServiceServer()
}

// UnimplementedRiskSDKServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRiskSDKServiceServer struct{}

func (UnimplementedRiskSDKServiceServer) Health(context.Context, *HealthRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Health not implemented")
}
func (UnimplementedRiskSDKServiceServer) DeviceGet(context.Context, *DeviceGetRequest) (*DeviceGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceGet not implemented")
}
func (UnimplementedRiskSDKServiceServer) BizInit(context.Context, *BizInitRequest) (*BizInitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BizInit not implemented")
}
func (UnimplementedRiskSDKServiceServer) GCaptcha(context.Context, *GCaptchaRequest) (*GCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GCaptcha not implemented")
}
func (UnimplementedRiskSDKServiceServer) SDKCheckPhone(context.Context, *SDKCheckPhoneRequest) (*SDKCheckPhoneResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKCheckPhone not implemented")
}
func (UnimplementedRiskSDKServiceServer) SDKPhoneCode(context.Context, *SDKPhoneCodeRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKPhoneCode not implemented")
}
func (UnimplementedRiskSDKServiceServer) SDKCheckSMSCode(context.Context, *SDKCheckCodeRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKCheckSMSCode not implemented")
}
func (UnimplementedRiskSDKServiceServer) SDKCheckEmail(context.Context, *SDKCheckEmailRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKCheckEmail not implemented")
}
func (UnimplementedRiskSDKServiceServer) SendComet(context.Context, *SendCometRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendComet not implemented")
}
func (UnimplementedRiskSDKServiceServer) NIDRisk(context.Context, *NIDCheckRequest) (*NIDCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NIDRisk not implemented")
}
func (UnimplementedRiskSDKServiceServer) FaceCode(context.Context, *FaceCodeRequest) (*FaceCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceCode not implemented")
}
func (UnimplementedRiskSDKServiceServer) FaceResult(context.Context, *FaceResultRequest) (*FaceResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceResult not implemented")
}
func (UnimplementedRiskSDKServiceServer) mustEmbedUnimplementedRiskSDKServiceServer() {}
func (UnimplementedRiskSDKServiceServer) testEmbeddedByValue()                        {}

// UnsafeRiskSDKServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RiskSDKServiceServer will
// result in compilation errors.
type UnsafeRiskSDKServiceServer interface {
	mustEmbedUnimplementedRiskSDKServiceServer()
}

func RegisterRiskSDKServiceServer(s grpc.ServiceRegistrar, srv RiskSDKServiceServer) {
	// If the following call pancis, it indicates UnimplementedRiskSDKServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RiskSDKService_ServiceDesc, srv)
}

func _RiskSDKService_Health_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).Health(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_Health_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).Health(ctx, req.(*HealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_DeviceGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).DeviceGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_DeviceGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).DeviceGet(ctx, req.(*DeviceGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_BizInit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BizInitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).BizInit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_BizInit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).BizInit(ctx, req.(*BizInitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_GCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).GCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_GCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).GCaptcha(ctx, req.(*GCaptchaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_SDKCheckPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKCheckPhoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).SDKCheckPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_SDKCheckPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).SDKCheckPhone(ctx, req.(*SDKCheckPhoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_SDKPhoneCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKPhoneCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).SDKPhoneCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_SDKPhoneCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).SDKPhoneCode(ctx, req.(*SDKPhoneCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_SDKCheckSMSCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKCheckCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).SDKCheckSMSCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_SDKCheckSMSCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).SDKCheckSMSCode(ctx, req.(*SDKCheckCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_SDKCheckEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKCheckEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).SDKCheckEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_SDKCheckEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).SDKCheckEmail(ctx, req.(*SDKCheckEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_SendComet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCometRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).SendComet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_SendComet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).SendComet(ctx, req.(*SendCometRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_NIDRisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NIDCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).NIDRisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_NIDRisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).NIDRisk(ctx, req.(*NIDCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_FaceCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).FaceCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_FaceCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).FaceCode(ctx, req.(*FaceCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskSDKService_FaceResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskSDKServiceServer).FaceResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskSDKService_FaceResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskSDKServiceServer).FaceResult(ctx, req.(*FaceResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RiskSDKService_ServiceDesc is the grpc.ServiceDesc for RiskSDKService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RiskSDKService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.risk.RiskSDKService",
	HandlerType: (*RiskSDKServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Health",
			Handler:    _RiskSDKService_Health_Handler,
		},
		{
			MethodName: "DeviceGet",
			Handler:    _RiskSDKService_DeviceGet_Handler,
		},
		{
			MethodName: "BizInit",
			Handler:    _RiskSDKService_BizInit_Handler,
		},
		{
			MethodName: "GCaptcha",
			Handler:    _RiskSDKService_GCaptcha_Handler,
		},
		{
			MethodName: "SDKCheckPhone",
			Handler:    _RiskSDKService_SDKCheckPhone_Handler,
		},
		{
			MethodName: "SDKPhoneCode",
			Handler:    _RiskSDKService_SDKPhoneCode_Handler,
		},
		{
			MethodName: "SDKCheckSMSCode",
			Handler:    _RiskSDKService_SDKCheckSMSCode_Handler,
		},
		{
			MethodName: "SDKCheckEmail",
			Handler:    _RiskSDKService_SDKCheckEmail_Handler,
		},
		{
			MethodName: "SendComet",
			Handler:    _RiskSDKService_SendComet_Handler,
		},
		{
			MethodName: "NIDRisk",
			Handler:    _RiskSDKService_NIDRisk_Handler,
		},
		{
			MethodName: "FaceCode",
			Handler:    _RiskSDKService_FaceCode_Handler,
		},
		{
			MethodName: "FaceResult",
			Handler:    _RiskSDKService_FaceResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/sdk.proto",
}

syntax = "proto3";

package papegames.sparrow.risk;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "openapiv3/annotations.proto";
import "papegames/type/empty.proto";
import "papegames/type/raw_message.proto";
import "tagger/tagger.proto";
import "proto/base.proto";

option go_package = "risk/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "RiskProto";
option java_package = "com.papegames.sparrow.risk";

// This API represents pay-risk service.
service RiskSDKService {
  option (google.api.default_host) = "risk.papegames.com";

  // 健康检查
  rpc Health(HealthRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "p2"}}
      ]
    };
    option (google.api.http) = {
      get: "/v1/health",
    };
  }

  // 获取设备号
  rpc DeviceGet(DeviceGetRequest)returns(DeviceGetResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "获取DOID"
    };
    option (google.api.http) = {
      post: "/v1/risk/hd/get"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 数美获取设备号
  //  rpc ShumeiDeviceGet(ShumeiDeviceGetRequest)returns(DeviceGetResponse){
  //    option (openapi.v3.operation) = {
  //      specification_extension: [
  //        {name: "level", value: {yaml: "P0"}},
  //        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
  //      ],
  //      operation_id: "数美渠道获取设备号"
  //    };
  //    option (google.api.http) = {
  //      post: "/v1/risk/sd/get"
  //      body: "*"
  //    };
  //    option (google.api.method_signature) = "form-data";
  //  }

  //  rpc ShumeiDeviceConfig(ShumeiDeviceConfigRequest)returns(ShumeiDeviceConfigResponse){
  //    option (openapi.v3.operation) = {
  //      specification_extension: [
  //        {name: "level", value: {yaml: "P0"}},
  //        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
  //      ],
  //      operation_id: "数美渠道获取配置"
  //    };
  //    option (google.api.http) = {
  //      post: "/v1/risk/sd/config"
  //      body: "*"
  //    };
  //    option (google.api.method_signature) = "form-data";
  //  }


  // init接口
  rpc BizInit(BizInitRequest)returns(BizInitResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "初始化接口"
    };
    option (google.api.http) = {
      post: "/v1/risk/biz/init"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 极验验证接口
  rpc GCaptcha(GCaptchaRequest)returns(GCaptchaResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "极验验证接口"
    };
    option (google.api.http) = {
      post: "/v1/risk/captcha/g/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }


  rpc SDKCheckPhone(SDKCheckPhoneRequest)returns(SDKCheckPhoneResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "手机号码验证"
    };
    option (google.api.http) = {
      post: "/v1/risk/phone/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKPhoneCode(SDKPhoneCodeRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "手机号码单发短信"
    };
    option (google.api.http) = {
      post: "/v1/risk/phone/sendcode"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKCheckSMSCode(SDKCheckCodeRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "短信验证"
    };
    option (google.api.http) = {
      post: "/v1/risk/code/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKCheckEmail(SDKCheckEmailRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "邮箱地址验证"
    };
    option (google.api.http) = {
      post: "/v1/risk/email/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SendComet(SendCometRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "发送长链消息"
    };
    option (google.api.http) = {
      post: "/v1/risk/comet/send"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 游戏账号风险信息查询
  rpc NIDRisk(NIDCheckRequest)returns(NIDCheckResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "游戏账号风险信息查询"
    };
    option (google.api.http) = {
      post: "/v1/risk/gs/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc FaceCode(FaceCodeRequest)returns(FaceCodeResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "人脸验证初始化（移动端）"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/code"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }


  rpc FaceResult(FaceResultRequest)returns(FaceResultResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "人脸验证认证结果"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/result"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

}

message SendCometRequest{
  string client_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string nid = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint64 code = 3;
  string DOID = 4;
  string ip = 5;
  string account = 6;
}

message GCaptchaRequest {
  string number = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string captcha = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string token = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  string time = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  string captcha_id = 5 [
    (google.api.field_behavior) = REQUIRED
  ];
  string nonce = 6 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32 client_id = 7 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string app_id = 8 [
    (google.api.field_behavior) = REQUIRED
  ];
  string DOID = 9;
}

message GCaptchaResponse{
  string pass_token = 1;
}

message ShumeiDeviceGetRequest{

}
message ShumeiDeviceRequestData {
  string organization = 1 ;
  string os = 2;
  string app_id = 3;
  int32 encode = 4;
  int32 compress = 5;
  string tn = 6;
  string ep = 7;
  int32  retry = 8;
}

message ShumeiDeviceResponseData {
  SDKDeviceInfo extraInfo = 31;
}

message DeviceGetRequest {
  string information = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string third_device_id = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message ThirdDeviceIdData{
  string fingerprint = 1;
  string shumei = 2;
  string sdk_DOID = 3;
}


message DeviceInfo{
  string device_id = 1;
  string third_info = 3;
}

message DeviceGetResponse{
  string DOID = 1;
  string provider_device_id = 2;
}

message DeviceGetResponseData{
  int32 code = 2;
  string requestId = 3;
  Detail detail = 4;
}

message Detail{
  string DOID = 1;
  string deviceId = 2;
}

message DeviceLogsRequest{
  string DOID = 1;
  string information = 2;
}


message BizInitRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string include_keys = 2;
  string exclude_keys = 3;
}

message BizInitResponse{
  BizInitCaptchaData captcha = 1;
  bool oae = 2;
  bool ccr = 3;
  string interactive_msg = 4;
  map<string, papegames.type.RawMessage> options = 5;
}

message BizInitCaptchaData{
  string captcha_id = 1;
  bool  close = 2;
}

message HealthRequest {

}
message ThirdDeviceData{
  string third_device_id = 1;
  string third_data = 2;
}

message ShumeiDeviceConfigRequest{

}

message ShumeiDeviceConfigResponse{

}

message SDKCheckPhoneRequest{
  string nonce = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string phone = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 mock = 3;
  uint32 client_id = 5 [
    (google.api.field_behavior) = REQUIRED
  ];
  string app_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SDKCheckPhoneResponse{
  string  code = 1;
}

message SDKPhoneCodeRequest{
  string nonce = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32 client_id = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  string app_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SDKCheckCodeRequest{
  string nonce = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string code = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32 client_id = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  string app_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  string DOID = 5;
}

message SDKCheckEmailRequest{
  string nonce = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string email = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SDKDeviceInfo{
  string os = 1;
  string device_id = 2;
  string android_id = 3;
  string oaid = 4;
  string idfv = 5;
  string idfa = 6;
  string paper_device_id = 7;
  bool is_dark = 8;
}

message CreateDOID {
  SDKDeviceInfo SDKDeviceInfo = 1;
  string  DOID = 3;
  string  ip = 4;
  string  providerName = 5;
  string  thirdDeviceId = 6;
  ThirdDeviceIdData  thirdDeviceIdData = 7;
  string  deviceId = 8;
}

message CreateSdKDOID{
  string DOID = 1;
  string SDKDOID = 2;
}


message ShumeiRiskResponse {

  message Machine_account_risk {
    uint32 b_machine_control_tokenid = 1;
    uint64 b_machine_control_tokenid_last_ts = 2;
    uint32 b_offer_wall_tokenid = 3;
    uint64 b_offer_wall_tokenid_last_ts = 4;
  }

  message Ugc_account_risk {
    uint32 b_politics_risk_tokenid = 1;
    uint64 b_politics_risk_tokenid_last_ts = 2;
    uint32 b_sexy_risk_tokenid = 3;
    uint64 b_sexy_risk_tokenid_last_ts = 4;
    uint32 b_advertise_risk_tokenid = 5;
    uint64 b_advertise_risk_tokenid_last_ts = 6;
  }

  message Scene_account_risk {
    uint32 i_tout_risk_tokenid = 1;
    uint64 i_tout_risk_tokenid_last_ts = 2;
  }

  message Account_active_info {
    uint64 i_tokenid_first_active_timestamp = 1;
    uint32 i_tokenid_active_days_7d = 2;
    uint32 i_tokenid_active_days_4w = 3;
  }

  message Account_freq_info {
    uint32 i_tokenid_login_cnt_1d = 1;
    uint32 i_tokenid_login_cnt_7d = 2;
  }

  message Account_relate_info {
    uint32 i_tokenid_relate_smid_cnt_1d = 1;
    uint32 i_tokenid_relate_smid_cnt_7d = 2;
    uint32 i_tokenid_relate_ip_city_cnt_1d = 3;
    uint32 i_tokenid_relate_ip_city_cnt_7d = 4;
  }

  message S_tokenid_relate_smid_info_map_4w {
    string smid = 1;
    string days = 2;
  }

  message S_tokenid_relate_ip_city_info_map_4w {
    string city = 1;
    string days = 2;
  }

  message Account_common_info {
    repeated S_tokenid_relate_smid_info_map_4w s_tokenid_relate_smid_info_map_4w = 1;
    repeated S_tokenid_relate_ip_city_info_map_4w s_tokenid_relate_ip_city_info_map_4w = 2;
  }

  message Tokenlabels {
    Machine_account_risk machine_account_risk = 1;
    Ugc_account_risk UGC_account_risk = 2;
    Scene_account_risk scene_account_risk = 3;
    Account_active_info account_active_info = 4;
    Account_freq_info account_freq_info = 5;
    Account_relate_info account_relate_info = 6;
    Account_common_info account_common_info = 7;
  }

  message Other {
    uint32 b_mismatch = 1;
    uint64 b_mismatch_last_ts = 2;
  }

  message Fake_device {
    uint32 b_pc_emulator = 1;
    uint64 b_pc_emulator_last_ts = 2;
    string b_pc_emulator_pc_id = 3;
    uint32 b_cloud_device = 4;
    uint64 b_cloud_device_last_ts = 5;
    uint32 b_altered = 6;
    uint64 b_altered_last_ts = 7;
    uint32 b_multi_boxing = 8;
    uint64 b_multi_boxing_last_ts = 9;
    uint32 b_multi_boxing_by_os = 10;
    uint64 b_multi_boxing_by_os_last_ts = 11;
    uint32 b_multi_boxing_by_app = 12;
    uint64 b_multi_boxing_by_app_last_ts = 13;
    uint32 b_faker = 14;
    uint64 b_faker_last_ts = 15;
    uint32 b_farmer = 16;
    uint64 b_farmer_last_ts = 17;
    uint32 b_offerwall = 18;
    uint64 b_offerwall_last_ts = 19;
    Other other = 20;
  }

  message Device_suspicious_labels {
    uint32 b_root = 1;
    uint64 b_root_last_ts = 2;
    uint32 b_sim = 3;
    uint64 b_sim_last_ts = 4;
    uint32 b_debuggable = 5;
    uint64 b_debuggable_last_ts = 6;
    uint32 b_vpn = 7;
    uint64 b_vpn_last_ts = 8;
    uint32 b_monkey_apps = 9;
    uint64 b_monkey_apps_last_ts = 10;
    uint32 b_acc = 11;
    uint64 b_acc_last_ts = 12;
    uint32 b_multi_boxing_apps = 13;
    uint64 b_multi_boxing_apps_last_ts = 14;
    uint32 b_hook = 15;
    uint64 b_hook_last_ts = 16;
    uint32 b_vpn_apps = 17;
    uint64 b_vpn_apps_last_ts = 18;
    uint32 b_manufacture = 19;
    uint64 b_manufacture_last_ts = 20;
    uint32 b_icloud = 21;
    uint64 b_icloud_last_ts = 22;
    uint32 b_wx_code = 23;
    uint64 b_wx_code_last_ts = 24;
    uint32 b_sms_code = 25;
    uint64 b_sms_code_last_ts = 26;
    uint32 b_low_osver = 27;
    uint64 b_low_osver_last_ts = 28;
    uint32 b_remote_control_apps = 29;
    uint64 b_remote_control_apps_last_ts = 30;
    uint32 b_repackage = 31;
    uint64 b_repackage_last_ts = 32;
    uint32 b_reset = 33;
  }

  message Device_active_info {
    uint64 i_smid_boot_timestamp = 1;
  }

  message Common {
    uint32 b_monkey_apps = 1;
    uint64 b_monkey_apps_last_ts = 2;
  }

  message Monkey_game {
    uint32 b_monkey_game_apps = 1;
    uint64 b_monkey_game_apps_last_ts = 2;
  }

  message Monkey_read {
    uint32 b_monkey_read_apps = 1;
    uint64 b_monkey_read_apps_last_ts = 2;
  }

  message Monkey_device {
    Common common = 1;
    Monkey_game monkey_game = 2;
    Monkey_read monkey_read = 3;
  }

  message Devicelabels {
    string id = 1;
    Fake_device fake_device = 2;
    Device_suspicious_labels device_suspicious_labels = 3;
    Device_active_info device_active_info = 4;
    Monkey_device monkey_device = 5;
  }

  message Detail {
  }

  message Tokenprofilelabels {
    string label1 = 1;
    string lable2 = 2;
    string label3 = 3;
    string description = 4;
    uint64 timestamp = 5;
    Detail detail = 6;
  }

  message Tokenrisklabels {
    string label1 = 1;
    string label2 = 2;
    string label3 = 3;
    string description = 4;
    uint64 timestamp = 5;
    Detail detail = 6;
  }

  message Devicerisklabels {
    string label1 = 1;
    string label2 = 2;
    string label3 = 3;
    string description = 4;
    uint64 timestamp = 5;
    Detail detail = 6;
  }

  uint32 code = 1;
  string message = 2;
  uint32 profileExist = 3;
  string requestId = 4;
  //  Tokenlabels tokenLabels = 5;
  Devicelabels deviceLabels = 6;
  //  repeated Tokenprofilelabels tokenProfileLabels = 7;
  //  repeated Tokenrisklabels tokenRiskLabels = 8;
  //  repeated Devicerisklabels deviceRiskLabels = 9;
}

message NIDCheckRequest {
  string nid = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string ip = 2;
  uint32 client_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  string DOID = 6;
  string sig = 7 [
    (google.api.field_behavior) = REQUIRED
  ];
  int64  timestamp = 8 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message NIDCheckResponse {
  option (tagger.disable_omitempty) = true;
  int64 risk_score = 1;
}


message FaceCodeRequest {
  string nid = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string token = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string scene = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  string vendor = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  string extra = 5;
  int64 client_id = 6 [
    (google.api.field_behavior) = REQUIRED
  ];
  string DOID = 7 [
    (google.api.field_behavior) = REQUIRED
  ];
}


message FaceCodeResponse {
  option (tagger.disable_omitempty) = true;
  string  vendor = 1;
  string code = 2;
  FaceServiceData service = 3;
}


message FaceCodeVal {
  string  service = 1;
  string  nid = 2;
  string  scene = 3;
  string  vendor = 4;
  string  id_card = 6;
  string  real_name = 7;
}

message FaceRdbTokeVal{
  int64  client_id = 1;
  string DOID = 2;
  string ip = 3;
  string nid = 4;
  string scene = 5;
  string id_card = 6;
  string real_name = 7;
}

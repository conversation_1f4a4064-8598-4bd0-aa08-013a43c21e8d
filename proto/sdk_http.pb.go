// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.5.0
// protoc          v4.25.1
// source: risk
package proto

import (
	context "context"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func RegisterRiskSDKServiceGinServer(s *xgin.Server, srv RiskSDKServiceServer) {
	eng := s.GetGinEngine()
	xgin.RegisterHandler(eng,
		"GET", "/v1/health",
		_RiskSDKServiceGin_Health_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/health", "Health")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/hd/get",
		_RiskSDKServiceGin_DeviceGet_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/hd/get", "DeviceGet")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/biz/init",
		_RiskSDKServiceGin_BizInit_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/biz/init", "BizInit")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/captcha/g/check",
		_RiskSDKServiceGin_GCaptcha_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/captcha/g/check", "GCaptcha")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/phone/check",
		_RiskSDKServiceGin_SDKCheckPhone_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/phone/check", "SDKCheckPhone")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/phone/sendcode",
		_RiskSDKServiceGin_SDKPhoneCode_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/phone/sendcode", "SDKPhoneCode")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/code/check",
		_RiskSDKServiceGin_SDKCheckSMSCode_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/code/check", "SDKCheckSMSCode")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/email/check",
		_RiskSDKServiceGin_SDKCheckEmail_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/email/check", "SDKCheckEmail")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/comet/send",
		_RiskSDKServiceGin_SendComet_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/comet/send", "SendComet")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/gs/check",
		_RiskSDKServiceGin_NIDRisk_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/gs/check", "NIDRisk")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/face/code",
		_RiskSDKServiceGin_FaceCode_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/face/code", "FaceCode")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/risk/face/result",
		_RiskSDKServiceGin_FaceResult_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/risk/face/result", "FaceResult")
}

func _RiskSDKServiceGin_Health_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/health",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Health(ctx, in.(*HealthRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(HealthRequest)
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Health(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_DeviceGet_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/hd/get",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeviceGet(ctx, in.(*DeviceGetRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(DeviceGetRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeviceGet(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_BizInit_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/biz/init",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.BizInit(ctx, in.(*BizInitRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(BizInitRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.BizInit(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_GCaptcha_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/captcha/g/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GCaptcha(ctx, in.(*GCaptchaRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(GCaptchaRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GCaptcha(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_SDKCheckPhone_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/phone/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKCheckPhone(ctx, in.(*SDKCheckPhoneRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKCheckPhoneRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKCheckPhone(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_SDKPhoneCode_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/phone/sendcode",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKPhoneCode(ctx, in.(*SDKPhoneCodeRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKPhoneCodeRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKPhoneCode(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_SDKCheckSMSCode_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/code/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKCheckSMSCode(ctx, in.(*SDKCheckCodeRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKCheckCodeRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKCheckSMSCode(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_SDKCheckEmail_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/email/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SDKCheckEmail(ctx, in.(*SDKCheckEmailRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SDKCheckEmailRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SDKCheckEmail(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_SendComet_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/comet/send",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SendComet(ctx, in.(*SendCometRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(SendCometRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SendComet(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_NIDRisk_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/gs/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.NIDRisk(ctx, in.(*NIDCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(NIDCheckRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.NIDRisk(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_FaceCode_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/face/code",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.FaceCode(ctx, in.(*FaceCodeRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(FaceCodeRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.FaceCode(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _RiskSDKServiceGin_FaceResult_Handler(srv RiskSDKServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/risk/face/result",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.FaceResult(ctx, in.(*FaceResultRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(FaceResultRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.FaceResult(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: proto/server.proto

package proto

import (
	context "context"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RiskServerService_CaptchaTokenCheck_FullMethodName       = "/papegames.sparrow.risk.RiskServerService/CaptchaTokenCheck"
	RiskServerService_AppCheckRisk_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/AppCheckRisk"
	RiskServerService_AccountCheckRisk_FullMethodName        = "/papegames.sparrow.risk.RiskServerService/AccountCheckRisk"
	RiskServerService_PayCheckRisk_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/PayCheckRisk"
	RiskServerService_DeviceRisk_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/DeviceRisk"
	RiskServerService_AddBlackList_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/AddBlackList"
	RiskServerService_EditBlackList_FullMethodName           = "/papegames.sparrow.risk.RiskServerService/EditBlackList"
	RiskServerService_DeleteBlackList_FullMethodName         = "/papegames.sparrow.risk.RiskServerService/DeleteBlackList"
	RiskServerService_AddWhiteList_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/AddWhiteList"
	RiskServerService_EditWhiteList_FullMethodName           = "/papegames.sparrow.risk.RiskServerService/EditWhiteList"
	RiskServerService_DeleteWhiteList_FullMethodName         = "/papegames.sparrow.risk.RiskServerService/DeleteWhiteList"
	RiskServerService_AddDarkList_FullMethodName             = "/papegames.sparrow.risk.RiskServerService/AddDarkList"
	RiskServerService_EditDarkList_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/EditDarkList"
	RiskServerService_DeleteDarkList_FullMethodName          = "/papegames.sparrow.risk.RiskServerService/DeleteDarkList"
	RiskServerService_AddConfigApp_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/AddConfigApp"
	RiskServerService_EditConfigApp_FullMethodName           = "/papegames.sparrow.risk.RiskServerService/EditConfigApp"
	RiskServerService_DeleteConfigApp_FullMethodName         = "/papegames.sparrow.risk.RiskServerService/DeleteConfigApp"
	RiskServerService_SDKSendComet_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/SDKSendComet"
	RiskServerService_ReloadGeetestConfig_FullMethodName     = "/papegames.sparrow.risk.RiskServerService/ReloadGeetestConfig"
	RiskServerService_DeleteApproved_FullMethodName          = "/papegames.sparrow.risk.RiskServerService/DeleteApproved"
	RiskServerService_DecodeData_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/DecodeData"
	RiskServerService_EncodeData_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/encodeData"
	RiskServerService_CheckAppId_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/checkAppId"
	RiskServerService_SDKDOIDCheck_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/SDKDOIDCheck"
	RiskServerService_ShumeiDecode_FullMethodName            = "/papegames.sparrow.risk.RiskServerService/ShumeiDecode"
	RiskServerService_BindNotice_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/BindNotice"
	RiskServerService_SDKDOIDToDOID_FullMethodName           = "/papegames.sparrow.risk.RiskServerService/SDKDOIDToDOID"
	RiskServerService_FaceStatus_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/FaceStatus"
	RiskServerService_FaceCodeForWeb_FullMethodName          = "/papegames.sparrow.risk.RiskServerService/FaceCodeForWeb"
	RiskServerService_QrCodeGenerate_FullMethodName          = "/papegames.sparrow.risk.RiskServerService/QrCodeGenerate"
	RiskServerService_QrCodeScan_FullMethodName              = "/papegames.sparrow.risk.RiskServerService/QrCodeScan"
	RiskServerService_FaceStatusQueryByQrCode_FullMethodName = "/papegames.sparrow.risk.RiskServerService/FaceStatusQueryByQrCode"
)

// RiskServerServiceClient is the client API for RiskServerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// This API represents pay-risk service.
type RiskServerServiceClient interface {
	// 极验服务端接口
	CaptchaTokenCheck(ctx context.Context, in *CaptchaTokenCheckRequest, opts ...grpc.CallOption) (*CaptchaTokenCheckResponse, error)
	// 业务风控检测
	AppCheckRisk(ctx context.Context, in *AppRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error)
	// 账号风控检测
	AccountCheckRisk(ctx context.Context, in *AccountRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error)
	// 支付风控检测
	PayCheckRisk(ctx context.Context, in *PayRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error)
	// 设备风险信息查询
	DeviceRisk(ctx context.Context, in *DeviceRiskRequest, opts ...grpc.CallOption) (*DeviceRiskResponse, error)
	// 黑名单列表-添加
	AddBlackList(ctx context.Context, in *AddBlackListRequest, opts ...grpc.CallOption) (*AddResponse, error)
	// 黑名单列表-修改
	EditBlackList(ctx context.Context, in *EditBlackListRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 黑名单列表-删除
	DeleteBlackList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 白名单列表-添加
	AddWhiteList(ctx context.Context, in *AddWhiteListRequest, opts ...grpc.CallOption) (*AddResponse, error)
	// 白名单列表-修改
	EditWhiteList(ctx context.Context, in *EditWhiteListRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 白名单列表-删除
	DeleteWhiteList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 黑产列表-添加
	AddDarkList(ctx context.Context, in *AddDarkListRequest, opts ...grpc.CallOption) (*AddResponse, error)
	// 黑产列表-修改
	EditDarkList(ctx context.Context, in *EditDarkListRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 黑产列表-删除
	DeleteDarkList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 风控开关配置-添加
	AddConfigApp(ctx context.Context, in *AddConfigAppRequest, opts ...grpc.CallOption) (*AddResponse, error)
	// 风控开关配置-修改
	EditConfigApp(ctx context.Context, in *EditConfigAppRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 风控开关配置-删除
	DeleteConfigApp(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	SDKSendComet(ctx context.Context, in *SDKSendCometRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	ReloadGeetestConfig(ctx context.Context, in *ReloadGeetestConfigRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	DeleteApproved(ctx context.Context, in *DeleteApprovedRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 加密数据解密
	DecodeData(ctx context.Context, in *DecodeDataRequest, opts ...grpc.CallOption) (*DecodeDataResponse, error)
	// 数据加密
	EncodeData(ctx context.Context, in *DecodeDataRequest, opts ...grpc.CallOption) (*DecodeDataResponse, error)
	// appId 验证
	CheckAppId(ctx context.Context, in *CheckAppIdRequest, opts ...grpc.CallOption) (*CheckAppIdRequest, error)
	SDKDOIDCheck(ctx context.Context, in *SDKDOIDCheckRequest, opts ...grpc.CallOption) (*SDKDOIDCheckResponse, error)
	ShumeiDecode(ctx context.Context, in *ShumeiDecodeRequest, opts ...grpc.CallOption) (*ShumeiDecodeResponse, error)
	BindNotice(ctx context.Context, in *BindNoticeRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	SDKDOIDToDOID(ctx context.Context, in *SDKDOIDRequest, opts ...grpc.CallOption) (*SDKDOIDResponse, error)
	FaceStatus(ctx context.Context, in *FaceStatusRequest, opts ...grpc.CallOption) (*FaceStatusResponse, error)
	FaceCodeForWeb(ctx context.Context, in *FaceCodeForWebRequest, opts ...grpc.CallOption) (*FaceCodeForWebResponse, error)
	QrCodeGenerate(ctx context.Context, in *QrCodeGenerateRequest, opts ...grpc.CallOption) (*QrCodeGenerateResponse, error)
	QrCodeScan(ctx context.Context, in *QrCodeScanRequest, opts ...grpc.CallOption) (*QrCodeScanResponse, error)
	FaceStatusQueryByQrCode(ctx context.Context, in *FaceStatusQueryByQrCodeRequest, opts ...grpc.CallOption) (*FaceStatusQueryByQrCodeResponse, error)
}

type riskServerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskServerServiceClient(cc grpc.ClientConnInterface) RiskServerServiceClient {
	return &riskServerServiceClient{cc}
}

func (c *riskServerServiceClient) CaptchaTokenCheck(ctx context.Context, in *CaptchaTokenCheckRequest, opts ...grpc.CallOption) (*CaptchaTokenCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CaptchaTokenCheckResponse)
	err := c.cc.Invoke(ctx, RiskServerService_CaptchaTokenCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AppCheckRisk(ctx context.Context, in *AppRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandlerRequest)
	err := c.cc.Invoke(ctx, RiskServerService_AppCheckRisk_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AccountCheckRisk(ctx context.Context, in *AccountRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandlerRequest)
	err := c.cc.Invoke(ctx, RiskServerService_AccountCheckRisk_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) PayCheckRisk(ctx context.Context, in *PayRiskCheckRequest, opts ...grpc.CallOption) (*HandlerRequest, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandlerRequest)
	err := c.cc.Invoke(ctx, RiskServerService_PayCheckRisk_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeviceRisk(ctx context.Context, in *DeviceRiskRequest, opts ...grpc.CallOption) (*DeviceRiskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceRiskResponse)
	err := c.cc.Invoke(ctx, RiskServerService_DeviceRisk_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AddBlackList(ctx context.Context, in *AddBlackListRequest, opts ...grpc.CallOption) (*AddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddResponse)
	err := c.cc.Invoke(ctx, RiskServerService_AddBlackList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) EditBlackList(ctx context.Context, in *EditBlackListRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_EditBlackList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeleteBlackList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_DeleteBlackList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AddWhiteList(ctx context.Context, in *AddWhiteListRequest, opts ...grpc.CallOption) (*AddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddResponse)
	err := c.cc.Invoke(ctx, RiskServerService_AddWhiteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) EditWhiteList(ctx context.Context, in *EditWhiteListRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_EditWhiteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeleteWhiteList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_DeleteWhiteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AddDarkList(ctx context.Context, in *AddDarkListRequest, opts ...grpc.CallOption) (*AddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddResponse)
	err := c.cc.Invoke(ctx, RiskServerService_AddDarkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) EditDarkList(ctx context.Context, in *EditDarkListRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_EditDarkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeleteDarkList(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_DeleteDarkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) AddConfigApp(ctx context.Context, in *AddConfigAppRequest, opts ...grpc.CallOption) (*AddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddResponse)
	err := c.cc.Invoke(ctx, RiskServerService_AddConfigApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) EditConfigApp(ctx context.Context, in *EditConfigAppRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_EditConfigApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeleteConfigApp(ctx context.Context, in *ClientIdRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_DeleteConfigApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) SDKSendComet(ctx context.Context, in *SDKSendCometRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_SDKSendComet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) ReloadGeetestConfig(ctx context.Context, in *ReloadGeetestConfigRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_ReloadGeetestConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DeleteApproved(ctx context.Context, in *DeleteApprovedRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_DeleteApproved_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) DecodeData(ctx context.Context, in *DecodeDataRequest, opts ...grpc.CallOption) (*DecodeDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecodeDataResponse)
	err := c.cc.Invoke(ctx, RiskServerService_DecodeData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) EncodeData(ctx context.Context, in *DecodeDataRequest, opts ...grpc.CallOption) (*DecodeDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecodeDataResponse)
	err := c.cc.Invoke(ctx, RiskServerService_EncodeData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) CheckAppId(ctx context.Context, in *CheckAppIdRequest, opts ...grpc.CallOption) (*CheckAppIdRequest, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckAppIdRequest)
	err := c.cc.Invoke(ctx, RiskServerService_CheckAppId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) SDKDOIDCheck(ctx context.Context, in *SDKDOIDCheckRequest, opts ...grpc.CallOption) (*SDKDOIDCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SDKDOIDCheckResponse)
	err := c.cc.Invoke(ctx, RiskServerService_SDKDOIDCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) ShumeiDecode(ctx context.Context, in *ShumeiDecodeRequest, opts ...grpc.CallOption) (*ShumeiDecodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ShumeiDecodeResponse)
	err := c.cc.Invoke(ctx, RiskServerService_ShumeiDecode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) BindNotice(ctx context.Context, in *BindNoticeRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, RiskServerService_BindNotice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) SDKDOIDToDOID(ctx context.Context, in *SDKDOIDRequest, opts ...grpc.CallOption) (*SDKDOIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SDKDOIDResponse)
	err := c.cc.Invoke(ctx, RiskServerService_SDKDOIDToDOID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) FaceStatus(ctx context.Context, in *FaceStatusRequest, opts ...grpc.CallOption) (*FaceStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaceStatusResponse)
	err := c.cc.Invoke(ctx, RiskServerService_FaceStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) FaceCodeForWeb(ctx context.Context, in *FaceCodeForWebRequest, opts ...grpc.CallOption) (*FaceCodeForWebResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaceCodeForWebResponse)
	err := c.cc.Invoke(ctx, RiskServerService_FaceCodeForWeb_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) QrCodeGenerate(ctx context.Context, in *QrCodeGenerateRequest, opts ...grpc.CallOption) (*QrCodeGenerateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QrCodeGenerateResponse)
	err := c.cc.Invoke(ctx, RiskServerService_QrCodeGenerate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) QrCodeScan(ctx context.Context, in *QrCodeScanRequest, opts ...grpc.CallOption) (*QrCodeScanResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QrCodeScanResponse)
	err := c.cc.Invoke(ctx, RiskServerService_QrCodeScan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskServerServiceClient) FaceStatusQueryByQrCode(ctx context.Context, in *FaceStatusQueryByQrCodeRequest, opts ...grpc.CallOption) (*FaceStatusQueryByQrCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FaceStatusQueryByQrCodeResponse)
	err := c.cc.Invoke(ctx, RiskServerService_FaceStatusQueryByQrCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskServerServiceServer is the server API for RiskServerService service.
// All implementations must embed UnimplementedRiskServerServiceServer
// for forward compatibility.
//
// This API represents pay-risk service.
type RiskServerServiceServer interface {
	// 极验服务端接口
	CaptchaTokenCheck(context.Context, *CaptchaTokenCheckRequest) (*CaptchaTokenCheckResponse, error)
	// 业务风控检测
	AppCheckRisk(context.Context, *AppRiskCheckRequest) (*HandlerRequest, error)
	// 账号风控检测
	AccountCheckRisk(context.Context, *AccountRiskCheckRequest) (*HandlerRequest, error)
	// 支付风控检测
	PayCheckRisk(context.Context, *PayRiskCheckRequest) (*HandlerRequest, error)
	// 设备风险信息查询
	DeviceRisk(context.Context, *DeviceRiskRequest) (*DeviceRiskResponse, error)
	// 黑名单列表-添加
	AddBlackList(context.Context, *AddBlackListRequest) (*AddResponse, error)
	// 黑名单列表-修改
	EditBlackList(context.Context, *EditBlackListRequest) (*xtype.Empty, error)
	// 黑名单列表-删除
	DeleteBlackList(context.Context, *ClientIdRequest) (*xtype.Empty, error)
	// 白名单列表-添加
	AddWhiteList(context.Context, *AddWhiteListRequest) (*AddResponse, error)
	// 白名单列表-修改
	EditWhiteList(context.Context, *EditWhiteListRequest) (*xtype.Empty, error)
	// 白名单列表-删除
	DeleteWhiteList(context.Context, *ClientIdRequest) (*xtype.Empty, error)
	// 黑产列表-添加
	AddDarkList(context.Context, *AddDarkListRequest) (*AddResponse, error)
	// 黑产列表-修改
	EditDarkList(context.Context, *EditDarkListRequest) (*xtype.Empty, error)
	// 黑产列表-删除
	DeleteDarkList(context.Context, *ClientIdRequest) (*xtype.Empty, error)
	// 风控开关配置-添加
	AddConfigApp(context.Context, *AddConfigAppRequest) (*AddResponse, error)
	// 风控开关配置-修改
	EditConfigApp(context.Context, *EditConfigAppRequest) (*xtype.Empty, error)
	// 风控开关配置-删除
	DeleteConfigApp(context.Context, *ClientIdRequest) (*xtype.Empty, error)
	SDKSendComet(context.Context, *SDKSendCometRequest) (*xtype.Empty, error)
	ReloadGeetestConfig(context.Context, *ReloadGeetestConfigRequest) (*xtype.Empty, error)
	DeleteApproved(context.Context, *DeleteApprovedRequest) (*xtype.Empty, error)
	// 加密数据解密
	DecodeData(context.Context, *DecodeDataRequest) (*DecodeDataResponse, error)
	// 数据加密
	EncodeData(context.Context, *DecodeDataRequest) (*DecodeDataResponse, error)
	// appId 验证
	CheckAppId(context.Context, *CheckAppIdRequest) (*CheckAppIdRequest, error)
	SDKDOIDCheck(context.Context, *SDKDOIDCheckRequest) (*SDKDOIDCheckResponse, error)
	ShumeiDecode(context.Context, *ShumeiDecodeRequest) (*ShumeiDecodeResponse, error)
	BindNotice(context.Context, *BindNoticeRequest) (*xtype.Empty, error)
	SDKDOIDToDOID(context.Context, *SDKDOIDRequest) (*SDKDOIDResponse, error)
	FaceStatus(context.Context, *FaceStatusRequest) (*FaceStatusResponse, error)
	FaceCodeForWeb(context.Context, *FaceCodeForWebRequest) (*FaceCodeForWebResponse, error)
	QrCodeGenerate(context.Context, *QrCodeGenerateRequest) (*QrCodeGenerateResponse, error)
	QrCodeScan(context.Context, *QrCodeScanRequest) (*QrCodeScanResponse, error)
	FaceStatusQueryByQrCode(context.Context, *FaceStatusQueryByQrCodeRequest) (*FaceStatusQueryByQrCodeResponse, error)
	mustEmbedUnimplementedRiskServerServiceServer()
}

// UnimplementedRiskServerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRiskServerServiceServer struct{}

func (UnimplementedRiskServerServiceServer) CaptchaTokenCheck(context.Context, *CaptchaTokenCheckRequest) (*CaptchaTokenCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CaptchaTokenCheck not implemented")
}
func (UnimplementedRiskServerServiceServer) AppCheckRisk(context.Context, *AppRiskCheckRequest) (*HandlerRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AppCheckRisk not implemented")
}
func (UnimplementedRiskServerServiceServer) AccountCheckRisk(context.Context, *AccountRiskCheckRequest) (*HandlerRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountCheckRisk not implemented")
}
func (UnimplementedRiskServerServiceServer) PayCheckRisk(context.Context, *PayRiskCheckRequest) (*HandlerRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCheckRisk not implemented")
}
func (UnimplementedRiskServerServiceServer) DeviceRisk(context.Context, *DeviceRiskRequest) (*DeviceRiskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceRisk not implemented")
}
func (UnimplementedRiskServerServiceServer) AddBlackList(context.Context, *AddBlackListRequest) (*AddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackList not implemented")
}
func (UnimplementedRiskServerServiceServer) EditBlackList(context.Context, *EditBlackListRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditBlackList not implemented")
}
func (UnimplementedRiskServerServiceServer) DeleteBlackList(context.Context, *ClientIdRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackList not implemented")
}
func (UnimplementedRiskServerServiceServer) AddWhiteList(context.Context, *AddWhiteListRequest) (*AddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWhiteList not implemented")
}
func (UnimplementedRiskServerServiceServer) EditWhiteList(context.Context, *EditWhiteListRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditWhiteList not implemented")
}
func (UnimplementedRiskServerServiceServer) DeleteWhiteList(context.Context, *ClientIdRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWhiteList not implemented")
}
func (UnimplementedRiskServerServiceServer) AddDarkList(context.Context, *AddDarkListRequest) (*AddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDarkList not implemented")
}
func (UnimplementedRiskServerServiceServer) EditDarkList(context.Context, *EditDarkListRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditDarkList not implemented")
}
func (UnimplementedRiskServerServiceServer) DeleteDarkList(context.Context, *ClientIdRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDarkList not implemented")
}
func (UnimplementedRiskServerServiceServer) AddConfigApp(context.Context, *AddConfigAppRequest) (*AddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddConfigApp not implemented")
}
func (UnimplementedRiskServerServiceServer) EditConfigApp(context.Context, *EditConfigAppRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditConfigApp not implemented")
}
func (UnimplementedRiskServerServiceServer) DeleteConfigApp(context.Context, *ClientIdRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConfigApp not implemented")
}
func (UnimplementedRiskServerServiceServer) SDKSendComet(context.Context, *SDKSendCometRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKSendComet not implemented")
}
func (UnimplementedRiskServerServiceServer) ReloadGeetestConfig(context.Context, *ReloadGeetestConfigRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadGeetestConfig not implemented")
}
func (UnimplementedRiskServerServiceServer) DeleteApproved(context.Context, *DeleteApprovedRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteApproved not implemented")
}
func (UnimplementedRiskServerServiceServer) DecodeData(context.Context, *DecodeDataRequest) (*DecodeDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecodeData not implemented")
}
func (UnimplementedRiskServerServiceServer) EncodeData(context.Context, *DecodeDataRequest) (*DecodeDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EncodeData not implemented")
}
func (UnimplementedRiskServerServiceServer) CheckAppId(context.Context, *CheckAppIdRequest) (*CheckAppIdRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAppId not implemented")
}
func (UnimplementedRiskServerServiceServer) SDKDOIDCheck(context.Context, *SDKDOIDCheckRequest) (*SDKDOIDCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKDOIDCheck not implemented")
}
func (UnimplementedRiskServerServiceServer) ShumeiDecode(context.Context, *ShumeiDecodeRequest) (*ShumeiDecodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShumeiDecode not implemented")
}
func (UnimplementedRiskServerServiceServer) BindNotice(context.Context, *BindNoticeRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindNotice not implemented")
}
func (UnimplementedRiskServerServiceServer) SDKDOIDToDOID(context.Context, *SDKDOIDRequest) (*SDKDOIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SDKDOIDToDOID not implemented")
}
func (UnimplementedRiskServerServiceServer) FaceStatus(context.Context, *FaceStatusRequest) (*FaceStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceStatus not implemented")
}
func (UnimplementedRiskServerServiceServer) FaceCodeForWeb(context.Context, *FaceCodeForWebRequest) (*FaceCodeForWebResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceCodeForWeb not implemented")
}
func (UnimplementedRiskServerServiceServer) QrCodeGenerate(context.Context, *QrCodeGenerateRequest) (*QrCodeGenerateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QrCodeGenerate not implemented")
}
func (UnimplementedRiskServerServiceServer) QrCodeScan(context.Context, *QrCodeScanRequest) (*QrCodeScanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QrCodeScan not implemented")
}
func (UnimplementedRiskServerServiceServer) FaceStatusQueryByQrCode(context.Context, *FaceStatusQueryByQrCodeRequest) (*FaceStatusQueryByQrCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceStatusQueryByQrCode not implemented")
}
func (UnimplementedRiskServerServiceServer) mustEmbedUnimplementedRiskServerServiceServer() {}
func (UnimplementedRiskServerServiceServer) testEmbeddedByValue()                           {}

// UnsafeRiskServerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RiskServerServiceServer will
// result in compilation errors.
type UnsafeRiskServerServiceServer interface {
	mustEmbedUnimplementedRiskServerServiceServer()
}

func RegisterRiskServerServiceServer(s grpc.ServiceRegistrar, srv RiskServerServiceServer) {
	// If the following call pancis, it indicates UnimplementedRiskServerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RiskServerService_ServiceDesc, srv)
}

func _RiskServerService_CaptchaTokenCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CaptchaTokenCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).CaptchaTokenCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_CaptchaTokenCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).CaptchaTokenCheck(ctx, req.(*CaptchaTokenCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AppCheckRisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppRiskCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AppCheckRisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AppCheckRisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AppCheckRisk(ctx, req.(*AppRiskCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AccountCheckRisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountRiskCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AccountCheckRisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AccountCheckRisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AccountCheckRisk(ctx, req.(*AccountRiskCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_PayCheckRisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRiskCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).PayCheckRisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_PayCheckRisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).PayCheckRisk(ctx, req.(*PayRiskCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeviceRisk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRiskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeviceRisk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeviceRisk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeviceRisk(ctx, req.(*DeviceRiskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AddBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AddBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AddBlackList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AddBlackList(ctx, req.(*AddBlackListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_EditBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditBlackListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).EditBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_EditBlackList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).EditBlackList(ctx, req.(*EditBlackListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeleteBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeleteBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeleteBlackList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeleteBlackList(ctx, req.(*ClientIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AddWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWhiteListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AddWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AddWhiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AddWhiteList(ctx, req.(*AddWhiteListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_EditWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditWhiteListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).EditWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_EditWhiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).EditWhiteList(ctx, req.(*EditWhiteListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeleteWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeleteWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeleteWhiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeleteWhiteList(ctx, req.(*ClientIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AddDarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDarkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AddDarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AddDarkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AddDarkList(ctx, req.(*AddDarkListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_EditDarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditDarkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).EditDarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_EditDarkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).EditDarkList(ctx, req.(*EditDarkListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeleteDarkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeleteDarkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeleteDarkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeleteDarkList(ctx, req.(*ClientIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_AddConfigApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConfigAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).AddConfigApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_AddConfigApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).AddConfigApp(ctx, req.(*AddConfigAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_EditConfigApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditConfigAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).EditConfigApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_EditConfigApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).EditConfigApp(ctx, req.(*EditConfigAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeleteConfigApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeleteConfigApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeleteConfigApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeleteConfigApp(ctx, req.(*ClientIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_SDKSendComet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKSendCometRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).SDKSendComet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_SDKSendComet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).SDKSendComet(ctx, req.(*SDKSendCometRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_ReloadGeetestConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadGeetestConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).ReloadGeetestConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_ReloadGeetestConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).ReloadGeetestConfig(ctx, req.(*ReloadGeetestConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DeleteApproved_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteApprovedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DeleteApproved(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DeleteApproved_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DeleteApproved(ctx, req.(*DeleteApprovedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_DecodeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).DecodeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_DecodeData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).DecodeData(ctx, req.(*DecodeDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_EncodeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).EncodeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_EncodeData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).EncodeData(ctx, req.(*DecodeDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_CheckAppId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAppIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).CheckAppId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_CheckAppId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).CheckAppId(ctx, req.(*CheckAppIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_SDKDOIDCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKDOIDCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).SDKDOIDCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_SDKDOIDCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).SDKDOIDCheck(ctx, req.(*SDKDOIDCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_ShumeiDecode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShumeiDecodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).ShumeiDecode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_ShumeiDecode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).ShumeiDecode(ctx, req.(*ShumeiDecodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_BindNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).BindNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_BindNotice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).BindNotice(ctx, req.(*BindNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_SDKDOIDToDOID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SDKDOIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).SDKDOIDToDOID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_SDKDOIDToDOID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).SDKDOIDToDOID(ctx, req.(*SDKDOIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_FaceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).FaceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_FaceStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).FaceStatus(ctx, req.(*FaceStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_FaceCodeForWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceCodeForWebRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).FaceCodeForWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_FaceCodeForWeb_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).FaceCodeForWeb(ctx, req.(*FaceCodeForWebRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_QrCodeGenerate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrCodeGenerateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).QrCodeGenerate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_QrCodeGenerate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).QrCodeGenerate(ctx, req.(*QrCodeGenerateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_QrCodeScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrCodeScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).QrCodeScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_QrCodeScan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).QrCodeScan(ctx, req.(*QrCodeScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskServerService_FaceStatusQueryByQrCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceStatusQueryByQrCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskServerServiceServer).FaceStatusQueryByQrCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskServerService_FaceStatusQueryByQrCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskServerServiceServer).FaceStatusQueryByQrCode(ctx, req.(*FaceStatusQueryByQrCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RiskServerService_ServiceDesc is the grpc.ServiceDesc for RiskServerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RiskServerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.risk.RiskServerService",
	HandlerType: (*RiskServerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CaptchaTokenCheck",
			Handler:    _RiskServerService_CaptchaTokenCheck_Handler,
		},
		{
			MethodName: "AppCheckRisk",
			Handler:    _RiskServerService_AppCheckRisk_Handler,
		},
		{
			MethodName: "AccountCheckRisk",
			Handler:    _RiskServerService_AccountCheckRisk_Handler,
		},
		{
			MethodName: "PayCheckRisk",
			Handler:    _RiskServerService_PayCheckRisk_Handler,
		},
		{
			MethodName: "DeviceRisk",
			Handler:    _RiskServerService_DeviceRisk_Handler,
		},
		{
			MethodName: "AddBlackList",
			Handler:    _RiskServerService_AddBlackList_Handler,
		},
		{
			MethodName: "EditBlackList",
			Handler:    _RiskServerService_EditBlackList_Handler,
		},
		{
			MethodName: "DeleteBlackList",
			Handler:    _RiskServerService_DeleteBlackList_Handler,
		},
		{
			MethodName: "AddWhiteList",
			Handler:    _RiskServerService_AddWhiteList_Handler,
		},
		{
			MethodName: "EditWhiteList",
			Handler:    _RiskServerService_EditWhiteList_Handler,
		},
		{
			MethodName: "DeleteWhiteList",
			Handler:    _RiskServerService_DeleteWhiteList_Handler,
		},
		{
			MethodName: "AddDarkList",
			Handler:    _RiskServerService_AddDarkList_Handler,
		},
		{
			MethodName: "EditDarkList",
			Handler:    _RiskServerService_EditDarkList_Handler,
		},
		{
			MethodName: "DeleteDarkList",
			Handler:    _RiskServerService_DeleteDarkList_Handler,
		},
		{
			MethodName: "AddConfigApp",
			Handler:    _RiskServerService_AddConfigApp_Handler,
		},
		{
			MethodName: "EditConfigApp",
			Handler:    _RiskServerService_EditConfigApp_Handler,
		},
		{
			MethodName: "DeleteConfigApp",
			Handler:    _RiskServerService_DeleteConfigApp_Handler,
		},
		{
			MethodName: "SDKSendComet",
			Handler:    _RiskServerService_SDKSendComet_Handler,
		},
		{
			MethodName: "ReloadGeetestConfig",
			Handler:    _RiskServerService_ReloadGeetestConfig_Handler,
		},
		{
			MethodName: "DeleteApproved",
			Handler:    _RiskServerService_DeleteApproved_Handler,
		},
		{
			MethodName: "DecodeData",
			Handler:    _RiskServerService_DecodeData_Handler,
		},
		{
			MethodName: "encodeData",
			Handler:    _RiskServerService_EncodeData_Handler,
		},
		{
			MethodName: "checkAppId",
			Handler:    _RiskServerService_CheckAppId_Handler,
		},
		{
			MethodName: "SDKDOIDCheck",
			Handler:    _RiskServerService_SDKDOIDCheck_Handler,
		},
		{
			MethodName: "ShumeiDecode",
			Handler:    _RiskServerService_ShumeiDecode_Handler,
		},
		{
			MethodName: "BindNotice",
			Handler:    _RiskServerService_BindNotice_Handler,
		},
		{
			MethodName: "SDKDOIDToDOID",
			Handler:    _RiskServerService_SDKDOIDToDOID_Handler,
		},
		{
			MethodName: "FaceStatus",
			Handler:    _RiskServerService_FaceStatus_Handler,
		},
		{
			MethodName: "FaceCodeForWeb",
			Handler:    _RiskServerService_FaceCodeForWeb_Handler,
		},
		{
			MethodName: "QrCodeGenerate",
			Handler:    _RiskServerService_QrCodeGenerate_Handler,
		},
		{
			MethodName: "QrCodeScan",
			Handler:    _RiskServerService_QrCodeScan_Handler,
		},
		{
			MethodName: "FaceStatusQueryByQrCode",
			Handler:    _RiskServerService_FaceStatusQueryByQrCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/server.proto",
}

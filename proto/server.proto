syntax = "proto3";

package papegames.sparrow.risk;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "openapiv3/annotations.proto";
import "papegames/type/empty.proto";
import "tagger/tagger.proto";
import "papegames/type/raw_message.proto";
import "proto/base.proto";

option go_package = "risk/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "RiskProto";
option java_package = "com.papegames.sparrow.risk";

// This API represents pay-risk service.
service RiskServerService {
  option (google.api.default_host) = "risk.papegames.com";


  //极验服务端接口
  rpc CaptchaTokenCheck(CaptchaTokenCheckRequest)returns(CaptchaTokenCheckResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "极验验证接口"
    };
    option (google.api.http) = {
      post: "/v1/risk/captcha/token/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 业务风控检测
  rpc AppCheckRisk(AppRiskCheckRequest)returns(HandlerRequest){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "业务风控检测"
    };
    option (google.api.http) = {
      post: "/v1/risk/app/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 账号风控检测
  rpc AccountCheckRisk(AccountRiskCheckRequest)returns(HandlerRequest){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "账号风控检测"
    };
    option (google.api.http) = {
      post: "/v1/risk/account/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 支付风控检测
  rpc PayCheckRisk(PayRiskCheckRequest)returns(HandlerRequest){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "支付风控检测"
    };
    option (google.api.http) = {
      post: "/v1/risk/payment/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 设备风险信息查询
  rpc DeviceRisk(DeviceRiskRequest)returns(DeviceRiskResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "设备风险信息查询"
    };
    option (google.api.http) = {
      post: "/v1/risk/device/risk"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑名单列表-添加
  rpc AddBlackList(AddBlackListRequest)returns(AddResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "加白名单"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/black_list/create"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑名单列表-修改
  rpc EditBlackList(EditBlackListRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑名单列表-修改"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/black_list/update"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑名单列表-删除
  rpc DeleteBlackList(ClientIdRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑名单列表-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/black_list/delete"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 白名单列表-添加
  rpc AddWhiteList(AddWhiteListRequest)returns(AddResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑名单列表-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/white_list/create"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 白名单列表-修改
  rpc EditWhiteList(EditWhiteListRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "白名单列表-修改"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/white_list/update"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 白名单列表-删除
  rpc DeleteWhiteList(ClientIdRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "白名单列表-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/white_list/delete"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑产列表-添加
  rpc AddDarkList(AddDarkListRequest)returns(AddResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑产列表-添加"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/dark_list/create"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑产列表-修改
  rpc EditDarkList(EditDarkListRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑产列表-修改"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/dark_list/update"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 黑产列表-删除
  rpc DeleteDarkList(ClientIdRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "黑产列表-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/dark_list/delete"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 风控开关配置-添加
  rpc AddConfigApp(AddConfigAppRequest)returns(AddResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "风控配置-添加"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/config_app/create"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 风控开关配置-修改
  rpc EditConfigApp(EditConfigAppRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "风控配置-修改"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/config_app/update"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 风控开关配置-删除
  rpc DeleteConfigApp(ClientIdRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "风控配置-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/config_app/delete"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKSendComet(SDKSendCometRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "发送长链消息"
    };
    option (google.api.http) = {
      post: "/v1/risk/comet/send"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc ReloadGeetestConfig(ReloadGeetestConfigRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "重新加载极验配置"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/geetest_config/reload"
      body: "*"
    };

    option (google.api.method_signature) = "form-data";
  }

  rpc DeleteApproved(DeleteApprovedRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "ToB端"}}
      ],
      operation_id: "允许账号/设备-删除"
    };
    option (google.api.http) = {
      post: "/v1/risk/tob/approved/delete"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }


  // 加密数据解密
  rpc DecodeData(DecodeDataRequest)returns(DecodeDataResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "加密数据解密"
    };
    option (google.api.http) = {
      post: "/v1/risk/decode"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // 数据加密
  rpc encodeData(DecodeDataRequest)returns(DecodeDataResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "加密数据解密"
    };
    option (google.api.http) = {
      post: "/v1/risk/encode"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  // appId 验证
  rpc checkAppId(CheckAppIdRequest)returns(CheckAppIdRequest){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "加密数据解密"
    };
    option (google.api.http) = {
      post: "/v1/risk/app_id/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKDOIDCheck(SDKDOIDCheckRequest)returns(SDKDOIDCheckResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "SDK DOID验证"
    };
    option (google.api.http) = {
      post: "/v1/risk/ds/check"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc ShumeiDecode(ShumeiDecodeRequest)returns(ShumeiDecodeResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "数美boxId解密"
    };
    option (google.api.http) = {
      post: "/v1/risk/shumei/decode"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc BindNotice(BindNoticeRequest)returns(papegames.type.Empty){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "绑定手机号通知"
    };
    option (google.api.http) = {
      post: "/v1/risk/bind/notice"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc SDKDOIDToDOID(SDKDOIDRequest)returns(SDKDOIDResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "SDK DOID转DOID"
    };
    option (google.api.http) = {
      post: "/v1/risk/device/sdk_doid"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc FaceStatus(FaceStatusRequest)returns(FaceStatusResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P2"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: ""
    };
    option (google.api.http) = {
      post: "/v1/risk/face/status"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc FaceCodeForWeb(FaceCodeForWebRequest)returns(FaceCodeForWebResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "SDK端"}}
      ],
      operation_id: "人脸验证初始化（Server端)"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/code/web"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }
  
  rpc QrCodeGenerate(QrCodeGenerateRequest)returns(QrCodeGenerateResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "二维码初始化（PC端)"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/qrcode/generate"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc QrCodeScan(QrCodeScanRequest)returns(QrCodeScanResponse){
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "二维码扫码"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/qrcode/scan"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }

  rpc FaceStatusQueryByQrCode(FaceStatusQueryByQrCodeRequest)returns(FaceStatusQueryByQrCodeResponse){ 
    option (openapi.v3.operation) = {
      specification_extension: [
        {name: "level", value: {yaml: "P0"}},
        {name: "x-apifox-folder", value: {yaml: "服务端"}}
      ],
      operation_id: "使用code查询人脸认证状态"
    };
    option (google.api.http) = {
      post: "/v1/risk/face/qrcode/status"
      body: "*"
    };
    option (google.api.method_signature) = "form-data";
  }
}

message ClientIdRequest{
  int64 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  int64 id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
}

message DeleteApprovedRequest{
  string  nid = 1;
  string  DOID = 2;
}

message AddBlackListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string app_id = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string uid = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 type = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  int64 expired_at = 5;
}
message EditBlackListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  int64 id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  int64 expired_at = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
}
message AddWhiteListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string app_id = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string uid = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 type = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  int64 expired_at = 5;
}
message EditWhiteListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  int64 id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  int64 expired_at = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
}
message AddDarkListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string app_id = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string uid = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 type = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  // int32 provider = 5 [
  //   (google.api.field_behavior) = REQUIRED
  // ];
  int32 score = 5 [
    (google.api.field_behavior) = REQUIRED
  ];
  int64 expired_at = 6;
}
message EditDarkListRequest{
  uint32 client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  int64 id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  int64 expired_at = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 score = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message AddConfigAppRequest{
  uint32 client_id = 1;
  string app_id = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  string frequency = 3 [
    (google.api.field_behavior) = REQUIRED
  ];
  int32 is_open = 4;
  int32 type = 5;
  string title = 6;
  int32 handle = 7;
}
message EditConfigAppRequest{
  int64 id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  string frequency = 3;
  int64 handle = 5;
  int32 is_open = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message RiskCheckResponse{
  uint32  has_pass = 1;
  string  device_id = 3;
  HandlerRequest captcha = 2;
}

message HandlerRequest{
  string nonce = 1 ;
  string provider = 2;
  repeated string phone = 3;
  repeated string sale_phone = 4;
  string email = 9;
  string sale_email = 10;
  int64 ttl = 11;
}
message Device {
  string fid = 1;
  string ishumei = 2;
  string aliyun = 3;
  string geetest = 4;
}

message AppRiskCheckRequest {
  //用户的nid
  string nid = 1  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  string ip = 2  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      pattern: "ipv4"
    }
  ]; //用户的 IP 地址
  string action = 3  [
    (google.api.field_behavior) = REQUIRED
  ];
  string app_id = 4  [
    (google.api.field_behavior) = REQUIRED
  ]; //应用的 app_id
  string device = 5; //用户的设备
  string DOID = 6; //DOID
  uint32 client_id = 7; //租户 ID
  string lang = 8; //语言
  string role_id = 9; //角色ID
  string scene = 10; //场景ID
}

message AccountRiskCheckRequest {
  string nid = 1  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  string ip = 2  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      pattern: "ipv4"
    }
  ];
  string action = 3  [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32 client_id = 4  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string device = 5;
  string DOID = 6;
  string app_id = 7;
  string lang = 8;
  string role_id = 9;
  string scene = 10;
}


message DeviceIds{
  string shuzilm = 1;
  string shumei = 2;
}


message CaptchaTokenCheckRequest{
  uint32  client_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string  pass_token = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message CaptchaTokenCheckResponse{
  bool has_pass = 1;
}

message DeviceRiskRequest{
  string DOID = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32 client_id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
}

message DeviceRiskResponse{
  uint32 score = 1;
}


message AddResponse{
  uint64 id = 1;
}


message PayRiskCheckRequest {
  string  nid = 1  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 1
  ];
  string  ip = 2 ;
  string  action = 3  [
    (google.api.field_behavior) = REQUIRED
  ];
  uint32  client_id = 4  [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property).minimum = 100
  ];
  string  device = 5;
  uint32  charge_type = 6;
  uint32  platform = 7;
  string  product_id = 8;
  string  role_id = 9;
  uint32  region = 10;
  uint32  zone_id = 11;
  string  lang = 12;
  string  app_id = 13;
  string  scene = 14;
}

message SDKSendCometRequest{
  string clientid = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  string nid = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
  uint64 code = 3;
  string doid = 4;
  string ip = 5;
  string account = 6;
}

message ReloadGeetestConfigRequest {}
message DecodeDataRequest {
  string  app_id = 1;
  int64  timestamp = 2;
  string  data = 3;
}

message DecodeDataResponse {
  papegames.type.RawMessage value = 1;
}

message CheckAppIdRequest {
  string  app_id = 1;
  string  app_key = 2;
  string  aes_key = 3;
  string  app_secret = 4;
}

message SDKDOIDCheckRequest{
  string DOID = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SDKDOIDCheckResponse{
  bool pass = 1;
  string device_id = 2;
}

message ShumeiDecodeRequest{
  string boxid = 1;
}
message ShumeiDecodeResponse{
  string value = 1;
  string boxid = 2;
}

message BindNoticeRequest {
  int64 nid = 1;
  int32 code = 2;
  uint32 client_id = 3;
  string app_id = 4;
  string doid = 5;
  string nonce = 6;
}

message SDKDOIDRequest{
  string sdk_DOID = 1;
}

message SDKDOIDResponse{
  option (tagger.disable_omitempty) = true;
  string DOID = 1;
}

message FaceStatusRequest{
  //用户nid
  string nid = 1;
  //场景
  string scene = 2;
  //人脸识别token
  string pass_token = 3;
  //租户 ID
  int64 client_id = 4 [
    (google.api.field_behavior) = REQUIRED
  ];
  string DOID = 5;
  string ip = 6;
  string id_card = 7;
  string real_name = 8;
}

message FaceStatusResponse{
  option (tagger.disable_omitempty) = true;
  uint32 status = 1;
}


message FaceCodeForWebRequest {
  //用户nid
  string nid = 1 ;
  //用户token
  string token = 2;
  //真实姓名
  string real_name = 3 ;
  //身份证
  string id_card = 4 ;

  //手机号码
  string phone = 11;
  //场景，固定值:account_real_info
  string scene = 5 [
    (google.api.field_behavior) = REQUIRED
  ];
  //厂商，固定值:aliyun
  string vendor = 6 [
    (google.api.field_behavior) = REQUIRED
  ];
  //扩展信息，json格式, meta_info必传，{"meta_info":{}}
  string extra = 7;
  //租户 ID
  int64 client_id = 8 [
    (google.api.field_behavior) = REQUIRED
  ];
  //DOID值
  string DOID = 9 [
    (google.api.field_behavior) = REQUIRED
  ];
  //认证结束后回跳页面的链接地址
  string return_url = 10 [
    (google.api.field_behavior) = REQUIRED
  ];

  //appId
  string app_id = 12 [
    (google.api.field_behavior) = REQUIRED
  ];
  //ip
  string ip = 13;
}

message FaceCodeForWebResponse {
  option (tagger.disable_omitempty) = true;
  //厂商
  string vendor = 1;
  //请求的唯一code
  string code = 2;
  //认证结束后回跳页面的链接地址
  string return_url = 3;
  //厂商的扩展数据
  FaceServiceData service = 4;
}

message QrCodeGenerateRequest {
  //租户 ID
  int64 client_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  //用户nid
  string nid = 2 [
    (google.api.field_behavior) = REQUIRED
  ] ;
  //用户token
  string token = 3 ;
  //真实姓名
  string real_name = 4 ;
  //身份证
  string id_card = 5 ;
}

message QrCodeGenerateResponse {
  option (tagger.disable_omitempty) = true;
  //二维码id
  string qrcode_id = 1;
  //过期时间
  string expired_at = 2;
  //今日剩余次数
  string remaining_attempts = 3;
}


message QrCodeScanRequest {
  //二维码id
  string qrcode_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
}

 message QrCodeScanResponse {
  option (tagger.disable_omitempty) = true;
  //用户id
  string uid = 1;
  //用户昵称
  string nid = 2 ;
  //用户token
  string token = 3;
  //真实姓名
  string real_name = 4 ;
  //身份证
  string id_card = 5 ;
}


message FaceStatusQueryByQrCodeRequest {
  //二维码id
  string qrcode_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message FaceStatusQueryByQrCodeResponse {
  option (tagger.disable_omitempty) = true;
  //人脸认证状态 0:未认证 1：认证通过  2: 认证不通过
  uint32 status = 1;
}
// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: proto/base.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaceServiceData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 认证id
	CertifyId     string `protobuf:"bytes,1,opt,name=certify_id,json=certifyId,proto3" json:"certify_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceServiceData) Reset() {
	*x = FaceServiceData{}
	mi := &file_proto_base_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceServiceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceServiceData) ProtoMessage() {}

func (x *FaceServiceData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_base_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceServiceData.ProtoReflect.Descriptor instead.
func (*FaceServiceData) Descriptor() ([]byte, []int) {
	return file_proto_base_proto_rawDescGZIP(), []int{0}
}

func (x *FaceServiceData) GetCertifyId() string {
	if x != nil {
		return x.CertifyId
	}
	return ""
}

type FaceResultRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 人脸识别初始化接口返回的code
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// 租户 ID
	ClientId int64 `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// DOID值
	DOID          string `protobuf:"bytes,5,opt,name=DOID,proto3" json:"DOID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceResultRequest) Reset() {
	*x = FaceResultRequest{}
	mi := &file_proto_base_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceResultRequest) ProtoMessage() {}

func (x *FaceResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_base_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceResultRequest.ProtoReflect.Descriptor instead.
func (*FaceResultRequest) Descriptor() ([]byte, []int) {
	return file_proto_base_proto_rawDescGZIP(), []int{1}
}

func (x *FaceResultRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FaceResultRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FaceResultRequest) GetDOID() string {
	if x != nil {
		return x.DOID
	}
	return ""
}

type FaceResultResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int32                  `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	PassToken     string                 `protobuf:"bytes,2,opt,name=pass_token,json=passToken,proto3" json:"pass_token"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaceResultResponse) Reset() {
	*x = FaceResultResponse{}
	mi := &file_proto_base_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceResultResponse) ProtoMessage() {}

func (x *FaceResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_base_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceResultResponse.ProtoReflect.Descriptor instead.
func (*FaceResultResponse) Descriptor() ([]byte, []int) {
	return file_proto_base_proto_rawDescGZIP(), []int{2}
}

func (x *FaceResultResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FaceResultResponse) GetPassToken() string {
	if x != nil {
		return x.PassToken
	}
	return ""
}

var File_proto_base_proto protoreflect.FileDescriptor

const file_proto_base_proto_rawDesc = "" +
	"\n" +
	"\x10proto/base.proto\x12\x16papegames.sparrow.risk\x1a\x13tagger/tagger.proto\x1a\x1fgoogle/api/field_behavior.proto\"0\n" +
	"\x0fFaceServiceData\x12\x1d\n" +
	"\n" +
	"certify_id\x18\x01 \x01(\tR\tcertifyId\"d\n" +
	"\x11FaceResultRequest\x12\x18\n" +
	"\x04code\x18\x03 \x01(\tB\x04\xe2A\x01\x02R\x04code\x12!\n" +
	"\tclient_id\x18\x04 \x01(\x03B\x04\xe2A\x01\x02R\bclientId\x12\x12\n" +
	"\x04DOID\x18\x05 \x01(\tR\x04DOID\"R\n" +
	"\x12FaceResultResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"pass_token\x18\x02 \x01(\tR\tpassToken:\x05ȧ\x86\a\x01B;\n" +
	"\x1acom.papegames.sparrow.riskB\tRiskProtoP\x01Z\x10risk/proto;protob\x06proto3"

var (
	file_proto_base_proto_rawDescOnce sync.Once
	file_proto_base_proto_rawDescData []byte
)

func file_proto_base_proto_rawDescGZIP() []byte {
	file_proto_base_proto_rawDescOnce.Do(func() {
		file_proto_base_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_base_proto_rawDesc), len(file_proto_base_proto_rawDesc)))
	})
	return file_proto_base_proto_rawDescData
}

var file_proto_base_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_base_proto_goTypes = []any{
	(*FaceServiceData)(nil),    // 0: papegames.sparrow.risk.FaceServiceData
	(*FaceResultRequest)(nil),  // 1: papegames.sparrow.risk.FaceResultRequest
	(*FaceResultResponse)(nil), // 2: papegames.sparrow.risk.FaceResultResponse
}
var file_proto_base_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_base_proto_init() }
func file_proto_base_proto_init() {
	if File_proto_base_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_base_proto_rawDesc), len(file_proto_base_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_base_proto_goTypes,
		DependencyIndexes: file_proto_base_proto_depIdxs,
		MessageInfos:      file_proto_base_proto_msgTypes,
	}.Build()
	File_proto_base_proto = out.File
	file_proto_base_proto_goTypes = nil
	file_proto_base_proto_depIdxs = nil
}

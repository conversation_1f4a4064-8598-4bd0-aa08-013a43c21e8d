// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.6
// protoc              v4.25.1
// source: proto/sdk.proto

package proto

func (x *SendCometRequest) Validate() error {
	if len(x.GetClientId()) == 0 {
		return SendCometRequestValidationError{
			field:   "ClientId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetNid()) == 0 {
		return SendCometRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *GCaptchaRequest) Validate() error {
	if len(x.<PERSON>ber()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "Number",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetCaptcha()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "Captcha",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.<PERSON>()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "Token",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetTime()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "Time",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetCaptchaId()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "CaptchaId",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetNonce()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "Nonce",
			reason:  "required",
			message: "value is required",
		}
	}
	if x.GetClientId() < 100 {
		return GCaptchaRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	if len(x.GetAppId()) == 0 {
		return GCaptchaRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *GCaptchaResponse) Validate() error {
	return nil
}

func (x *ShumeiDeviceGetRequest) Validate() error {
	return nil
}

func (x *ShumeiDeviceRequestData) Validate() error {
	return nil
}

func (x *ShumeiDeviceResponseData) Validate() error {
	if v, ok := interface{}(x.GetExtraInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiDeviceResponseDataValidationError{
				field:   "ExtraInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *DeviceGetRequest) Validate() error {
	if len(x.GetInformation()) == 0 {
		return DeviceGetRequestValidationError{
			field:   "Information",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetThirdDeviceId()) == 0 {
		return DeviceGetRequestValidationError{
			field:   "ThirdDeviceId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *ThirdDeviceIdData) Validate() error {
	return nil
}

func (x *DeviceInfo) Validate() error {
	return nil
}

func (x *DeviceGetResponse) Validate() error {
	return nil
}

func (x *DeviceGetResponseData) Validate() error {
	if v, ok := interface{}(x.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceGetResponseDataValidationError{
				field:   "Detail",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Detail) Validate() error {
	return nil
}

func (x *DeviceLogsRequest) Validate() error {
	return nil
}

func (x *BizInitRequest) Validate() error {
	if x.GetClientId() < 100 {
		return BizInitRequestValidationError{
			field:   "ClientId",
			reason:  "minimum",
			message: "value must be greater than or equal to 100",
		}
	}
	return nil
}

func (x *BizInitResponse) Validate() error {
	if v, ok := interface{}(x.GetCaptcha()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BizInitResponseValidationError{
				field:   "Captcha",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *BizInitCaptchaData) Validate() error {
	return nil
}

func (x *HealthRequest) Validate() error {
	return nil
}

func (x *ThirdDeviceData) Validate() error {
	return nil
}

func (x *ShumeiDeviceConfigRequest) Validate() error {
	return nil
}

func (x *ShumeiDeviceConfigResponse) Validate() error {
	return nil
}

func (x *SDKCheckPhoneRequest) Validate() error {
	if len(x.GetNonce()) == 0 {
		return SDKCheckPhoneRequestValidationError{
			field:   "Nonce",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetPhone()) == 0 {
		return SDKCheckPhoneRequestValidationError{
			field:   "Phone",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return SDKCheckPhoneRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SDKCheckPhoneResponse) Validate() error {
	return nil
}

func (x *SDKPhoneCodeRequest) Validate() error {
	if len(x.GetNonce()) == 0 {
		return SDKPhoneCodeRequestValidationError{
			field:   "Nonce",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return SDKPhoneCodeRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SDKCheckCodeRequest) Validate() error {
	if len(x.GetNonce()) == 0 {
		return SDKCheckCodeRequestValidationError{
			field:   "Nonce",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetCode()) == 0 {
		return SDKCheckCodeRequestValidationError{
			field:   "Code",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetAppId()) == 0 {
		return SDKCheckCodeRequestValidationError{
			field:   "AppId",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SDKCheckEmailRequest) Validate() error {
	if len(x.GetNonce()) == 0 {
		return SDKCheckEmailRequestValidationError{
			field:   "Nonce",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetEmail()) == 0 {
		return SDKCheckEmailRequestValidationError{
			field:   "Email",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SDKDeviceInfo) Validate() error {
	return nil
}

func (x *CreateDOID) Validate() error {
	if v, ok := interface{}(x.GetSDKDeviceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDOIDValidationError{
				field:   "SDKDeviceInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetThirdDeviceIdData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDOIDValidationError{
				field:   "ThirdDeviceIdData",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *CreateSdKDOID) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse) Validate() error {
	if v, ok := interface{}(x.GetDeviceLabels()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponseValidationError{
				field:   "DeviceLabels",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_MachineAccountRisk) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_UgcAccountRisk) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_SceneAccountRisk) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_AccountActiveInfo) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_AccountFreqInfo) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_AccountRelateInfo) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_AccountCommonInfo) Validate() error {
	for _, item := range x.GetSTokenidRelateSmidInfoMap_4W() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiRiskResponse_AccountCommonInfoValidationError{
					field:   "STokenidRelateSmidInfoMap_4W",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	for _, item := range x.GetSTokenidRelateIpCityInfoMap_4W() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiRiskResponse_AccountCommonInfoValidationError{
					field:   "STokenidRelateIpCityInfoMap_4W",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenlabels) Validate() error {
	if v, ok := interface{}(x.GetMachineAccountRisk()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "MachineAccountRisk",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetUGCAccountRisk()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "UGCAccountRisk",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetSceneAccountRisk()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "SceneAccountRisk",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAccountActiveInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "AccountActiveInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAccountFreqInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "AccountFreqInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAccountRelateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "AccountRelateInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAccountCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenlabelsValidationError{
				field:   "AccountCommonInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Other) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_FakeDevice) Validate() error {
	if v, ok := interface{}(x.GetOther()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_FakeDeviceValidationError{
				field:   "Other",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_DeviceSuspiciousLabels) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_DeviceActiveInfo) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_Common) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_MonkeyGame) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_MonkeyRead) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_MonkeyDevice) Validate() error {
	if v, ok := interface{}(x.GetCommon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_MonkeyDeviceValidationError{
				field:   "Common",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMonkeyGame()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_MonkeyDeviceValidationError{
				field:   "MonkeyGame",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMonkeyRead()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_MonkeyDeviceValidationError{
				field:   "MonkeyRead",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Devicelabels) Validate() error {
	if v, ok := interface{}(x.GetFakeDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_DevicelabelsValidationError{
				field:   "FakeDevice",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetDeviceSuspiciousLabels()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_DevicelabelsValidationError{
				field:   "DeviceSuspiciousLabels",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetDeviceActiveInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_DevicelabelsValidationError{
				field:   "DeviceActiveInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMonkeyDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_DevicelabelsValidationError{
				field:   "MonkeyDevice",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Detail) Validate() error {
	return nil
}

func (x *ShumeiRiskResponse_Tokenprofilelabels) Validate() error {
	if v, ok := interface{}(x.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenprofilelabelsValidationError{
				field:   "Detail",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Tokenrisklabels) Validate() error {
	if v, ok := interface{}(x.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_TokenrisklabelsValidationError{
				field:   "Detail",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ShumeiRiskResponse_Devicerisklabels) Validate() error {
	if v, ok := interface{}(x.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiRiskResponse_DevicerisklabelsValidationError{
				field:   "Detail",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *NIDCheckRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return NIDCheckRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSig()) == 0 {
		return NIDCheckRequestValidationError{
			field:   "Sig",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *NIDCheckResponse) Validate() error {
	return nil
}

func (x *FaceCodeRequest) Validate() error {
	if len(x.GetNid()) == 0 {
		return FaceCodeRequestValidationError{
			field:   "Nid",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetToken()) == 0 {
		return FaceCodeRequestValidationError{
			field:   "Token",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetScene()) == 0 {
		return FaceCodeRequestValidationError{
			field:   "Scene",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetVendor()) == 0 {
		return FaceCodeRequestValidationError{
			field:   "Vendor",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetDOID()) == 0 {
		return FaceCodeRequestValidationError{
			field:   "DOID",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *FaceCodeResponse) Validate() error {
	if v, ok := interface{}(x.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FaceCodeResponseValidationError{
				field:   "Service",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *FaceCodeVal) Validate() error {
	return nil
}

func (x *FaceRdbTokeVal) Validate() error {
	return nil
}

type SendCometRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SendCometRequestValidationError) Field() string { return e.field }

func (e SendCometRequestValidationError) Reason() string { return e.reason }

func (e SendCometRequestValidationError) Message() string { return e.message }

func (e SendCometRequestValidationError) Cause() error { return e.cause }

func (e SendCometRequestValidationError) ErrorName() string { return "SendCometRequestValidationError" }

func (e SendCometRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SendCometRequest." + e.field + ": " + e.message + cause
}

type GCaptchaRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GCaptchaRequestValidationError) Field() string { return e.field }

func (e GCaptchaRequestValidationError) Reason() string { return e.reason }

func (e GCaptchaRequestValidationError) Message() string { return e.message }

func (e GCaptchaRequestValidationError) Cause() error { return e.cause }

func (e GCaptchaRequestValidationError) ErrorName() string { return "GCaptchaRequestValidationError" }

func (e GCaptchaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GCaptchaRequest." + e.field + ": " + e.message + cause
}

type GCaptchaResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GCaptchaResponseValidationError) Field() string { return e.field }

func (e GCaptchaResponseValidationError) Reason() string { return e.reason }

func (e GCaptchaResponseValidationError) Message() string { return e.message }

func (e GCaptchaResponseValidationError) Cause() error { return e.cause }

func (e GCaptchaResponseValidationError) ErrorName() string { return "GCaptchaResponseValidationError" }

func (e GCaptchaResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GCaptchaResponse." + e.field + ": " + e.message + cause
}

type ShumeiDeviceGetRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDeviceGetRequestValidationError) Field() string { return e.field }

func (e ShumeiDeviceGetRequestValidationError) Reason() string { return e.reason }

func (e ShumeiDeviceGetRequestValidationError) Message() string { return e.message }

func (e ShumeiDeviceGetRequestValidationError) Cause() error { return e.cause }

func (e ShumeiDeviceGetRequestValidationError) ErrorName() string {
	return "ShumeiDeviceGetRequestValidationError"
}

func (e ShumeiDeviceGetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDeviceGetRequest." + e.field + ": " + e.message + cause
}

type ShumeiDeviceRequestDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDeviceRequestDataValidationError) Field() string { return e.field }

func (e ShumeiDeviceRequestDataValidationError) Reason() string { return e.reason }

func (e ShumeiDeviceRequestDataValidationError) Message() string { return e.message }

func (e ShumeiDeviceRequestDataValidationError) Cause() error { return e.cause }

func (e ShumeiDeviceRequestDataValidationError) ErrorName() string {
	return "ShumeiDeviceRequestDataValidationError"
}

func (e ShumeiDeviceRequestDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDeviceRequestData." + e.field + ": " + e.message + cause
}

type ShumeiDeviceResponseDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDeviceResponseDataValidationError) Field() string { return e.field }

func (e ShumeiDeviceResponseDataValidationError) Reason() string { return e.reason }

func (e ShumeiDeviceResponseDataValidationError) Message() string { return e.message }

func (e ShumeiDeviceResponseDataValidationError) Cause() error { return e.cause }

func (e ShumeiDeviceResponseDataValidationError) ErrorName() string {
	return "ShumeiDeviceResponseDataValidationError"
}

func (e ShumeiDeviceResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDeviceResponseData." + e.field + ": " + e.message + cause
}

type DeviceGetRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceGetRequestValidationError) Field() string { return e.field }

func (e DeviceGetRequestValidationError) Reason() string { return e.reason }

func (e DeviceGetRequestValidationError) Message() string { return e.message }

func (e DeviceGetRequestValidationError) Cause() error { return e.cause }

func (e DeviceGetRequestValidationError) ErrorName() string { return "DeviceGetRequestValidationError" }

func (e DeviceGetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceGetRequest." + e.field + ": " + e.message + cause
}

type ThirdDeviceIdDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ThirdDeviceIdDataValidationError) Field() string { return e.field }

func (e ThirdDeviceIdDataValidationError) Reason() string { return e.reason }

func (e ThirdDeviceIdDataValidationError) Message() string { return e.message }

func (e ThirdDeviceIdDataValidationError) Cause() error { return e.cause }

func (e ThirdDeviceIdDataValidationError) ErrorName() string {
	return "ThirdDeviceIdDataValidationError"
}

func (e ThirdDeviceIdDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ThirdDeviceIdData." + e.field + ": " + e.message + cause
}

type DeviceInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceInfoValidationError) Field() string { return e.field }

func (e DeviceInfoValidationError) Reason() string { return e.reason }

func (e DeviceInfoValidationError) Message() string { return e.message }

func (e DeviceInfoValidationError) Cause() error { return e.cause }

func (e DeviceInfoValidationError) ErrorName() string { return "DeviceInfoValidationError" }

func (e DeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceInfo." + e.field + ": " + e.message + cause
}

type DeviceGetResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceGetResponseValidationError) Field() string { return e.field }

func (e DeviceGetResponseValidationError) Reason() string { return e.reason }

func (e DeviceGetResponseValidationError) Message() string { return e.message }

func (e DeviceGetResponseValidationError) Cause() error { return e.cause }

func (e DeviceGetResponseValidationError) ErrorName() string {
	return "DeviceGetResponseValidationError"
}

func (e DeviceGetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceGetResponse." + e.field + ": " + e.message + cause
}

type DeviceGetResponseDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceGetResponseDataValidationError) Field() string { return e.field }

func (e DeviceGetResponseDataValidationError) Reason() string { return e.reason }

func (e DeviceGetResponseDataValidationError) Message() string { return e.message }

func (e DeviceGetResponseDataValidationError) Cause() error { return e.cause }

func (e DeviceGetResponseDataValidationError) ErrorName() string {
	return "DeviceGetResponseDataValidationError"
}

func (e DeviceGetResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceGetResponseData." + e.field + ": " + e.message + cause
}

type DetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DetailValidationError) Field() string { return e.field }

func (e DetailValidationError) Reason() string { return e.reason }

func (e DetailValidationError) Message() string { return e.message }

func (e DetailValidationError) Cause() error { return e.cause }

func (e DetailValidationError) ErrorName() string { return "DetailValidationError" }

func (e DetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Detail." + e.field + ": " + e.message + cause
}

type DeviceLogsRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeviceLogsRequestValidationError) Field() string { return e.field }

func (e DeviceLogsRequestValidationError) Reason() string { return e.reason }

func (e DeviceLogsRequestValidationError) Message() string { return e.message }

func (e DeviceLogsRequestValidationError) Cause() error { return e.cause }

func (e DeviceLogsRequestValidationError) ErrorName() string {
	return "DeviceLogsRequestValidationError"
}

func (e DeviceLogsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DeviceLogsRequest." + e.field + ": " + e.message + cause
}

type BizInitRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BizInitRequestValidationError) Field() string { return e.field }

func (e BizInitRequestValidationError) Reason() string { return e.reason }

func (e BizInitRequestValidationError) Message() string { return e.message }

func (e BizInitRequestValidationError) Cause() error { return e.cause }

func (e BizInitRequestValidationError) ErrorName() string { return "BizInitRequestValidationError" }

func (e BizInitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BizInitRequest." + e.field + ": " + e.message + cause
}

type BizInitResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BizInitResponseValidationError) Field() string { return e.field }

func (e BizInitResponseValidationError) Reason() string { return e.reason }

func (e BizInitResponseValidationError) Message() string { return e.message }

func (e BizInitResponseValidationError) Cause() error { return e.cause }

func (e BizInitResponseValidationError) ErrorName() string { return "BizInitResponseValidationError" }

func (e BizInitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BizInitResponse." + e.field + ": " + e.message + cause
}

type BizInitCaptchaDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BizInitCaptchaDataValidationError) Field() string { return e.field }

func (e BizInitCaptchaDataValidationError) Reason() string { return e.reason }

func (e BizInitCaptchaDataValidationError) Message() string { return e.message }

func (e BizInitCaptchaDataValidationError) Cause() error { return e.cause }

func (e BizInitCaptchaDataValidationError) ErrorName() string {
	return "BizInitCaptchaDataValidationError"
}

func (e BizInitCaptchaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BizInitCaptchaData." + e.field + ": " + e.message + cause
}

type HealthRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e HealthRequestValidationError) Field() string { return e.field }

func (e HealthRequestValidationError) Reason() string { return e.reason }

func (e HealthRequestValidationError) Message() string { return e.message }

func (e HealthRequestValidationError) Cause() error { return e.cause }

func (e HealthRequestValidationError) ErrorName() string { return "HealthRequestValidationError" }

func (e HealthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid HealthRequest." + e.field + ": " + e.message + cause
}

type ThirdDeviceDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ThirdDeviceDataValidationError) Field() string { return e.field }

func (e ThirdDeviceDataValidationError) Reason() string { return e.reason }

func (e ThirdDeviceDataValidationError) Message() string { return e.message }

func (e ThirdDeviceDataValidationError) Cause() error { return e.cause }

func (e ThirdDeviceDataValidationError) ErrorName() string { return "ThirdDeviceDataValidationError" }

func (e ThirdDeviceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ThirdDeviceData." + e.field + ": " + e.message + cause
}

type ShumeiDeviceConfigRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDeviceConfigRequestValidationError) Field() string { return e.field }

func (e ShumeiDeviceConfigRequestValidationError) Reason() string { return e.reason }

func (e ShumeiDeviceConfigRequestValidationError) Message() string { return e.message }

func (e ShumeiDeviceConfigRequestValidationError) Cause() error { return e.cause }

func (e ShumeiDeviceConfigRequestValidationError) ErrorName() string {
	return "ShumeiDeviceConfigRequestValidationError"
}

func (e ShumeiDeviceConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDeviceConfigRequest." + e.field + ": " + e.message + cause
}

type ShumeiDeviceConfigResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiDeviceConfigResponseValidationError) Field() string { return e.field }

func (e ShumeiDeviceConfigResponseValidationError) Reason() string { return e.reason }

func (e ShumeiDeviceConfigResponseValidationError) Message() string { return e.message }

func (e ShumeiDeviceConfigResponseValidationError) Cause() error { return e.cause }

func (e ShumeiDeviceConfigResponseValidationError) ErrorName() string {
	return "ShumeiDeviceConfigResponseValidationError"
}

func (e ShumeiDeviceConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiDeviceConfigResponse." + e.field + ": " + e.message + cause
}

type SDKCheckPhoneRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKCheckPhoneRequestValidationError) Field() string { return e.field }

func (e SDKCheckPhoneRequestValidationError) Reason() string { return e.reason }

func (e SDKCheckPhoneRequestValidationError) Message() string { return e.message }

func (e SDKCheckPhoneRequestValidationError) Cause() error { return e.cause }

func (e SDKCheckPhoneRequestValidationError) ErrorName() string {
	return "SDKCheckPhoneRequestValidationError"
}

func (e SDKCheckPhoneRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKCheckPhoneRequest." + e.field + ": " + e.message + cause
}

type SDKCheckPhoneResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKCheckPhoneResponseValidationError) Field() string { return e.field }

func (e SDKCheckPhoneResponseValidationError) Reason() string { return e.reason }

func (e SDKCheckPhoneResponseValidationError) Message() string { return e.message }

func (e SDKCheckPhoneResponseValidationError) Cause() error { return e.cause }

func (e SDKCheckPhoneResponseValidationError) ErrorName() string {
	return "SDKCheckPhoneResponseValidationError"
}

func (e SDKCheckPhoneResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKCheckPhoneResponse." + e.field + ": " + e.message + cause
}

type SDKPhoneCodeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKPhoneCodeRequestValidationError) Field() string { return e.field }

func (e SDKPhoneCodeRequestValidationError) Reason() string { return e.reason }

func (e SDKPhoneCodeRequestValidationError) Message() string { return e.message }

func (e SDKPhoneCodeRequestValidationError) Cause() error { return e.cause }

func (e SDKPhoneCodeRequestValidationError) ErrorName() string {
	return "SDKPhoneCodeRequestValidationError"
}

func (e SDKPhoneCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKPhoneCodeRequest." + e.field + ": " + e.message + cause
}

type SDKCheckCodeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKCheckCodeRequestValidationError) Field() string { return e.field }

func (e SDKCheckCodeRequestValidationError) Reason() string { return e.reason }

func (e SDKCheckCodeRequestValidationError) Message() string { return e.message }

func (e SDKCheckCodeRequestValidationError) Cause() error { return e.cause }

func (e SDKCheckCodeRequestValidationError) ErrorName() string {
	return "SDKCheckCodeRequestValidationError"
}

func (e SDKCheckCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKCheckCodeRequest." + e.field + ": " + e.message + cause
}

type SDKCheckEmailRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKCheckEmailRequestValidationError) Field() string { return e.field }

func (e SDKCheckEmailRequestValidationError) Reason() string { return e.reason }

func (e SDKCheckEmailRequestValidationError) Message() string { return e.message }

func (e SDKCheckEmailRequestValidationError) Cause() error { return e.cause }

func (e SDKCheckEmailRequestValidationError) ErrorName() string {
	return "SDKCheckEmailRequestValidationError"
}

func (e SDKCheckEmailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKCheckEmailRequest." + e.field + ": " + e.message + cause
}

type SDKDeviceInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SDKDeviceInfoValidationError) Field() string { return e.field }

func (e SDKDeviceInfoValidationError) Reason() string { return e.reason }

func (e SDKDeviceInfoValidationError) Message() string { return e.message }

func (e SDKDeviceInfoValidationError) Cause() error { return e.cause }

func (e SDKDeviceInfoValidationError) ErrorName() string { return "SDKDeviceInfoValidationError" }

func (e SDKDeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SDKDeviceInfo." + e.field + ": " + e.message + cause
}

type CreateDOIDValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CreateDOIDValidationError) Field() string { return e.field }

func (e CreateDOIDValidationError) Reason() string { return e.reason }

func (e CreateDOIDValidationError) Message() string { return e.message }

func (e CreateDOIDValidationError) Cause() error { return e.cause }

func (e CreateDOIDValidationError) ErrorName() string { return "CreateDOIDValidationError" }

func (e CreateDOIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CreateDOID." + e.field + ": " + e.message + cause
}

type CreateSdKDOIDValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CreateSdKDOIDValidationError) Field() string { return e.field }

func (e CreateSdKDOIDValidationError) Reason() string { return e.reason }

func (e CreateSdKDOIDValidationError) Message() string { return e.message }

func (e CreateSdKDOIDValidationError) Cause() error { return e.cause }

func (e CreateSdKDOIDValidationError) ErrorName() string { return "CreateSdKDOIDValidationError" }

func (e CreateSdKDOIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CreateSdKDOID." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponseValidationError) Field() string { return e.field }

func (e ShumeiRiskResponseValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponseValidationError) Message() string { return e.message }

func (e ShumeiRiskResponseValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponseValidationError) ErrorName() string {
	return "ShumeiRiskResponseValidationError"
}

func (e ShumeiRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_MachineAccountRiskValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) ErrorName() string {
	return "ShumeiRiskResponse_MachineAccountRiskValidationError"
}

func (e ShumeiRiskResponse_MachineAccountRiskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_MachineAccountRisk." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_UgcAccountRiskValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) ErrorName() string {
	return "ShumeiRiskResponse_UgcAccountRiskValidationError"
}

func (e ShumeiRiskResponse_UgcAccountRiskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_UgcAccountRisk." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_SceneAccountRiskValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) ErrorName() string {
	return "ShumeiRiskResponse_SceneAccountRiskValidationError"
}

func (e ShumeiRiskResponse_SceneAccountRiskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_SceneAccountRisk." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_AccountActiveInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) ErrorName() string {
	return "ShumeiRiskResponse_AccountActiveInfoValidationError"
}

func (e ShumeiRiskResponse_AccountActiveInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_AccountActiveInfo." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_AccountFreqInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) ErrorName() string {
	return "ShumeiRiskResponse_AccountFreqInfoValidationError"
}

func (e ShumeiRiskResponse_AccountFreqInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_AccountFreqInfo." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_AccountRelateInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) ErrorName() string {
	return "ShumeiRiskResponse_AccountRelateInfoValidationError"
}

func (e ShumeiRiskResponse_AccountRelateInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_AccountRelateInfo." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) Field() string {
	return e.field
}

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) Reason() string {
	return e.reason
}

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) Message() string {
	return e.message
}

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) ErrorName() string {
	return "ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError"
}

func (e ShumeiRiskResponse_STokenidRelateSmidInfoMap_4WValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_STokenidRelateSmidInfoMap_4W." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) Field() string {
	return e.field
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) Reason() string {
	return e.reason
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) Message() string {
	return e.message
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) Cause() error {
	return e.cause
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) ErrorName() string {
	return "ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError"
}

func (e ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4WValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_STokenidRelateIpCityInfoMap_4W." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_AccountCommonInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) ErrorName() string {
	return "ShumeiRiskResponse_AccountCommonInfoValidationError"
}

func (e ShumeiRiskResponse_AccountCommonInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_AccountCommonInfo." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_TokenlabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_TokenlabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_TokenlabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_TokenlabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_TokenlabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_TokenlabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_TokenlabelsValidationError"
}

func (e ShumeiRiskResponse_TokenlabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Tokenlabels." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_OtherValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_OtherValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_OtherValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_OtherValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_OtherValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_OtherValidationError) ErrorName() string {
	return "ShumeiRiskResponse_OtherValidationError"
}

func (e ShumeiRiskResponse_OtherValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Other." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_FakeDeviceValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_FakeDeviceValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_FakeDeviceValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_FakeDeviceValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_FakeDeviceValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_FakeDeviceValidationError) ErrorName() string {
	return "ShumeiRiskResponse_FakeDeviceValidationError"
}

func (e ShumeiRiskResponse_FakeDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_FakeDevice." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError"
}

func (e ShumeiRiskResponse_DeviceSuspiciousLabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_DeviceSuspiciousLabels." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_DeviceActiveInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) ErrorName() string {
	return "ShumeiRiskResponse_DeviceActiveInfoValidationError"
}

func (e ShumeiRiskResponse_DeviceActiveInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_DeviceActiveInfo." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_CommonValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_CommonValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_CommonValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_CommonValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_CommonValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_CommonValidationError) ErrorName() string {
	return "ShumeiRiskResponse_CommonValidationError"
}

func (e ShumeiRiskResponse_CommonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Common." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_MonkeyGameValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_MonkeyGameValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_MonkeyGameValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_MonkeyGameValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_MonkeyGameValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_MonkeyGameValidationError) ErrorName() string {
	return "ShumeiRiskResponse_MonkeyGameValidationError"
}

func (e ShumeiRiskResponse_MonkeyGameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_MonkeyGame." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_MonkeyReadValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_MonkeyReadValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_MonkeyReadValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_MonkeyReadValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_MonkeyReadValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_MonkeyReadValidationError) ErrorName() string {
	return "ShumeiRiskResponse_MonkeyReadValidationError"
}

func (e ShumeiRiskResponse_MonkeyReadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_MonkeyRead." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_MonkeyDeviceValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) ErrorName() string {
	return "ShumeiRiskResponse_MonkeyDeviceValidationError"
}

func (e ShumeiRiskResponse_MonkeyDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_MonkeyDevice." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_DevicelabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_DevicelabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_DevicelabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_DevicelabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_DevicelabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_DevicelabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_DevicelabelsValidationError"
}

func (e ShumeiRiskResponse_DevicelabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Devicelabels." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_DetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_DetailValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_DetailValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_DetailValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_DetailValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_DetailValidationError) ErrorName() string {
	return "ShumeiRiskResponse_DetailValidationError"
}

func (e ShumeiRiskResponse_DetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Detail." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_TokenprofilelabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_TokenprofilelabelsValidationError"
}

func (e ShumeiRiskResponse_TokenprofilelabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Tokenprofilelabels." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_TokenrisklabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_TokenrisklabelsValidationError"
}

func (e ShumeiRiskResponse_TokenrisklabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Tokenrisklabels." + e.field + ": " + e.message + cause
}

type ShumeiRiskResponse_DevicerisklabelsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) Field() string { return e.field }

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) Reason() string { return e.reason }

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) Message() string { return e.message }

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) Cause() error { return e.cause }

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) ErrorName() string {
	return "ShumeiRiskResponse_DevicerisklabelsValidationError"
}

func (e ShumeiRiskResponse_DevicerisklabelsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShumeiRiskResponse_Devicerisklabels." + e.field + ": " + e.message + cause
}

type NIDCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e NIDCheckRequestValidationError) Field() string { return e.field }

func (e NIDCheckRequestValidationError) Reason() string { return e.reason }

func (e NIDCheckRequestValidationError) Message() string { return e.message }

func (e NIDCheckRequestValidationError) Cause() error { return e.cause }

func (e NIDCheckRequestValidationError) ErrorName() string { return "NIDCheckRequestValidationError" }

func (e NIDCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid NIDCheckRequest." + e.field + ": " + e.message + cause
}

type NIDCheckResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e NIDCheckResponseValidationError) Field() string { return e.field }

func (e NIDCheckResponseValidationError) Reason() string { return e.reason }

func (e NIDCheckResponseValidationError) Message() string { return e.message }

func (e NIDCheckResponseValidationError) Cause() error { return e.cause }

func (e NIDCheckResponseValidationError) ErrorName() string { return "NIDCheckResponseValidationError" }

func (e NIDCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid NIDCheckResponse." + e.field + ": " + e.message + cause
}

type FaceCodeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceCodeRequestValidationError) Field() string { return e.field }

func (e FaceCodeRequestValidationError) Reason() string { return e.reason }

func (e FaceCodeRequestValidationError) Message() string { return e.message }

func (e FaceCodeRequestValidationError) Cause() error { return e.cause }

func (e FaceCodeRequestValidationError) ErrorName() string { return "FaceCodeRequestValidationError" }

func (e FaceCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceCodeRequest." + e.field + ": " + e.message + cause
}

type FaceCodeResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceCodeResponseValidationError) Field() string { return e.field }

func (e FaceCodeResponseValidationError) Reason() string { return e.reason }

func (e FaceCodeResponseValidationError) Message() string { return e.message }

func (e FaceCodeResponseValidationError) Cause() error { return e.cause }

func (e FaceCodeResponseValidationError) ErrorName() string { return "FaceCodeResponseValidationError" }

func (e FaceCodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceCodeResponse." + e.field + ": " + e.message + cause
}

type FaceCodeValValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceCodeValValidationError) Field() string { return e.field }

func (e FaceCodeValValidationError) Reason() string { return e.reason }

func (e FaceCodeValValidationError) Message() string { return e.message }

func (e FaceCodeValValidationError) Cause() error { return e.cause }

func (e FaceCodeValValidationError) ErrorName() string { return "FaceCodeValValidationError" }

func (e FaceCodeValValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceCodeVal." + e.field + ": " + e.message + cause
}

type FaceRdbTokeValValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FaceRdbTokeValValidationError) Field() string { return e.field }

func (e FaceRdbTokeValValidationError) Reason() string { return e.reason }

func (e FaceRdbTokeValValidationError) Message() string { return e.message }

func (e FaceRdbTokeValValidationError) Cause() error { return e.cause }

func (e FaceRdbTokeValValidationError) ErrorName() string { return "FaceRdbTokeValValidationError" }

func (e FaceRdbTokeValValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FaceRdbTokeVal." + e.field + ": " + e.message + cause
}

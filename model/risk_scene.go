package model

import (
	"context"
	"risk/startup"
)

type RiskScene struct {
	Id       uint64 `json:"id" gorm:"primaryKey"`
	ClientId uint64 `json:"client_id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	Status   int    `json:"-"`
	ModelId  string `json:"model_id"`
	IsDel    int    `json:"-"`
	Switch   string `json:"switch"`
}

func GetRiskScenes(ctx context.Context) ([]*RiskScene, error) {
	db := startup.GetMySql()
	var models []*RiskScene
	err := db.Table("risk_scene").
		Select("id", "client_id", "name", "code", "model_id", "switch").
		Where("status = ?", 1).
		Where("is_del = ?", 0).
		Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

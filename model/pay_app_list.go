package model

import (
	"context"
	"encoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"time"
)

const (
	TableNamePayAppList = "pay_app_list"
)

type PayAppList struct {
	ID        int64     `gorm:"column:id;primaryKey" json:"id,omitempty"`      // 主键
	AppID     string    `gorm:"column:app_id" json:"app_id,omitempty"`         // 应用ID
	AppKey    string    `gorm:"column:app_key" json:"app_key,omitempty"`       // sign签名密钥
	AppName   string    `gorm:"column:app_name" json:"app_name,omitempty"`     // 应用名称
	AppOwner  string    `gorm:"column:app_owner" json:"app_owner,omitempty"`   // 负责人
	AesKey    string    `gorm:"column:aes_key" json:"aes_key,omitempty"`       // AES秘钥
	IsDelete  int32     `gorm:"column:is_delete" json:"is_delete,omitempty"`   // 是否删除
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at,omitempty"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at,omitempty"` // 更新时间
	DeletedAt time.Time `gorm:"column:deleted_at" json:"deleted_at,omitempty"` // 删除时间
}

func NewPayAppList() *PayAppList {
	return &PayAppList{}
}

func (p *PayAppList) String() string {
	bytes, err := json.Marshal(p)
	if err != nil {
		return ""
	}
	return string(bytes)
}

func (p *PayAppList) TableName() string {
	return TableNamePayAppList
}

func (p *PayAppList) List(ctx context.Context, db *xgorm.DB) ([]*PayAppList, error) {
	var results []*PayAppList
	err := db.WithContext(ctx).Table(p.TableName()).Where("is_delete=0").Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

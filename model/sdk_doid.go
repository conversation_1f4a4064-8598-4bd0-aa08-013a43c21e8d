package model

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"time"
)

type RiskSdkDoid struct {
	ID        uint      `json:"id" gorm:"column:id"`
	SdkDOID   string    `json:"sdk_DOID" gorm:"column:sdk_DOID"` // SDK生成DOID
	DOID      string    `json:"DOID" gorm:"column:DOID"`         // 服务端DOID
	IsDeleted int8      `json:"is_deleted" gorm:"column:is_deleted"`
	DeletedAt time.Time `json:"deleted_at" gorm:"column:deleted_at"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

func (m *RiskSdkDoid) TableName() string {
	return "risk_sdk_doid"
}

func (m *RiskSdkDoid) Add(sdkDOID, DOID string, db *xgorm.DB) error {
	if sdkDOID == "" || DOID == "" {
		return nil
	}
	return db.Model(&RiskSdkDoid{}).Create(&RiskSdkDoid{
		SdkDOID:   sdkDOID,
		DOID:      DOID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}).Error
}
func (i *RiskSdkDoid) GetBySDKDOID(sdkDOID string, db *xgorm.DB) (*RiskSdkDoid, error) {
	var info RiskSdkDoid
	err := db.Model(&RiskSdkDoid{}).Where("sdk_doid = ?", sdkDOID).First(&info).Error
	if err != nil {
		return nil, err
	}
	return &info, nil
}

package model

import (
	"context"
	"risk/startup"
	"time"
)

// 极验配置
type GeetestConfigModel struct {
	ID         int64     `json:"id" gorm:"column:id;primaryKey;autoIncrement;comment:主键"`
	AppID      string    `json:"app_id" gorm:"column:app_id;type:bigint;not null;comment:应用ID"`
	IsClose    bool      `json:"is_close" gorm:"column:is_close;type:tinyint;not null;comment:是否关闭"`
	CaptchaID  string    `json:"captcha_id" gorm:"column:captcha_id;type:bigint;not null;comment:验证码ID"`
	CaptchaKey string    `json:"captcha_key" gorm:"column:captcha_key;type:varchar(255);not null;comment:验证码key"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at;type:bigint;not null;comment:创建时间"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at;type:bigint;not null;comment:更新时间"`
}

func (m *GeetestConfigModel) TableName() string {
	return "geetest_config"
}

func GeetestConfigList(ctx context.Context) ([]*GeetestConfigModel, error) {
	var models []*GeetestConfigModel
	err := startup.GetMySql().WithContext(ctx).Model(&GeetestConfigModel{}).
		Find(&models).Error

	return models, err
}

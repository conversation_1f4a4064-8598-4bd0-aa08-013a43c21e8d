package model

import (
	"errors"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gorm.io/gorm"
	"time"
)

type RiskProviderDarkList struct {
	Id        uint64    `gorm:"column:id;type:BIGINT UNSIGNED;AUTO_INCREMENT;NOT NULL"`
	AppId     string    `json:"app_id" gorm:"column:app_id"` // app_id
	ClientId  uint64    `gorm:"column:client_id;type:BIGINT UNSIGNED;NOT NULL"`
	Uid       string    `gorm:"column:uid;type:VARCHAR(100);NOT NULL"`
	Type      uint8     `gorm:"column:type;type:TINYINT UNSIGNED;NOT NULL"`
	Provider  uint8     `gorm:"column:provider;type:TINYINT;NOT NULL"`
	Score     uint8     `gorm:"column:score;type:TINYINT;NOT NULL"`
	ExpiredAt int64     `gorm:"column:expired_at;type:TINYINT;NOT NULL"`
	IsDelete  uint8     `gorm:"column:is_delete;type:TINYINT UNSIGNED;NOT NULL"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;NOT NULL"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:TIMESTAMP;NOT NULL"`
	DeletedAt uint64    `gorm:"column:deleted_at;type:BIGINT UNSIGNED;NOT NULL"`
}

func (m *RiskProviderDarkList) TableName() string {
	return "risk_provider_dark_list"
}

func (m *RiskProviderDarkList) OneByUid(uid string, t uint32, db *gorm.DB) (*RiskProviderDarkList, error) {
	var info RiskProviderDarkList
	err := db.Table(m.TableName()).
		Where("uid=?", uid).
		Where("type=?", t).
		Where("expired_at>?", time.Now().Unix()).
		Where("provider=0").
		First(&info).Error
	return &info, err
}

func (m *RiskProviderDarkList) Add(appId string, clientId uint32, uid string, t uint32, provider uint8, score uint8, expire uint32, db *gorm.DB) (bool, error) {
	condition := RiskProviderDarkList{
		Uid:      uid,
		Type:     uint8(t),
		Provider: provider,
	}

	var info RiskProviderDarkList
	err := db.Table(m.TableName()).Where(condition).Find(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return false, err
	} else if !errors.Is(err, xgorm.ErrRecordNotFound) && info.Id > 0 {
		m.UpdateScoreById(score, expire, info.Id, db)
		return true, nil
	}

	return m.Create(appId, clientId, uid, t, provider, score, expire, db)
}

func (m *RiskProviderDarkList) Create(appId string, clientId uint32, uid string, t uint32, provider uint8, score uint8, expire uint32, db *gorm.DB) (bool, error) {
	data := RiskProviderDarkList{
		AppId:     appId,
		ClientId:  uint64(clientId),
		Uid:       uid,
		Type:      uint8(t),
		Provider:  provider,
		Score:     score,
		ExpiredAt: time.Now().Add(time.Duration(expire) * time.Second).Unix(),
		IsDelete:  0,
	}

	if err := db.Table(m.TableName()).Create(&data).Error; err != nil {
		return false, err
	} else {
		return true, nil
	}
}

func (m *RiskProviderDarkList) UpdateScoreById(score uint8, expire uint32, id uint64, db *gorm.DB) (bool, error) {
	data := RiskProviderDarkList{
		Score:     score,
		ExpiredAt: time.Now().Add(time.Duration(expire) * time.Second).Unix(),
	}
	if err := db.Table(m.TableName()).
		Where("id=?", id).
		Updates(&data).Error; err != nil {
		return false, err
	}
	return true, nil
}

package model

import (
	"risk/utils"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

type RiskBlackList struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	AppId     string    `json:"app_id" gorm:"column:app_id"`         // app_id
	ClientID  uint64    `json:"client_id" gorm:"column:client_id"`   // 租户ID 0 全部租户适用
	Uid       string    `json:"uid" gorm:"column:uid"`               // 唯一标识
	Type      uint8     `json:"type" gorm:"column:type"`             // 1:nid 2:ip 3:device_id
	ExpiredAt int64     `json:"expired_at" gorm:"column:expired_at"` // 过期时间戳，0 永久有效
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

type RiskBlackListRdb struct {
	ExpiredAt int64 `json:"expired_at" gorm:"column:expired_at"` // 过期时间戳，0 永久有效
}

func (m *RiskBlackList) TableName() string {
	return "risk_black_list"
}

func (m *RiskBlackList) OneByIp(ip, appId string, clientId uint32, db *xgorm.DB) (*RiskBlackList, error) {
	return m.OneByCondition(ip, appId, clientId, 2, db)
}

func (m *RiskBlackList) OneByAccount(account, appId string, clientId uint32, db *xgorm.DB) (*RiskBlackList, error) {
	return m.OneByCondition(account, appId, clientId, 1, db)
}

func (m *RiskBlackList) OneByDevice(deviceId, appId string, clientId uint32, db *xgorm.DB) (*RiskBlackList, error) {
	return m.OneByCondition(deviceId, appId, clientId, 3, db)
}

func (m *RiskBlackList) OneByCondition(uid, appId string, clientId, t uint32, db *xgorm.DB) (*RiskBlackList, error) {
	info := RiskBlackList{}
	err := db.Table(m.TableName()).
		Select("id,client_id,uid,type,expired_at").
		Where("client_id=?", clientId).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("uid=?", uid).
		Where("expired_at>=? or expired_at=0", utils.NowUnix()).
		Where("type=?", t).
		Where("is_delete=0").
		First(&info).Error
	return &info, err
}

func (m *RiskBlackList) ExistByCondition(uid, appId string, clientId, t uint32, db *xgorm.DB) (bool, error) {
	var count int64
	err := db.Table(m.TableName()).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("client_id=?", clientId).
		Where("uid=?", uid).
		Where("expired_at>=? or expired_at=0", utils.NowUnix()).
		Where("type=?", t).
		Where("is_delete=0").
		Count(&count).Error
	return count > 0, err
}

func (r *RiskBlackList) Add(db *xgorm.DB) error {
	return db.Select("app_id", "client_id", "uid", "type", "expired_at").
		Create(r).Error
}

func (r *RiskBlackList) Update(db *xgorm.DB, id int64, expiredAt int64) error {
	return db.Model(&RiskBlackList{}).
		Where("id = ? and is_delete=0", id).
		Update("expired_at", expiredAt).Error
}

func (r *RiskBlackList) Delete(db *xgorm.DB, id int64) error {
	var m RiskBlackList
	return db.Model(&RiskBlackList{}).
		Delete(&m, id).Error
}

func (r *RiskBlackList) GetById(db *xgorm.DB, id int64) (*RiskBlackList, error) {
	var conf RiskBlackList
	err := db.Model(&RiskBlackList{}).
		Where("id = ? and is_delete=0", id).
		First(&conf).Error

	return &conf, err
}

func (r *RiskBlackList) GetByUnionUnique(db *xgorm.DB, clientId uint32, appId, uid string, t int32) (*RiskBlackList, error) {
	var conf RiskBlackList
	err := db.Model(&RiskBlackList{}).
		Where("client_id=?", clientId).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("uid=?", uid).
		Where("type=?", t).
		Where("is_delete=0").
		First(&conf).Error

	return &conf, err
}

func (r *RiskBlackList) FindAll(db *xgorm.DB) ([]RiskBlackList, error) {
	list := make([]RiskBlackList, 0)
	err := db.Model(&RiskBlackList{}).
		Where("is_delete=0").
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

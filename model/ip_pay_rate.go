package model

import "time"

type RiskIpPayRate struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	ClientID  uint64    `json:"client_id" gorm:"column:client_id"`   // 租户ID 0:全部租户适用
	Ip        string    `json:"ip" gorm:"column:ip"`                 // IP地址
	Rate      int       `json:"rate" gorm:"column:rate"`             // 支付成功率
	Rage      uint8     `json:"rage" gorm:"column:rage"`             // 统计范围
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

func (m *RiskIpPayRate) TableName() string {
	return "risk_ip_pay_rate"
}

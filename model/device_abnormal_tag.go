package model

import (
	"encoding/json"
	"errors"
	"github.com/bytedance/sonic"
	"github.com/gogf/gf/v2/container/garray"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"time"
)

type RiskDeviceAbnormalTag struct {
	ID            uint64    `json:"id" gorm:"column:id"`
	DOID          string    `json:"DOID" gorm:"column:DOID"`                       // DOID
	ThirdDeviceID string    `json:"third_device_id" gorm:"column:third_device_id"` // 供应商设备ID
	RiskTag       string    `json:"risk_tag" gorm:"column:risk_tag"`               // 风险标签集
	IsDeleted     uint8     `json:"is_deleted" gorm:"column:is_deleted"`           // 是否删除
	CreatedAt     time.Time `json:"created_at" gorm:"column:created_at"`           // 创建时间
	UpdatedAt     time.Time `json:"updated_at" gorm:"column:updated_at"`           // 更新时间
	DeletedAt     time.Time `json:"deleted_at" gorm:"column:deleted_at"`           // 删除时间
}

func (m *RiskDeviceAbnormalTag) TableName() string {
	return "risk_device_abnormal_tag"
}

func (m *RiskDeviceAbnormalTag) Add(DOID, thirdDeviceID, riskTag string, db *xgorm.DB) error {
	jsonRiskTag, _ := json.Marshal([]string{riskTag})
	data := &RiskDeviceAbnormalTag{
		DOID:          DOID,
		ThirdDeviceID: thirdDeviceID,
		RiskTag:       string(jsonRiskTag),
	}
	return db.Table(m.TableName()).Select("DOID", "third_device_id", "risk_tag").Create(data).Error
}

func (m *RiskDeviceAbnormalTag) ExistByDOID(DOID string, db *xgorm.DB) bool {
	var count int64
	db.Table(m.TableName()).Where("DOID = ?", DOID).Count(&count)
	return count > 0
}

func (m *RiskDeviceAbnormalTag) CreateOrUpdateTagByDOID(DOID, riskTag string, db *xgorm.DB) error {
	var info RiskDeviceAbnormalTag
	err := db.Table(m.TableName()).Where("DOID = ?", DOID).First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return m.Add(DOID, "", riskTag, db)
	}
	var riskTags []string
	err = sonic.UnmarshalString(info.RiskTag, &riskTags)
	if err != nil {
		return err
	}

	gTags := garray.NewStrArrayFrom(riskTags)
	if gTags.Contains(riskTag) {
		return nil
	}
	gTags.Append(riskTag)
	riskTag, err = sonic.MarshalString(gTags)
	if err != nil {
		return err
	}
	return db.Table(m.TableName()).Where("DOID = ?", DOID).Update("risk_tag", riskTag).Error
}

package model

import (
	"risk/utils"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

type RiskWhiteList struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	AppId     string    `json:"app_id" gorm:"column:app_id"`         // app_id
	ClientID  uint64    `json:"client_id" gorm:"column:client_id"`   // 租户ID 0:全部租户适用
	Uid       string    `json:"uid" gorm:"column:uid"`               // 唯一标识
	Type      uint8     `json:"type" gorm:"column:type"`             // 1:nid 2:ip 3:device_id
	ExpiredAt int64     `json:"expired_at" gorm:"column:expired_at"` // 过期时间戳，0 永久有效
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

type RiskWhiteListRdb struct {
	ExpiredAt int64 `json:"expired_at" gorm:"column:expired_at"` // 过期时间戳，0 永久有效
}

func (m *RiskWhiteList) TableName() string {
	return "risk_white_list"
}

func (m *RiskWhiteList) OneByIp(ip, appId string, clientId uint32, db *xgorm.DB) (*RiskWhiteList, error) {
	return m.OneByCondition(ip, appId, clientId, 2, db)
}

func (m *RiskWhiteList) OneByAccount(account, appId string, clientId uint32, db *xgorm.DB) (*RiskWhiteList, error) {
	return m.OneByCondition(account, appId, clientId, 1, db)
}

func (m *RiskWhiteList) OneByDevice(deviceId, appId string, clientId uint32, db *xgorm.DB) (*RiskWhiteList, error) {
	return m.OneByCondition(deviceId, appId, clientId, 3, db)
}

func (m *RiskWhiteList) OneByCondition(uid, appId string, clientId, t uint32, db *xgorm.DB) (*RiskWhiteList, error) {
	info := RiskWhiteList{}
	err := db.Table(m.TableName()).
		Select("id,client_id,uid,type,expired_at").
		Where("client_id=?", clientId).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("uid=?", uid).
		Where("expired_at>=? or expired_at=0", utils.NowUnix()).
		Where("type=?", t).
		Where("is_delete=0").
		First(&info).Error
	return &info, err
}

func (m *RiskWhiteList) ExistByCondition(uid, appId string, clientId, t uint32, db *xgorm.DB) (bool, error) {
	var count int64
	err := db.Table(m.TableName()).
		Select("id,client_id,uid,type,expired_at").
		Where("client_id=?", clientId).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("uid=?", uid).
		Where("expired_at>=? or expired_at=0", utils.NowUnix()).
		Where("type=?", t).
		Where("is_delete=0").
		Count(&count).Error
	return count > 0, err
}

func (r *RiskWhiteList) Add(db *xgorm.DB) error {
	return db.Select("app_id", "client_id", "uid", "type", "expired_at").
		Create(r).Error
}

func (r *RiskWhiteList) Update(db *xgorm.DB, id int64, expiredAt int64) error {
	return db.Model(&RiskWhiteList{}).
		Where("id = ? and is_delete=0", id).
		Update("expired_at", expiredAt).Error
}

func (r *RiskWhiteList) Delete(db *xgorm.DB, id int64) error {
	var m RiskWhiteList
	return db.Model(&RiskWhiteList{}).
		Delete(&m, id).Error
}

func (r *RiskWhiteList) GetById(db *xgorm.DB, id int64) (*RiskWhiteList, error) {
	var conf RiskWhiteList
	err := db.Model(&RiskWhiteList{}).
		Where("id = ? and is_delete=0", id).
		First(&conf).Error

	return &conf, err
}

func (r *RiskWhiteList) GetByUnionUnique(db *xgorm.DB, clientId uint32, appId, uid string, t int32) (*RiskWhiteList, error) {
	var conf RiskWhiteList
	err := db.Model(&RiskWhiteList{}).
		Where("client_id=?", clientId).
		Where("app_id in(?)", []string{appId, "all"}).
		Where("uid=?", uid).
		Where("type=?", t).
		Where("is_delete=0").
		First(&conf).Error

	return &conf, err
}

func (r *RiskWhiteList) FindAll(db *xgorm.DB) ([]RiskWhiteList, error) {
	list := make([]RiskWhiteList, 0)
	err := db.Model(&RiskWhiteList{}).
		Where("is_delete=0").
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

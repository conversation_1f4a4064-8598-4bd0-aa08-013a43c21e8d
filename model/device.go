package model

import (
	"errors"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"time"
)

type RiskDevice struct {
	ID         uint64    `json:"id" gorm:"column:id"`
	DOID       string    `json:"DOID" gorm:"column:DOID"`               // DOID
	Os         string    `json:"os" gorm:"column:os"`                   // 操作系统
	Ip         string    `json:"ip" gorm:"column:ip"`                   // 客户端IP
	DeviceId   string    `json:"device_id" gorm:"column:device_id"`     // 客户端DeviceId
	ProviderId uint64    `json:"provider_id" gorm:"column:provider_id"` // 第三方关联ID
	IsDeleted  uint8     `json:"is_deleted" gorm:"column:is_deleted"`   // 是否删除
	DeletedAt  time.Time `json:"deleted_at" gorm:"column:deleted_at"`   // 删除日期
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`   // 创建日期
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at"`   // 更新日期
}

func (m *RiskDevice) TableName() string {
	return "risk_device"
}

func (m *RiskDevice) Add(DOID, os, ip, deviceId string, providerId uint64, db *xgorm.DB) error {
	data := &RiskDevice{
		DOID:       DOID,
		Os:         os,
		Ip:         ip,
		DeviceId:   deviceId,
		ProviderId: providerId,
	}
	return db.Table(m.TableName()).Select("DOID", "os", "ip", "device_id", "information").Create(data).Error
}

func (m *RiskDevice) FindByDOID(DOID string, db *xgorm.DB) (*RiskDevice, error) {
	var info RiskDevice
	err := db.Table(m.TableName()).Where("DOID=?", DOID).First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &info, nil
}

func (m *RiskDevice) ExistByDOID(DOID string, db *xgorm.DB) (bool, error) {
	var count int64
	err := db.Table(m.TableName()).Where("DOID=?", DOID).Count(&count).Error
	return count > 0, err
}

func (m *RiskDevice) FindByOSAndDeviceId(os, deviceId string, db *xgorm.DB) (*RiskDevice, error) {
	var info RiskDevice
	err := db.Table(m.TableName()).Where("os=? AND device_id=?", os, deviceId).First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &info, nil

}

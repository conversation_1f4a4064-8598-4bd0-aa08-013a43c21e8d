package model

import "time"

type RiskConfigPayRate struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	ClientID  uint64    `json:"client_id" gorm:"column:client_id"`   // 租户ID 0:全部租户适用
	Rage      uint8     `json:"rage" gorm:"column:rage"`             // 统计范围
	Type      uint8     `json:"type" gorm:"column:type"`             // 类型 1:用户 2:ip
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

func (m *RiskConfigPayRate) TableName() string {
	return "risk_config_pay_rate"
}

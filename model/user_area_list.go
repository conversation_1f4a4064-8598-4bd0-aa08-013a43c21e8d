package model

import "time"

type RiskUserAreaList struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	Nid       uint64    `json:"nid" gorm:"column:nid"`               // 用户nid
	Province  string    `json:"province" gorm:"column:province"`     // 省份
	City      string    `json:"city" gorm:"column:city"`             // 市
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

func (m *RiskUserAreaList) TableName() string {
	return "risk_user_area_list"
}

package model

import (
	"context"
	"risk/startup"
)

type RiskModel struct {
	Id       uint64 `json:"id" gorm:"primaryKey"`
	ClientId int64  `json:"client_id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	Type     string `json:"type"`
	Status   string `json:"status"`
	SetList  string `json:"set_list"`
	IsDel    int8   `json:"is_del"`
}

func GetRiskModels(ctx context.Context) ([]RiskModel, error) {
	db := startup.GetMySql()
	var models []RiskModel
	err := db.Table("risk_model").
		Where("status = ?", 1).
		Where("is_del = ?", 0).
		Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

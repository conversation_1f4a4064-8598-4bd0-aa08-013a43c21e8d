package model

import (
	"context"
	"errors"
	"fmt"
	"risk/constant"
	"risk/rdbs"
	"risk/utils"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gorm.io/gorm"
)

type RiskDarkList struct {
	Id        uint64    `json:"id" gorm:"column:id;type:BIGINT UNSIGNED;AUTO_INCREMENT;NOT NULL"`
	AppId     string    `json:"app_id" gorm:"column:app_id"` // app_id
	ClientId  uint64    `json:"client_id" gorm:"column:client_id;type:BIGINT UNSIGNED;NOT NULL"`
	Uid       string    `json:"uid" gorm:"column:uid;type:VARCHAR(100);NOT NULL"`
	Type      uint8     `json:"type" gorm:"column:type;type:TINYINT UNSIGNED;NOT NULL"`
	Provider  uint8     `json:"provider" gorm:"column:provider;type:TINYINT;NOT NULL"`
	Score     uint8     `json:"score" gorm:"column:score;type:TINYINT;NOT NULL"`
	ExpiredAt int64     `json:"expired_at" gorm:"column:expired_at;type:TINYINT;NOT NULL"`
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete;type:TINYINT UNSIGNED;NOT NULL"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;type:TIMESTAMP;NOT NULL"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;type:TIMESTAMP;NOT NULL"`
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at;type:BIGINT UNSIGNED;NOT NULL"`
}

type RiskDarkListRdb struct {
	Score     uint8 `json:"score" gorm:"column:score;type:TINYINT;NOT NULL"`
	ExpiredAt int64 `json:"expired_at" gorm:"column:expired_at;type:TINYINT;NOT NULL"`
}

func (m *RiskDarkList) TableName() string {
	return "risk_dark_list"
}

func (m *RiskDarkList) OneByUid(uid string, t uint32, db *gorm.DB) (*RiskDarkList, error) {
	var info RiskDarkList
	err := db.Table(m.TableName()).
		Where("uid=?", uid).
		Where("type=?", t).
		Where("expired_at >=?", utils.NowUnix()).
		Where("provider=0").
		First(&info).Error
	return &info, err
}

func (m *RiskDarkList) Add(appId string, clientId uint32, uid string, t uint8, provider uint8, score uint8, expire int64, db *gorm.DB) error {
	condition := RiskDarkList{
		Uid:      uid,
		Type:     t,
		Provider: provider,
	}

	var info RiskDarkList
	err := db.Table(m.TableName()).Where(condition).First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return err
	} else if !errors.Is(err, xgorm.ErrRecordNotFound) && info.Id > 0 {
		if info.Score != score {
			_ = m.UpdateScoreById(score, expire, info.Id, db, uid, t)
		}
		return nil
	}

	return m.Create(appId, clientId, uid, t, provider, score, expire, db)
}

func (m *RiskDarkList) Create(appId string, clientId uint32, uid string, t uint8, provider uint8, score uint8, expire int64, db *gorm.DB) error {
	data := RiskDarkList{
		AppId:     appId,
		ClientId:  uint64(clientId),
		Uid:       uid,
		Type:      t,
		Provider:  provider,
		Score:     score,
		ExpiredAt: 0,
		IsDelete:  0,
	}
	if expire > 0 {
		data.ExpiredAt = time.Now().Add(time.Duration(expire) * time.Second).Unix()
	}
	if err := db.Table(m.TableName()).Create(&data).Error; err != nil {
		return err
	} else {
		if err := m.AddCache(&data); err != nil {
			return fmt.Errorf("add cache error: %w", err)
		}
		return nil
	}
}

func (m *RiskDarkList) UpdateScoreById(score uint8, expire int64, id uint64, db *gorm.DB, uid string, t uint8) error {
	data := RiskDarkList{
		Score:     score,
		ExpiredAt: 0,
	}
	if expire > 0 {
		data.ExpiredAt = time.Now().Add(time.Duration(expire) * time.Second).Unix()
	}
	if err := db.Table(m.TableName()).
		Where("id=?", id).
		Updates(&data).Error; err != nil {
		return err
	}
	data.Type = t
	data.Uid = uid
	if err := m.AddCache(&data); err != nil {
		return fmt.Errorf("add cache error: %w", err)
	}

	return nil
}

func (m *RiskDarkList) Delete(db *xgorm.DB, id int64) error {
	return db.Model(&RiskDarkList{}).
		Delete(&m, id).Error
}

func (m *RiskDarkList) GetByUnionUnique(db *xgorm.DB, uid string, t int32) (*RiskDarkList, error) {
	var conf RiskDarkList
	err := db.Model(&RiskDarkList{}).
		Where("uid=?", uid).
		Where("type=?", t).
		Where("provider=0").
		Where("is_delete=0").
		First(&conf).Error

	return &conf, err
}

func (m *RiskDarkList) FindAll(db *xgorm.DB) ([]RiskDarkList, error) {
	list := make([]RiskDarkList, 0)
	err := db.Model(&RiskDarkList{}).
		Where("is_delete=0").
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (m *RiskDarkList) GetById(db *xgorm.DB, id int64) (*RiskDarkList, error) {
	var conf RiskDarkList
	err := db.Model(&RiskDarkList{}).
		Where("id = ? and is_delete=0", id).
		First(&conf).Error

	return &conf, err
}

func (m *RiskDarkList) AddCache(conf *RiskDarkList) error {
	soncVal, err := sonic.MarshalString(RiskDarkListRdb{
		Score:     conf.Score,
		ExpiredAt: conf.ExpiredAt,
	})
	if err != nil {
		return err
	}

	rdbKey := fmt.Sprintf("%st-%d:u-%s", constant.RdbDarkList, conf.Type, conf.Uid)

	if err := rdbs.NewCache(context.Background()).Set(rdbKey, soncVal); err != nil {
		return err
	}

	return nil
}

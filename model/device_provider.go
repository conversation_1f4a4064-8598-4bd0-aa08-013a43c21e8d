package model

import (
	"errors"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"time"
)

type RiskDeviceProvider struct {
	ID               uint64    `json:"id" gorm:"column:id"`
	DOID             string    `json:"DOID" gorm:"column:DOID"`                           // DOID
	SdkDeviceId      string    `json:"sdk_device_id" gorm:"column:sdk_device_id"`         // SDK设备ID
	Os               string    `json:"os" gorm:"column:os"`                               // 操作系统
	Ip               string    `json:"ip" gorm:"column:ip"`                               // 客户端IP
	Provider         string    `json:"provider" gorm:"column:provider"`                   // 供应商
	ThirdDeviceID    string    `json:"third_device_id" gorm:"column:third_device_id"`     // 供应商设备ID
	ThirdData        string    `json:"third_data" gorm:"column:third_data"`               // 供应商原生数据
	ThirdInformation string    `json:"third_information" gorm:"column:third_information"` // 供应商收集的设备信息
	IsDeleted        int8      `json:"is_deleted" gorm:"column:is_deleted"`               // 是否删除
	DeletedAt        uint64    `json:"deleted_at" gorm:"column:deleted_at"`               // 删除日期
	CreatedAt        time.Time `json:"created_at" gorm:"column:created_at"`               // 创建日期
	UpdatedAt        time.Time `json:"updated_at" gorm:"column:updated_at"`               // 更新日期
}

func (m *RiskDeviceProvider) TableName() string {
	return "risk_device_provider"
}

func (m *RiskDeviceProvider) FindByProviderAndThirdDeviceID(provider, thirdDeviceID string, db *xgorm.DB) (*RiskDeviceProvider, error) {
	var info RiskDeviceProvider
	err := db.Table(m.TableName()).
		Where("provider=?", provider).
		Where("third_device_id=?", thirdDeviceID).
		First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &info, err
}

func (m *RiskDeviceProvider) FindByDOID(DOID string, db *xgorm.DB) (*RiskDeviceProvider, error) {
	var info RiskDeviceProvider
	err := db.Table(m.TableName()).Where("DOID=?", DOID).First(&info).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &info, nil
}

func (m *RiskDeviceProvider) ExistByDOID(DOID string, db *xgorm.DB) (bool, error) {
	var count int64
	err := db.Table(m.TableName()).Where("DOID=?", DOID).Count(&count).Error
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		return false, err
	} else if errors.Is(err, xgorm.ErrRecordNotFound) {
		return false, nil
	}
	return count > 0, nil
}

func (m *RiskDeviceProvider) Add(data *RiskDeviceProvider, db *xgorm.DB) error {
	return db.Omit("is_deleted", "deleted_at").Table(m.TableName()).Create(data).Error
}

func (m *RiskDeviceProvider) UpdateByDOID(DOID, provider, thirdDeviceID, thirdData string, db *xgorm.DB) error {
	data := &RiskDeviceProvider{
		DOID:          DOID,
		Provider:      provider,
		ThirdDeviceID: thirdDeviceID,
		ThirdData:     thirdData,
	}
	return db.Table(m.TableName()).Where("DOID=?", DOID).Updates(data).Error
}

func (m *RiskDeviceProvider) UpdateByThird(DOID, provider, thirdDeviceID string, db *xgorm.DB) error {
	data := &RiskDeviceProvider{
		DOID: DOID,
	}
	return db.Table(m.TableName()).
		Where("provider=?", provider).
		Where("third_device_id=?", thirdDeviceID).
		Updates(data).Error
}

func (m *RiskDeviceProvider) FirstOrCreateByThirdDeviceID(provider, DOID, thirdDeviceID, thirdData, thirdInformation, sdkDeviceId string, db *xgorm.DB) error {
	data := &RiskDeviceProvider{
		DOID:             DOID,
		Provider:         provider,
		ThirdDeviceID:    thirdDeviceID,
		ThirdData:        thirdData,
		ThirdInformation: thirdInformation,
		SdkDeviceId:      sdkDeviceId,
	}

	return db.Select("DOID", "provider", "third_device_id", "third_data", "third_information", "sdk_device_id").
		Table(m.TableName()).
		Where("third_device_id=?", thirdDeviceID).
		FirstOrCreate(data).Error

}

package model

import (
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

type RiskConfigApp struct {
	ID        uint64    `json:"id" gorm:"column:id"`                 // 主键
	Title     string    `json:"title" gorm:"column:title"`           // 标题
	AppId     string    `json:"app_id" gorm:"column:app_id"`         // App ID 0:全部app适用
	ClientID  uint64    `json:"client_id" gorm:"column:client_id"`   // 租户ID 0:全部租户适用
	Frequency string    `json:"frequency" gorm:"column:frequency"`   // 频率配置
	IsOpen    uint8     `json:"is_open" gorm:"column:is_open"`       // 是否开启 0:未开启 1:开启
	Type      uint16    `json:"type" gorm:"column:type"`             // 类型 1:登录设备 2:登录IP 3:黑产检测 4:账号频率 5:IP频率 6 设备频率 7:账号成功率 8:IP成功率 9设备成功率 10 是否开启人机校验 11 请求间隔 12 全局开关
	Handle    uint64    `json:"handle" gorm:"column:handle"`         // 处理器
	IsDelete  uint8     `json:"is_delete" gorm:"column:is_delete"`   // 是否删除
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt uint64    `json:"deleted_at" gorm:"column:deleted_at"` // 删除时间
}

type RiskConfigAppRdb struct {
	Frequency string    `json:"frequency" gorm:"column:frequency"`   // 频率配置
	Handle    uint64    `json:"handle" gorm:"column:handle"`         // 处理器
	IsOpen    uint8     `json:"is_open" gorm:"column:is_open"`       // 是否开启 0:未开启 1:开启
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
}

type FrequencyArray struct {
	Time  uint32 `json:"time"`
	Count uint32 `json:"count"`
}

func (m *RiskConfigApp) TableName() string {
	return "risk_config_app"
}

func (m *RiskConfigApp) OneByClientIdAndType(appId string, clientId, t uint32, db *xgorm.DB) (*RiskConfigApp, error) {
	var info RiskConfigApp
	err := db.Table(m.TableName()).Select("id,app_id,frequency,client_id,type,is_open,updated_at").
		Where("client_id=?", clientId).
		Where("app_id=?", appId).
		Where("type=?", t).
		Where("is_delete=0").
		First(&info).Error
	return &info, err
}

func (m *RiskConfigApp) FindAll(db *xgorm.DB) ([]RiskConfigApp, error) {
	list := make([]RiskConfigApp, 0)
	gdb := db.Table(m.TableName()).Select("app_id,frequency,client_id,type,is_open,updated_at,handle")
	err := gdb.Where("is_delete=0").Find(&list).Order("type ASC").Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (r *RiskConfigApp) Add(db *xgorm.DB) error {
	return db.Select("app_id", "client_id", "frequency", "is_open", "type", "title", "handle").
		Create(r).Error
}

func (r *RiskConfigApp) Update(db *xgorm.DB, id int64) error {
	return db.Model(&RiskConfigApp{}).
		Select("frequency", "is_open", "handle").
		Where("id=? and is_delete=0", id).
		Updates(r).Error
}

func (r *RiskConfigApp) Delete(db *xgorm.DB, id int64) error {
	return db.Model(&RiskConfigApp{}).
		Where("id = ?", id).
		Update("is_delete", 1).Error
}

func (r *RiskConfigApp) GetById(db *xgorm.DB, id int64) (*RiskConfigApp, error) {
	var conf RiskConfigApp
	err := db.Model(&RiskConfigApp{}).
		Where("id = ? and is_delete=0", id).
		First(&conf).Error

	return &conf, err
}

func (r *RiskConfigApp) GetByUnionUnique(db *xgorm.DB, clientId uint32, appId string, t int32) (*RiskConfigApp, error) {
	return r.OneByClientIdAndType(appId, clientId, uint32(t), db)
}

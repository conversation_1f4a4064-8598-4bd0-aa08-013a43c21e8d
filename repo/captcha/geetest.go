package captcha

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"go.uber.org/zap"
	"net/url"
	"risk/config"
	"risk/startup"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
)

type Geetest struct {
	logger *xlog.Logger
	ctx    context.Context
}

func NewGeetest(ctx context.Context) *Geetest {
	return &Geetest{
		logger: xlog.FromContext(ctx),
		ctx:    ctx,
	}
}

func (g *Geetest) Validate(captchaId, lotNumber, captchaOutput, PassToken, genTime, captchaKey, appId string) (hasPass bool, err error) {
	if config.Get().Machine.Geetest.Disable {
		//极验开关
		return true, nil
	}
	formData := url.Values{}
	formData.Set("lot_number", lotNumber)
	formData.Set("captcha_output", captchaOutput)
	formData.Set("pass_token", PassToken)
	formData.Set("gen_time", genTime)
	formData.Set("sign_token", g.hmacEncode(captchaKey, lotNumber))
	formData.Set("captcha_id", captchaId)

	g.logger.Info("geetest.Validate http body", zap.String("data", formData.Encode()))
	client := xresty.New()
	client.SetDebug(true)
	client.SetTimeout(3 * time.Second)
	resp, err := client.NewRequest().
		SetContext(g.ctx).
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetBody(formData.Encode()).
		Post(config.Get().Machine.Geetest.ApiUri)
	if err != nil {
		g.logger.Error("geetest.Validate error:%v", zap.Error(err))
		return true, nil
	}

	if resp.StatusCode() != 200 {
		g.logger.Error("geetest.Validate http code", zap.Int("http code", resp.StatusCode()))
		return true, nil
	}
	g.logger.Info("geetest.Validate http response", zap.String("body", string(resp.Body())))
	root, err := sonic.Get(resp.Body())
	if err != nil {
		g.logger.Error("geetest.Validate sonic response error", zap.Error(err))
		return true, nil
	}

	status, err := root.GetByPath("status").String()
	if err != nil {
		g.logger.Error("geetest.Validate sonic status, error", zap.Error(err))
		return true, nil
	} else if status != "success" {
		g.logger.Error("geetest.Validate response status != success", zap.String("body", string(resp.Body())))
		return false, nil
	}

	result, err := root.GetByPath("result").String()
	if err != nil {
		g.logger.Error("geetest.Validate sonic status, err:", zap.Error(err))
		return true, nil
	} else if result != "success" {
		g.logger.Error("geetest.Validate response result != success", zap.String("body", string(resp.Body())))
		return false, nil
	}

	if g.DeviceCheck(appId) &&
		(config.Get().Machine.Geetest.EnableCheckRiskDevice || config.Get().Machine.Geetest.EnableCheckIp) {

		if config.Get().Machine.Geetest.EnableCheckRiskDevice {
			riskDevice, err := root.GetByPath("captcha_args", "risk_device").Int64()
			if err != nil {
				g.logger.Error("geetest.Validate sonic risk_device error", zap.Error(err))
				return true, nil
			}
			platform := startup.GetVerify().GetAppPlatform(appId)
			if riskDevice != 0 && (platform == "ios" || platform == "android") {
				g.logger.Warn("geetest.Validate risk device check failed",
					zap.Int64("risk_device", riskDevice))
				return false, nil
			}
		}

		if config.Get().Machine.Geetest.EnableCheckIp {
			geestestUserIp, err := root.GetByPath("captcha_args", "user_ip").String()
			if err != nil {
				g.logger.Error("geetest.Validate sonic user_ip error", zap.Error(err))
				return true, nil
			}

			if geestestUserIp != shared.GetClientIP(xgin.FromContext(g.ctx)) {
				g.logger.Warn("geetest.Validate user ip check failed",
					zap.String("geetest_user_ip", geestestUserIp),
					zap.String("client_ip", shared.GetClientIP(xgin.FromContext(g.ctx))))
				return false, nil
			}
		}
	}
	return true, nil
}

func (g *Geetest) hmacEncode(key string, data string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(data))
	return hex.EncodeToString(mac.Sum(nil))
}

func (g *Geetest) DeviceCheck(appId string) bool {
	configOpen := config.Get().Machine.Geetest.DeviceCheckAppId
	for _, configAppId := range configOpen {
		if appId == configAppId {
			return true
		}
	}
	return false
}

package repo

import (
	"context"
	"fmt"
	ePB "gitlab.papegames.com/biz/protobuf/risk/risk-evaluation-engine"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"risk/config"
	"risk/utils"
	"risk/xmetric"
	"strconv"
	"time"
)

var evaluationConn *grpc.ClientConn

func NewEvaluationConn() error {
	addr := fmt.Sprintf("%s:%d", config.Get().GRPCStrategy.EvaluationUri.Host, config.Get().GRPCStrategy.EvaluationUri.Port)
	var err error
	evaluationConn, err = grpc.NewClient(addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithIdleTimeout(time.Duration(config.Get().GRPCStrategy.IdleTimeout)*time.Second),
	)
	return err
}

type Evaluation struct {
}

func NewEvaluation() *Evaluation {
	return &Evaluation{}
}

func (e *Evaluation) getConn() (*grpc.ClientConn, error) {
	return evaluationConn, nil
	//addr := fmt.Sprintf("%s:%d", config.Get().GRPCStrategy.EvaluationUri.Host, config.Get().GRPCStrategy.EvaluationUri.Port)
	//return grpc.NewClient(addr,
	//	grpc.WithTransportCredentials(insecure.NewCredentials()),
	//	grpc.WithIdleTimeout(time.Duration(config.Get().GRPCStrategy.IdleTimeout)*time.Second),
	//)
}

// ListCheck 黑白名单检查
func (e *Evaluation) ListCheck(ctx context.Context, request *ePB.ListCheckRequest) (*ePB.ListCheckResp, error) {
	conn, err := e.getConn()
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "list_check"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "evaluation", path, strconv.Itoa(int(request.ClientId)))
	}()

	client := ePB.NewRiskEvaluationEngineServiceClient(conn)
	return client.ListCheck(utils.GrpcSetContext(ctx), request)
}

// DisposeEvaluate 处置判断获取
func (e *Evaluation) DisposeEvaluate(ctx context.Context, request *ePB.EvaluateRequest) (*ePB.EvaluateResp, error) {
	conn, err := e.getConn()
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "dispose_evaluate"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "evaluation", path, request.SceneId)
	}()
	client := ePB.NewRiskEvaluationEngineServiceClient(conn)
	return client.DisposeEvaluate(utils.GrpcSetContext(ctx), request)
}

// ReportCheckSuccess 处置验证上报
func (e *Evaluation) ReportCheckSuccess(ctx context.Context, request *ePB.ReportCheckSuccessRequest) error {
	conn, err := e.getConn()
	if err != nil {
		return err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "report_check_success"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "evaluation", path, request.SceneId)
	}()

	client := ePB.NewRiskEvaluationEngineServiceClient(conn)
	_, err = client.ReportCheckSuccess(utils.GrpcSetContext(ctx), request)
	return err
}

package dark

import (
	"context"
	"errors"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/endpoints"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/saf"
	"github.com/bytedance/sonic"
	"github.com/bytedance/sonic/ast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/config"
	"risk/constant"
	"risk/xmetric"
	"strconv"
	"strings"
	"time"
)

type <PERSON><PERSON> struct {
}

func (a *<PERSON>yun) CheckIP(ip string, logger *xlog.Logger, ctx context.Context) (bool, error) {
	data := map[string]interface{}{
		"ip": ip,
	}
	service := "risk_intelligence_ip"
	response, err := a.done(data, service, logger)
	if err != nil {
		logger.Error(fmt.Sprintf("http err:%v", err))
		return true, err
	}
	score, err := response.GetByPath("Data", "score").Float64()
	scoreInt := int32(score * 100)
	if scoreInt > config.Get().Dark.Aliyun.IpRisk.RiskSource {
		return false, nil
	}
	return true, nil
}

func (a *Aliyun) CheckDevice(deviceId string, fid string, logger *xlog.Logger, ctx context.Context) (bool, error) {
	data := map[string]interface{}{
		"deviceToken": deviceId,
		"id":          fid,
	}
	service := "device_risk"
	response, err := a.done(data, service, logger)
	if err != nil {
		logger.Error(fmt.Sprintf("http err:%v", err))
		return true, err
	}

	tags, err := response.GetByPath("Data", "tags").String()
	if err != nil {
		logger.Error(fmt.Sprintf("response get tags:%v", err))
		return true, err
	}

	confTags := strings.Split(config.Get().Dark.Aliyun.DeviceRisk.Tags, ",")
	//rw_0101 疑似虚拟浏览器,rw_0103 疑似自动化工具,rw_0108 异常行为轨迹,token_invalid 无效的设备Token,token_tampered token被截取、篡改,token_replay Token重放
	for _, tag := range confTags {
		if strings.Index(tags, tag) > -1 {
			logger.Warn(fmt.Sprintf("aliyun check device:%v,tag:%v", deviceId, tag))
			return false, nil
		}
	}
	return true, nil
}

func (a *Aliyun) done(data map[string]interface{}, s string, logger *xlog.Logger) (*ast.Node, error) {
	// 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
	// 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
	formData, err := sonic.MarshalString(data)

	logger.Info(fmt.Sprintf("aliyun http body:%#v", formData))

	if err != nil {
		return nil, err
	}
	conf := config.Get().Dark.Aliyun
	client, err := saf.NewClientWithAccessKey("cn-shanghai", conf.AppId, conf.AppSecret)
	client.SetConnectTimeout(3 * time.Second)
	client.SetReadTimeout(3 * time.Second)
	endpoints.AddEndpointMapping("cn-shanghai", "saf", "saf.cn-shanghai.aliyuncs.com")
	request := saf.CreateExecuteRequestRequest()
	// 产品Service请参考[公共参数]文档中的Service字段描述
	request.Service = s
	request.ServiceParameters = formData
	request.Scheme = "https"
	response, err := client.ExecuteRequest(request)
	if err != nil {
		return nil, err
	} else if !response.IsSuccess() {
		xmetric.Metrics.Inc("aliyun", "curl", "error")
		logger.Error(fmt.Sprintf(" http failed,http code: %d", response.Code))
		return nil, errors.New("aliyun http code:" + strconv.Itoa(response.Code))
	}
	logger.Info(fmt.Sprintf("http request data:%v, response data: %v", formData, response.GetHttpContentString()))
	root, err := sonic.Get(response.GetHttpContentBytes())
	if err != nil {
		xmetric.Metrics.Inc("aliyun", "curl", "body-error")
		return nil, err
	}
	code, err := root.Get("Code").Int64()
	if err != nil {
		xmetric.Metrics.Inc("aliyun", "curl", "code-error")
		return nil, err
	} else if code != 200 {
		xmetric.Metrics.Inc("aliyun", "curl", "body-code-error")
		return nil, errors.New("http response code:" + strconv.FormatInt(code, 10))
	}

	xmetric.Metrics.Inc("aliyun", "curl", "success")
	return &root, nil

}

func (a *Aliyun) GetName() uint8 {
	return constant.ProviderAliyun
}

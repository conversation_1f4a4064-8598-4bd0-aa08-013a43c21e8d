package dark

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/bytedance/sonic/ast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"risk/config"
	"risk/constant"
	"risk/xmetric"
	"strconv"
	"time"
)

type Ishumei struct {
}

func (i *Ishumei) CheckIP(ip string, logger *xlog.Logger, ctx context.Context) (bool, error) {

	data := make(map[string]interface{})
	data["ip"] = ip
	logger.Info(fmt.Sprintf("数美IP检查 ip:%v", ip))

	root, err := i.done(data, logger, ctx)
	if err != nil {
		logger.Error(fmt.Sprintf("http error: %v", err))
		return true, err
	}
	ipLabels := root.Get("ipLabels")

	//是否风险IP
	_, err = ipLabels.GetByPath("risk_ip", "risk_ip").Int64()
	if err != nil {
		logger.Info(fmt.Sprintf("ip:%v 检测结果无 风险IP 值", ip))
		return true, err
	}
	//else if riskIP == 1 {
	//	logger.Error(fmt.Sprintf("ip:%v 是风险IP", ip))
	//	return false, nil
	//}

	//是否秒拨IP
	bSecdial, err := ipLabels.GetByPath("b_secdial", "b_secdial").Int64()
	if err != nil {
		logger.Info(fmt.Sprintf("ip:%v 检测结果无 秒拨IP 值", ip))
	} else if bSecdial == 1 {
		logger.Warn(fmt.Sprintf("ip:%v 检测结果是 秒拨IP", ip))
		return false, nil
	}
	//是否机房IP
	bIdc, err := ipLabels.GetByPath("b_idc", "b_idc").Int64()
	if err != nil {
		logger.Info(fmt.Sprintf("ip:%v 检测结果无 机房IP 值", ip))
	} else if bIdc == 1 {
		logger.Warn(fmt.Sprintf("ip:%v 检测结果是 机房IP", ip))
		return false, nil
	}
	//是否代理IP
	bProxy, err := ipLabels.GetByPath("b_proxy", "b_proxy").Int64()
	if err != nil {
		logger.Info(fmt.Sprintf("ip:%v 检测结果无 代理IP 值", ip))
	} else if bProxy == 1 {
		logger.Warn(fmt.Sprintf("ip:%v 检测结果是 代理IP", ip))
		return false, nil
	}
	return true, nil
}

func (i *Ishumei) CheckDevice(deviceId string, DOID string, logger *xlog.Logger, ctx context.Context) (bool, error) {
	data := map[string]interface{}{
		"deviceId": deviceId,
		//"id":       DOID,
	}

	logger.Info(fmt.Sprintf("数美设备检查 DOID:%v device id:%v", DOID, deviceId))

	root, err := i.done(data, logger, ctx)
	if err != nil {
		logger.Error(fmt.Sprintf("http error: %v", err))
		return true, err
	}
	//设备画像是否存在
	exist, err := root.Get("profileExist").Int64()
	if err != nil {
		return true, err
	} else if exist != 1 {
		logger.Error(fmt.Sprintf("device id:%v 不存在的设备", deviceId))
		return false, nil
	}
	shumeiDeviceId, err := root.GetByPath("deviceLabels", "id").String()
	if err == nil && shumeiDeviceId != "" {
		logger.Info(fmt.Sprintf("数美 shumeiDeviceId:%v DOID：%v", shumeiDeviceId, DOID))
	}

	//设备数据上报时全部或部分关键数据缺失或被伪造，进而伪造设备ID打业务接口的恶意行为，取值：0：非伪造设备1：是伪造设备
	bFaker, err := root.GetByPath("deviceLabels", "fake_device", "b_faker").Int64()
	if err == nil && bFaker == 1 {
		logger.Error(fmt.Sprintf("DOID:%v 检测结果是 伪造设备，shumeiDeviceId:%v", DOID, shumeiDeviceId))
		return false, nil
	}
	//自动化操作的多台设备，组成设备农场，批量作恶，取值：
	//0：非农场设备
	//1：是农场设备
	bFarmer, err := root.GetByPath("deviceLabels", "fake_device", "b_farmer").Int64()
	if err == nil && bFarmer == 1 {
		logger.Error(fmt.Sprintf("DOID:%v 检测结果是 农场设备，shumeiDeviceId:%v", DOID, shumeiDeviceId))
		return false, nil
	}
	//安装积分墙等网赚类工具的设备，取值：
	//0：非积分墙设备
	//1：是积分墙设备
	bOfferwall, err := root.GetByPath("deviceLabels", "fake_device", "b_offerwall").Int64()
	if err == nil && bOfferwall == 1 {
		logger.Error(fmt.Sprintf("DOID:%v 检测结果是 积分墙设备，shumeiDeviceId:%v", DOID, shumeiDeviceId))
		return false, nil
	}
	return true, nil

}

func (i *Ishumei) done(data map[string]interface{}, logger *xlog.Logger, ctx context.Context) (*ast.Node, error) {
	conf := config.Get().Dark.Ishumei
	formData := make(map[string]interface{})
	formData["accessKey"] = conf.AccessKey
	formData["data"] = data

	logger.Info(fmt.Sprintf("ishumei http body:%#v", formData))

	formDataJson, err := sonic.MarshalString(formData)
	if err != nil {
		return nil, err
	}

	client := xresty.New()
	client.SetDebug(true)
	client.SetTimeout(3 * time.Second)

	resp, err := client.NewRequest().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(formDataJson).
		Post(conf.Uri)
	if err != nil {
		xmetric.Metrics.Inc("ishumei", "curl", "error")
		return nil, err
	}
	if resp.StatusCode() != 200 {
		xmetric.Metrics.Inc("ishumei", "curl", "code-error")
		return nil, errors.New("http code:" + resp.Status())
	}

	logger.Info(fmt.Sprintf("ishumei http request data:%v, response data: %v", formDataJson, string(resp.Body())))

	root, err := sonic.Get(resp.Body())
	if err != nil {
		xmetric.Metrics.Inc("ishumei", "curl", "body-error")
		logger.Error(fmt.Sprintf("sonic error:%v", err))
		return nil, err
	}
	code, err := root.GetByPath("code").Int64()
	if err != nil {
		xmetric.Metrics.Inc("ishumei", "curl", "code-error")
		logger.Error(fmt.Sprintf("sonic code error:%v", err))
		return nil, err
	}
	if code != 1100 {
		xmetric.Metrics.Inc("ishumei", "curl", "body-code-error")
		logger.Error(fmt.Sprintf("ishumei response failed,data:%v", string(resp.Body())))
		return nil, errors.New("请求失败，code:" + strconv.FormatInt(code, 10))
	}
	xmetric.Metrics.Inc("ishumei", "curl", "success")
	return &root, nil
}

func (a *Ishumei) GetName() uint8 {
	return constant.ProviderIshumei
}

package dark

import (
	"fmt"
	"github.com/bytedance/sonic"
	"testing"
)

func TestIshumei_CheckIP(t *testing.T) {
	ret := "{\n    \"code\": 1100,\n    \"detail\": null,\n    \"devicePrimaryInfo\": {},\n    \"deviceRiskLabels\": [],\n    \"ipLabels\": {\n        \"b_cgn\": {\n            \"b_cgn\": 0\n        },\n        \"ip_city\": {\n            \"ip_city\": \"深圳\"\n        },\n        \"ip_country\": {\n            \"ip_country\": \"中国\"\n        },\n        \"ip_latitude\": {\n            \"ip_latitude\": 22.5427\n        },\n        \"ip_longitude\": {\n            \"ip_longitude\": 114.01449\n        },\n        \"ip_owner\": {\n            \"ip_owner\": \"China Telecom\"\n        },\n        \"ip_province\": {\n            \"ip_province\": \"广东\"\n        },\n        \"risk_ip\": {\n            \"risk_ip\": 0\n        }\n    },\n    \"message\": \"成功\",\n    \"profileExist\": 1,\n    \"requestId\": \"23e753672a69050d3dfb6db5d3259413\",\n    \"tokenProfileLabels\": [],\n    \"tokenRiskLabels\": []\n}"
	root, err := sonic.GetFromString(ret)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(root.Raw())
}

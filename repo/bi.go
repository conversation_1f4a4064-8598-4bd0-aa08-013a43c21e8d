package repo

import (
	"context"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"risk/startup"
	"time"
)

type ResultEvent struct {
	EventID          int64  `json:"eventid,omitempty"`
	ClientID         string `json:"clientid,omitempty"`
	PartEvent        string `json:"part_event"`
	DtEventTime      string `json:"dteventtime"`
	EventTimeStamp   int64  `json:"eventtimestamp,omitempty"`
	LoginRequestID   string `json:"loginrequestid,omitempty"`
	SessionID        string `json:"sessionid,omitempty"`
	DeviceID         string `json:"deviceid,omitempty"`
	DOID             string `json:"doid,omitempty"`
	AppID            string `json:"appid,omitempty"`
	VOpenID          string `json:"vopenid,omitempty"`
	SystemType       string `json:"systemtype,omitempty"`
	SDKVersion       string `json:"sdkversion,omitempty"`
	ClientVersion    string `json:"clientversion,omitempty"`
	PackageName      string `json:"packagename,omitempty"`
	RiskScoreTrigger int    `json:"riskscoretrigger,omitempty"`
	RiskscoreScene   string `json:"riskscoreScene"`
	RiskScore        int64  `json:"riskscore,omitempty"`
	RiskLabelScore   string `json:"risklabelscore,omitempty"`
	RiskLabel        string `json:"risklabel,omitempty"`
	CaptchaEventID   string `json:"captchaeventid"`
	CaptchaStatus    int    `json:"captchastatus"`
	CaptchaType      uint32 `json:"captchatype"`
	CaptchaResult    int    `json:"captcharesult"`
	FailedReason     string `json:"failedreason"`
	PassExpired      string `json:"passexpired"`
	RejectExpired    string `json:"rejectexpired"`
	Extra            string `json:"extra,omitempty"`
	ClientIP         string `json:"clientip,omitempty"`
	RoleId           string `json:"roleid,omitempty"`
}

// FromMapToResultEvent bi上报时间从请求中的额外参数获取
func FromMapToResultEvent(ctx context.Context, extraInfo map[string]string) *ResultEvent {
	now := time.Now()
	loginRequestId := xnet.GetRequestId(xgin.FromContext(ctx).Request)
	var event *ResultEvent
	if extraInfo != nil {
		event = &ResultEvent{
			EventID:        time.Now().UnixNano(),
			DtEventTime:    now.Format(time.DateTime),
			EventTimeStamp: now.Unix(),
			LoginRequestID: loginRequestId,
			SessionID:      extraInfo["session_id"],
			DeviceID:       extraInfo["device_id"],
			DOID:           extraInfo["doid"],
			AppID:          extraInfo["appid"],
			VOpenID:        extraInfo["vopenID"],
			SystemType:     extraInfo["system_type"],
			SDKVersion:     extraInfo["sdk_version"],
			ClientVersion:  extraInfo["client_version"],
			PackageName:    extraInfo["package_name"],
			ClientIP:       extraInfo["client_ip"],
			PartEvent:      "CaptchaResult",
		}
	} else {
		event = &ResultEvent{
			EventID:        time.Now().UnixNano(),
			DtEventTime:    now.Format(time.DateTime),
			EventTimeStamp: now.Unix(),
			LoginRequestID: loginRequestId,
			PartEvent:      "CaptchaResult",
		}
	}

	return event
}

type ScoreEvent struct {
	EventID          int64  `json:"eventid,omitempty"`
	PartEvent        string `json:"part_event"`
	ClientID         string `json:"clientid,omitempty"`
	DtEventTime      string `json:"dteventtime"`
	EventTimeStamp   int64  `json:"eventtimestamp,omitempty"`
	LoginRequestID   string `json:"loginrequestid,omitempty"`
	SessionID        string `json:"sessionid,omitempty"`
	DeviceID         string `json:"deviceid,omitempty"`
	DOID             string `json:"doid,omitempty"`
	AppID            string `json:"appid,omitempty"`
	VOpenID          string `json:"vopenid,omitempty"`
	SystemType       string `json:"systemtype,omitempty"`
	SDKVersion       string `json:"sdkversion,omitempty"`
	ClientVersion    string `json:"clientversion,omitempty"`
	PackageName      string `json:"packagename,omitempty"`
	RiskScoreTrigger int    `json:"riskscoretrigger,omitempty"`
	RiskScore        int    `json:"riskscore,omitempty"`
	RiskLabelScore   string `json:"risklabelscore,omitempty"`
	RiskLabel        string `json:"risklabel,omitempty"`
	IsBlacklist      int    `json:"isblacklist,omitempty"`
	IsWhitelist      int    `json:"iswhitelist,omitempty"`
	Extra            string `json:"extra,omitempty"`
	ClientIP         string `json:"clientip,omitempty"`
	RiskScoreScene   string `json:"riskscoreScene"`
}

// FromMapToScoreEvent bi上报时间从请求中的额外参数获取
func FromMapToScoreEvent(ctx context.Context, extraInfo map[string]string) (*ScoreEvent, error) {
	now := time.Now()
	var event *ScoreEvent

	loginRequestId := xnet.GetRequestId(xgin.FromContext(ctx).Request)
	if extraInfo != nil {
		event = &ScoreEvent{
			EventID:        time.Now().UnixNano(),
			DtEventTime:    now.Format(time.DateTime),
			EventTimeStamp: now.Unix(),
			LoginRequestID: loginRequestId,
			SessionID:      extraInfo["session_id"],
			DeviceID:       extraInfo["device_id"],
			DOID:           extraInfo["doid"],
			AppID:          extraInfo["appid"],
			VOpenID:        extraInfo["vopenID"],
			SystemType:     extraInfo["system_type"],
			SDKVersion:     extraInfo["sdk_version"],
			ClientVersion:  extraInfo["client_version"],
			PackageName:    extraInfo["package_name"],
			ClientIP:       extraInfo["client_ip"],
			PartEvent:      "RiskScore",
		}
	} else {
		event = &ScoreEvent{
			EventID:        time.Now().UnixNano(),
			DtEventTime:    now.Format(time.DateTime),
			EventTimeStamp: now.Unix(),
			LoginRequestID: loginRequestId,
			PartEvent:      "RiskScore",
		}
	}

	return event, nil
}

// ReportBI 上报BI
func ReportBI(ctx context.Context, event any) {
	log := xlog.FromContext(ctx)
	b, err := sonic.Marshal(event)
	if err != nil {
		log.Error("上报BI标签失败", xlog.Err(err))
		return
	}
	err = startup.GetBiKafka().WriteMessages(ctx, xkafka.Message{
		Value: b,
	})
	if err != nil {
		log.Error("上报BI Kafka失败", xlog.Err(err), xlog.String("msg", string(b)))
		return
	}
	log.Info("上报BI成功", xlog.String("msg", string(b)))
}

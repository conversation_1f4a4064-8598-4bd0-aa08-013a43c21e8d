package face

import (
	"context"
	"errors"
	cloudauth "github.com/alibabacloud-go/cloudauth-20190307/v3/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	conf "risk/config"
	"risk/constant"
)

var aliErrService = errors.New("500")

type <PERSON>yun struct {
}

func (a *<PERSON><PERSON>) GetCode(ctx context.Context, nid, orderId, name, idNo, extra, returnUrl, scene string) (*VerifyCode, error) {
	sceneId := conf.Get().Dark.Aliyun.FaceSceneId
	model := conf.Get().Dark.Aliyun.FaceVerifyModel
	if faceScene, ok := conf.Get().Dark.Aliyun.FaceSceneIds[scene]; scene != "" && ok {
		sceneId = faceScene.Id
		model = faceScene.Model
	}
	request := &cloudauth.InitFaceVerifyRequest{
		SceneId:      tea.Int64(sceneId),
		OuterOrderNo: tea.String(orderId),
		ProductCode:  tea.String("ID_PRO"),
		Model:        tea.String(model),
		CertType:     tea.String("IDENTITY_CARD"),
		CertName:     tea.String(name),
		CertNo:       tea.String(idNo),
		UserId:       tea.String(nid),
		ReturnUrl:    tea.String(returnUrl),
	}

	if rootNode, err := sonic.GetFromString(extra); err == nil {
		if metaInfo, _ := rootNode.Get("meta_info").Raw(); metaInfo != "" {
			request.MetaInfo = tea.String(metaInfo)
		}
		if uiCustomUrl, _ := rootNode.Get("ui_custom_url").String(); uiCustomUrl != "" {
			request.UiCustomUrl = tea.String(uiCustomUrl)
		}
	}

	for _, endpoint := range getEndpoints() {
		response, err := initFaceVerify(endpoint, request)
		if err != nil && errors.Is(err, aliErrService) {
			xlog.FromContext(ctx).Error("initFaceVerify error", xlog.Err(err))
			continue
		} else if err != nil {
			xlog.FromContext(ctx).Error("initFaceVerify error", xlog.Err(err))
			return nil, err
		}
		xlog.FromContext(ctx).Info("initFaceVerify success",
			xlog.Any("request", request),
			xlog.Any("response", response))
		if response == nil || response.Body == nil {
			xlog.FromContext(ctx).Error("initFaceVerify response is nil")
			return nil, errors.New("initFaceVerify response is nil")
		}

		if *response.Body.Code != "200" {
			return nil, ecode.Error(ecode.ServerError, *response.Body.Message)
		}

		certifyUrl := ""
		if response.Body.ResultObject == nil || response.Body.ResultObject.CertifyId == nil {
			xlog.FromContext(ctx).Error("initFaceVerify response is nil")
			return nil, errors.New("initFaceVerify response is nil")
		} else if returnUrl != "" && response.Body.ResultObject.CertifyUrl == nil {
			xlog.FromContext(ctx).Error("initFaceVerify response is nil")
			return nil, errors.New("initFaceVerify response  CertifyUrl is nil")
		}
		if response.Body.ResultObject.CertifyUrl != nil {
			certifyUrl = *response.Body.ResultObject.CertifyUrl
		}

		return &VerifyCode{
			Code:      *response.Body.ResultObject.CertifyId,
			ReturnUrl: certifyUrl,
		}, nil
	}
	return nil, nil
}

func (a *Aliyun) Verify(ctx context.Context, CertifyId, scene, extra string) (bool, error) {
	sceneId := conf.Get().Dark.Aliyun.FaceSceneId
	if faceScene, ok := conf.Get().Dark.Aliyun.FaceSceneIds[scene]; scene != "" && ok {
		sceneId = faceScene.Id
	}

	request := &cloudauth.DescribeFaceVerifyRequest{
		CertifyId: tea.String(CertifyId),
		SceneId:   tea.Int64(sceneId),
	}
	for _, endpoint := range getEndpoints() {
		response, err := describeFaceVerify(endpoint, request)
		if err != nil && errors.Is(err, aliErrService) {
			xlog.FromContext(ctx).Error("initFaceVerify error", xlog.Err(err))
			continue
		} else if err != nil {
			xlog.FromContext(ctx).Error("initFaceVerify error", xlog.Err(err))
			return false, err
		}

		xlog.FromContext(ctx).Info("initFaceVerify success",
			xlog.Any("request", request),
			xlog.Any("response", response))

		if response == nil || response.Body == nil {
			xlog.FromContext(ctx).Error("initFaceVerify response is nil")
			return false, errors.New("initFaceVerify response is nil")
		}

		if *response.Body.Code != "200" {
			return false, ecode.Error(ecode.ServerError, *response.Body.Message)
		}

		if response.Body.ResultObject == nil || response.Body.ResultObject.Passed == nil {
			xlog.FromContext(ctx).Error("initFaceVerify response  Body is nil")
			return false, errors.New("initFaceVerify response is nil")
		} else if *response.Body.ResultObject.SubCode == "206" {
			return false, constant.ErrFaceRisk
		}

		if *response.Body.ResultObject.Passed == "T" {
			return true, nil
		} else {
			return false, nil
		}
	}
	return false, nil
}

func CreateClient(endpoint *string) (_result *cloudauth.Client, _err error) {
	// 初始化Client。
	config := &openapi.Config{
		Endpoint:        endpoint,
		AccessKeyId:     tea.String(conf.Get().Dark.Aliyun.AppId),
		AccessKeySecret: tea.String(conf.Get().Dark.Aliyun.AppSecret),
	}
	_result, _err = cloudauth.NewClient(config)
	return _result, _err
}

func getEndpoints() []*string {
	endpoints := []*string{
		tea.String("cloudauth.cn-shanghai.aliyuncs.com"),
		tea.String("cloudauth.cn-beijing.aliyuncs.com"),
	}
	return endpoints
}

func getRuntime() *util.RuntimeOptions {
	// 创建RuntimeObject实例并设置运行参数。
	runtime := &util.RuntimeOptions{}
	runtime.ReadTimeout = tea.Int(5000)
	runtime.ConnectTimeout = tea.Int(5000)
	return runtime
}

func initFaceVerify(endpoint *string, request *cloudauth.InitFaceVerifyRequest) (*cloudauth.InitFaceVerifyResponse, error) {
	client, err := CreateClient(endpoint)
	if err != nil {
		return nil, err
	}
	body, err := client.InitFaceVerifyWithOptions(request, getRuntime())
	if err != nil {
		return nil, err
	}
	if body == nil {
		return nil, errors.New("no response")
	}
	_, err = isSuccess(*body.StatusCode, *body.Body.Message)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func describeFaceVerify(endpoint *string, request *cloudauth.DescribeFaceVerifyRequest) (*cloudauth.DescribeFaceVerifyResponse, error) {
	client, err := CreateClient(endpoint)
	if err != nil {
		return nil, err
	}
	body, err := client.DescribeFaceVerifyWithOptions(request, getRuntime())
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, errors.New("no response")
	}

	_, err = isSuccess(*body.StatusCode, *body.Body.Message)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func isSuccess(code int32, msg string) (bool, error) {
	if code == 500 {
		return false, aliErrService
	} else if code != 200 {
		return false, errors.New(msg)
	}
	return true, nil
}

package repo

import (
	"context"
	"fmt"
	ltPB "gitlab.papegames.com/biz/protobuf/risk/livetags"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"risk/config"
	"risk/utils"
	"risk/xmetric"
	"time"
)

var liveTagsConn *grpc.ClientConn

func NewLiveTagsConn() error {
	addr := fmt.Sprintf("%s:%d", config.Get().GRPCStrategy.LiveTagsUri.Host, config.Get().GRPCStrategy.LiveTagsUri.Port)
	var err error
	liveTagsConn, err = grpc.NewClient(addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithIdleTimeout(time.Duration(config.Get().GRPCStrategy.IdleTimeout)*time.Second),
	)
	return err
}

type LiveTags struct {
}

func NewLiveTags() *LiveTags {
	return &LiveTags{}

}

func (i *LiveTags) getConn() (*grpc.ClientConn, error) {
	return liveTagsConn, nil
	//addr := fmt.Sprintf("%s:%d", config.Get().GRPCStrategy.LiveTagsUri.Host, config.Get().GRPCStrategy.LiveTagsUri.Port)
	//return grpc.NewClient(addr,
	//	grpc.WithTransportCredentials(insecure.NewCredentials()),
	//	grpc.WithIdleTimeout(time.Duration(config.Get().GRPCStrategy.IdleTimeout)*time.Second),
	//)
}

// GetScore 获取计分，带暴力破解标签
func (i *LiveTags) GetScore(ctx context.Context, request *ltPB.GetScoreRequest) (*ltPB.GetScoreResponse, error) {
	conn, err := i.getConn()
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "get_score"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "livetags", path, request.ModelCode)
	}()

	client := ltPB.NewLivetagsServiceClient(conn)
	return client.GetScore(utils.GrpcSetContext(ctx), request)
}

// Report 暴力破解和验证通过上报
func (i *LiveTags) Report(ctx context.Context, request *ltPB.ReportRequest) error {
	conn, err := i.getConn()
	if err != nil {
		return err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "report"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "livetags", path, request.ModelCode)
	}()

	client := ltPB.NewLivetagsServiceClient(conn)
	_, err = client.Report(utils.GrpcSetContext(ctx), request)
	return err
}

// ScoreQuery 游戏和B端查询
func (i *LiveTags) ScoreQuery(ctx context.Context, request *ltPB.ScoreRequest) (*ltPB.ScoreResponse, error) {
	conn, err := i.getConn()
	if err != nil {
		return nil, err
	}
	begin := time.Now()
	defer func() {
		//conn.Close()
		var (
			path    = "score_query"
			latency = float64(time.Since(begin).Milliseconds())
		)
		xmetric.GrpcMetricRequest.Observe(latency, "livetags", path, request.ModelCode)
	}()
	client := ltPB.NewLivetagsServiceClient(conn)
	return client.ScoreQuery(utils.GrpcSetContext(ctx), request)
}

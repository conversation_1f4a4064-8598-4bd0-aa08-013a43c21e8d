package device

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"go.uber.org/zap"
	"risk/config"
	"risk/proto"
	"risk/utils"
	"risk/utils/shumei"
	"strconv"
	"time"
)

var Headers map[string]string

type Shumei struct {
}

func (s Shumei) GetDeviceId(ctx context.Context, body string) (*proto.ThirdDeviceData, error) {
	resp, err := s.done(ctx, body, config.Get().DeviceProvider.IshumeiProvider.DeviceProfileUri)
	if err != nil {
		return nil, err
	}

	var respData proto.DeviceGetResponseData
	err = sonic.Unmarshal(resp, &respData)
	if err != nil {
		return nil, err
	}

	if respData.Code != 1100 {
		return nil, errors.New("shumei code:" + strconv.Itoa(int(respData.Code)) + "; 1901:QPS 超限,1902:smsdk 参数不合法,1903:服务失败,9101:无权限操作")
	}

	deviceId, err := s.OutputBoxId(fmt.Sprintf("B%s", respData.Detail.DeviceId))
	if err != nil {
		return nil, err
	}
	return &proto.ThirdDeviceData{ThirdDeviceId: deviceId, ThirdData: respData.Detail.DeviceId}, nil

}

func (Shumei) DecryptBody(ctx context.Context, os, body string) (*proto.DeviceInfo, error) {
	ainfoKey := "bwGHghNiIXZgcmcEVXSxBtxupxnBRwgKZZqWDcfbPHQUOIkLSRwsiPTwFTYeXisO"
	deviceInfo, err := shumei.Decrypt(body, os, ainfoKey)
	if err != nil {
		return nil, err
	}

	var shumeiResponseData proto.ShumeiDeviceResponseData
	err = sonic.Unmarshal([]byte(deviceInfo), &shumeiResponseData)
	if err != nil {
		return nil, err
	}

	var sdkDeviceId utils.SdkDeviceId
	copier.Copy(&sdkDeviceId, &shumeiResponseData.ExtraInfo)
	deviceId, err := sdkDeviceId.GetDeviceId()
	if err != nil {
		return nil, errors.New("sdkDeviceId get error:" + err.Error())
	}

	return &proto.DeviceInfo{
		DeviceId:  deviceId,
		ThirdInfo: deviceInfo,
	}, nil
}

func (s Shumei) GetConfig(ctx context.Context, body string) (string, error) {
	resp, err := s.done(ctx, body, config.Get().DeviceProvider.IshumeiProvider.CloudConfUri)
	if err != nil {
		return "", err
	}
	return string(resp), nil
}

func (Shumei) done(ctx context.Context, body, uri string) ([]byte, error) {
	client := xresty.New()
	client.SetDebug(true)
	client.SetTimeout(3 * time.Second)

	var Headers map[string]string
	//https://help.ishumei.com/docs/tw/sdk/tool/developDoc/
	//Headers["Content-Type"] = "application/octet-stream"
	resp, err := client.NewRequest().
		SetContext(ctx).
		SetHeaders(Headers).
		SetBody(body).
		Post(uri)

	xlog.FromContext(ctx).Info("shumei http:", zap.String("body", body), zap.Any("headers", Headers), zap.String("resp", string(resp.Body())))
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != 200 {
		return nil, errors.New("shumei http code:" + resp.Status())
	}
	return resp.Body(), nil
}

func (s Shumei) OutputBoxId(boxId string) (string, error) {
	deviceId, err := shumei.OutputBoxId(boxId)
	return deviceId, err
}

func (s Shumei) GetRisk(ctx context.Context, body string) (*proto.ShumeiRiskResponse, error) {
	resp, err := s.done(ctx, body, config.Get().Dark.Ishumei.Uri)
	if err != nil {
		return nil, err
	}

	var respData proto.ShumeiRiskResponse
	err = sonic.Unmarshal(resp, &respData)
	if err != nil {
		return nil, err
	}

	if respData.Code != 1100 {
		return nil, errors.New("shumei code:" + strconv.Itoa(int(respData.Code)) + "; 1901:QPS 超限,1902:smsdk 参数不合法,1903:服务失败,9101:无权限操作")
	}

	return &respData, nil
}

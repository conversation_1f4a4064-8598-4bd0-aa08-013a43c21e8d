package device

import (
	"context"
	"risk/proto"
)

type Device interface {
	GetDeviceId(context.Context, string) (*proto.ThirdDeviceData, error)
	DecryptBody(context.Context, string, string) (*proto.DeviceInfo, error)
	GetConfig(context.Context, string) (string, error)
	GetRisk(context.Context, string) (*proto.ShumeiRiskResponse, error)
}

func NewDevice(provider string) Device {
	switch provider {
	case "shumei":
		return &Shumei{}
	}
	return nil
}

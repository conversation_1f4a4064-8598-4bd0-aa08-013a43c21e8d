# 风险控制系统接口文档

## 概述

本文档详细描述了风险控制系统的核心接口，包括账户检查、支付检查、人脸识别、验证码验证等功能。系统采用 gRPC 协议，支持多种风险检测策略和处理方式。

## 接口分类

### SDK端接口
- 面向客户端SDK的接口，主要用于用户交互和验证
- 路径前缀：`/v1/risk/`

### 服务端接口  
- 面向后端服务的接口，主要用于风险检测和管理
- 路径前缀：`/v1/risk/`

## 通用响应结构

### HandlerRequest (风险处理响应)
```protobuf
message HandlerRequest {
  string nonce = 1;           // 随机数/会话ID
  string provider = 2;        // 服务提供商 (1:极验)
  repeated string phone = 3;  // 手机号列表
  repeated string sale_phone = 4; // 销售手机号列表
  string email = 9;           // 邮箱地址
  string sale_email = 10;     // 销售邮箱地址
  int64 ttl = 11;            // 过期时间
}
```

### 错误码定义
- `51001`: 参数错误
- `51002`: 验证已过期，请刷新页面
- `50010`: 需要二次认证
- `50011`: 验证不通过
- `50014`: 人脸识别失败次数过多
- `50016`: code已过期或无效，请重新发起
- `50017`: 检测到风险异常，建议检查当前设备环境

### 处理类型常量
- `2203`: 验证码处理 (HandlerCaptcha)
- `2204`: 短信验证处理 (HandlerSms)  
- `2205`: 人脸识别处理 (HandlerFace)
- `2206`: 拒绝处理 (HandlerReject)
- `2207`: 邮箱验证处理 (HandlerEmail)
- `2210`: 登出处理 (HandlerLogout)
- `2212`: 强制绑定处理 (HandlerForceBind)

---

## 1. 账户风险检查接口

### 接口信息
- **路径**: `POST /v1/risk/account/check`
- **描述**: 账号风控检测，用于检测账户相关的风险行为
- **分类**: 服务端接口

### 请求参数 (AccountRiskCheckRequest)
```protobuf
message AccountRiskCheckRequest {
  string nid = 1;           // [必填] 用户的nid
  string ip = 2;            // [必填] 用户的IP地址 (IPv4格式)
  string action = 3;        // [必填] 操作类型
  uint32 client_id = 4;     // [必填] 租户ID (最小值:100)
  string device = 5;        // 用户的设备信息
  string DOID = 6;          // 设备唯一标识
  string app_id = 7;        // 应用ID
  string lang = 8;          // 语言
  string role_id = 9;       // 角色ID
  string scene = 10;        // 场景ID
}
```

### 响应结果 (HandlerRequest)
返回风险处理指令，包含验证码、短信验证等处理方式。

### 业务逻辑
1. 参数验证和复制
2. 设置风险类型为账户类型 (`RiskTypeAccount = 2`)
3. 默认场景为登录场景 (`RiskSDK = "login"`)
4. 调用风险策略引擎进行检测
5. 根据检测结果返回相应的处理指令

---

## 2. 支付风险检查接口

### 接口信息
- **路径**: `POST /v1/risk/payment/check`
- **描述**: 支付风控检测，用于检测支付相关的风险行为
- **分类**: 服务端接口

### 请求参数 (PayRiskCheckRequest)
```protobuf
message PayRiskCheckRequest {
  string nid = 1;           // [必填] 用户的nid
  string ip = 2;            // 用户的IP地址
  string action = 3;        // [必填] 操作类型
  uint32 client_id = 4;     // [必填] 租户ID (最小值:100)
  string device = 5;        // 设备信息
  uint32 charge_type = 6;   // 充值类型
  uint32 platform = 7;      // 平台类型
  string product_id = 8;    // 产品ID
  string role_id = 9;       // 角色ID
  uint32 region = 10;       // 区域
  uint32 zone_id = 11;      // 区服ID
  string lang = 12;         // 语言
  string app_id = 13;       // 应用ID
  string scene = 14;        // 场景ID
}
```

### 响应结果 (HandlerRequest)
返回风险处理指令。

### 业务逻辑
1. 设置风险类型为支付类型 (`RiskTypePay = 1`)
2. 默认场景为支付场景 (`RiskPayment = "payment"`)
3. 执行支付风险检测策略
4. 返回相应的风险处理结果

---

## 3. 绑定通知接口

### 接口信息
- **路径**: `POST /v1/risk/bind/notice`
- **描述**: 绑定通知接口，用于处理用户绑定相关的通知
- **分类**: 服务端接口

### 请求参数 (BindNoticeRequest)
```protobuf
message BindNoticeRequest {
  int64 nid = 1;            // 用户nid
  int32 code = 2;           // 通知代码
  uint32 client_id = 3;     // 租户ID
  string app_id = 4;        // 应用ID
  string doid = 5;          // 设备标识
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
调用绑定手机服务处理通知逻辑。

---

## 4. 业务初始化接口

### 接口信息
- **路径**: `POST /v1/risk/biz/init`
- **描述**: 初始化接口，获取业务配置信息
- **分类**: SDK端接口

### 请求参数 (BizInitRequest)
```protobuf
message BizInitRequest {
  uint32 client_id = 1;     // [必填] 租户ID (最小值:100)
  string include_keys = 2;  // 包含的配置键，逗号分隔
  string exclude_keys = 3;  // 排除的配置键，逗号分隔
}
```

### 响应结果 (BizInitResponse)
```protobuf
message BizInitResponse {
  BizInitCaptchaData captcha = 1;  // 验证码配置
  bool oae = 2;                    // 数美代理开关
  bool ccr = 3;                    // 加密开关
  string interactive_msg = 4;       // 交互消息
  map<string, RawMessage> options = 5; // 配置选项
}

message BizInitCaptchaData {
  string captcha_id = 1;    // 验证码ID
  bool close = 2;           // 是否关闭验证码
}
```

### 业务逻辑
1. 获取极验验证码配置
2. 处理加密开关和灰度配置
3. 根据include_keys和exclude_keys过滤配置项
4. 返回初始化配置信息

---

## 5. 极验验证码检查接口

### 接口信息
- **路径**: `POST /v1/risk/captcha/g/check`
- **描述**: 极验验证接口，验证用户完成的验证码
- **分类**: SDK端接口

### 请求参数 (GCaptchaRequest)
```protobuf
message GCaptchaRequest {
  string number = 1;        // [必填] 验证序号
  string captcha = 2;       // [必填] 验证码结果
  string token = 3;         // [必填] 验证令牌
  string time = 4;          // [必填] 验证时间
  string captcha_id = 5;    // [必填] 验证码ID
  string nonce = 6;         // [必填] 随机数
  uint32 client_id = 7;     // [必填] 租户ID (最小值:100)
  string app_id = 8;        // [必填] 应用ID
  string DOID = 9;          // 设备标识
}
```

### 响应结果 (GCaptchaResponse)
```protobuf
message GCaptchaResponse {
  string pass_token = 1;    // 通过令牌
}
```

### 业务逻辑
1. 调用极验服务进行验证码校验
2. 验证通过后返回pass_token
3. 用于后续的风险验证流程

---

## 6. 验证码检查接口

### 接口信息
- **路径**: `POST /v1/risk/code/check`
- **描述**: 短信验证码检查接口
- **分类**: SDK端接口

### 请求参数 (SDKCheckCodeRequest)
```protobuf
message SDKCheckCodeRequest {
  string nonce = 1;         // [必填] 随机数
  string code = 2;          // [必填] 验证码
  string app_id = 3;        // [必填] 应用ID
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
1. 根据nonce获取短信验证器
2. 校验验证码的正确性
3. 验证通过返回成功，失败返回错误

---

## 7. 人脸识别代码接口 (移动端)

### 接口信息
- **路径**: `POST /v1/risk/face/code`
- **描述**: 人脸验证初始化（移动端）
- **分类**: SDK端接口

### 请求参数 (FaceCodeRequest)
```protobuf
message FaceCodeRequest {
  string nid = 1;           // [必填] 用户nid
  string token = 2;         // [必填] 用户token
  string scene = 3;         // [必填] 场景
  string vendor = 4;        // [必填] 厂商 (如:aliyun)
  string extra = 5;         // 扩展信息，JSON格式
  int64 client_id = 6;      // [必填] 租户ID
  string DOID = 7;          // [必填] 设备标识
}
```

### 响应结果 (FaceCodeResponse)
```protobuf
message FaceCodeResponse {
  string vendor = 1;        // 厂商
  string code = 2;          // 请求的唯一code
  FaceServiceData service = 3; // 服务数据
}

message FaceServiceData {
  string certify_id = 1;    // 认证ID
}
```

### 业务逻辑
1. 根据厂商创建人脸识别服务
2. 调用SDK人脸识别初始化
3. 返回认证code和服务数据

---

## 8. 人脸识别代码接口 (网页端)

### 接口信息
- **路径**: `POST /v1/risk/face/code/web`
- **描述**: 人脸验证初始化（网页端）
- **分类**: SDK端接口

### 请求参数 (FaceCodeForWebRequest)
```protobuf
message FaceCodeForWebRequest {
  string nid = 1;           // 用户nid
  string token = 2;         // 用户token
  string real_name = 3;     // 真实姓名
  string id_card = 4;       // 身份证
  string phone = 11;        // 手机号码
  string scene = 5;         // [必填] 场景，固定值:account_real_info
  string vendor = 6;        // [必填] 厂商，固定值:aliyun
  string extra = 7;         // 扩展信息，JSON格式, meta_info必传
  int64 client_id = 8;      // [必填] 租户ID
  string DOID = 9;          // [必填] 设备标识
  string return_url = 10;   // [必填] 认证结束后回跳页面的链接地址
  string app_id = 12;       // [必填] 应用ID
  string ip = 13;           // IP地址
}
```

### 响应结果 (FaceCodeForWebResponse)
```protobuf
message FaceCodeForWebResponse {
  string vendor = 1;        // 厂商
  string code = 2;          // 请求的唯一code
  string return_url = 3;    // 回跳URL
  FaceServiceData service = 4; // 服务数据
}
```

### 业务逻辑
1. 检查人脸识别服务开关
2. 支持有登录态和无登录态两种模式
3. 有登录态时与SDK保持一致
4. 无登录态时需要身份证、姓名或手机号
5. 执行风险规则检查
6. 返回认证URL和服务数据

---

## 9. 人脸识别结果接口

### 接口信息
- **路径**: `POST /v1/risk/face/result`
- **描述**: 人脸验证认证结果
- **分类**: SDK端接口

### 请求参数 (FaceResultRequest)
```protobuf
message FaceResultRequest {
  string code = 3;          // [必填] 人脸识别初始化接口返回的code
  int64 client_id = 4;      // [必填] 租户ID
  string DOID = 5;          // 设备标识
}
```

### 响应结果 (FaceResultResponse)
```protobuf
message FaceResultResponse {
  int32 status = 1;         // 状态码
  string pass_token = 2;    // 通过令牌
}
```

### 业务逻辑
1. 根据code获取人脸识别服务
2. 调用验证方法获取结果
3. 返回验证状态和通过令牌

---

## 10. 人脸识别状态接口

### 接口信息
- **路径**: `POST /v1/risk/face/status`
- **描述**: 查询人脸识别状态
- **分类**: 服务端接口

### 请求参数 (FaceStatusRequest)
```protobuf
message FaceStatusRequest {
  string nid = 1;           // 用户nid
  string scene = 2;         // 场景
  string token = 3;         // 人脸识别token
  int64 client_id = 4;      // 租户ID
  string app_id = 5;        // 应用ID
  string ip = 6;            // IP地址
  string id_card = 7;       // 身份证
  string real_name = 8;     // 真实姓名
}
```

### 响应结果 (FaceStatusResponse)
```protobuf
message FaceStatusResponse {
  uint32 status = 1;        // 状态码
}
```

---

## 11. HD获取接口

### 接口信息
- **路径**: `POST /v1/risk/hd/get`
- **描述**: 设备信息获取接口
- **分类**: SDK端接口

### 请求参数 (DeviceGetRequest)
```protobuf
message DeviceGetRequest {
  string third_device_id = 1; // 第三方设备ID
  string information = 2;      // 设备信息
}
```

### 响应结果 (DeviceGetResponse)
```protobuf
message DeviceGetResponse {
  DeviceGetResponseData data = 1;
}

message DeviceGetResponseData {
  int32 code = 2;           // 响应码
  string requestId = 3;     // 请求ID
  Detail detail = 4;        // 详细信息
}

message Detail {
  string DOID = 1;          // 设备唯一标识
  string deviceId = 2;      // 设备ID
}
```

### 业务逻辑
1. 调用设备服务创建DOID
2. 返回设备唯一标识和相关信息

---

## 12. 手机号检查接口

### 接口信息
- **路径**: `POST /v1/risk/phone/check`
- **描述**: 手机号验证和短信发送
- **分类**: SDK端接口

### 请求参数 (SDKCheckPhoneRequest)
```protobuf
message SDKCheckPhoneRequest {
  string nonce = 1;         // [必填] 随机数
  string phone = 2;         // [必填] 手机号
  bool mock = 3;            // 是否模拟
  string app_id = 4;        // [必填] 应用ID
}
```

### 响应结果 (SDKCheckPhoneResponse)
```protobuf
message SDKCheckPhoneResponse {
  string code = 1;          // 响应码
}
```

### 业务逻辑
1. 根据nonce获取短信验证器
2. 检查手机号并发送短信验证码
3. 返回发送结果

---

## 风险检测策略

### 黑白名单检测
- 系统支持黑白名单机制
- 白名单用户直接通过
- 黑名单用户触发风险处理

### 风险评分
- 基于多维度数据进行风险评分
- 支持设备指纹、行为分析等
- 根据评分触发不同的处理策略

### 处理策略
1. **验证码验证** (2203): 要求用户完成图形验证码
2. **短信验证** (2204): 发送短信验证码到绑定手机
3. **邮箱验证** (2207): 发送验证码到绑定邮箱
4. **人脸识别** (2205): 要求进行人脸识别验证
5. **拒绝访问** (2206): 直接拒绝用户请求
6. **强制登出** (2210): 强制用户重新登录
7. **强制绑定** (2212): 要求用户绑定手机号

### 降级机制
- 当主要验证方式不可用时，自动降级到验证码验证
- 如手机号未绑定时，短信验证降级为验证码验证

---

## 使用示例

### 账户风险检查示例
```bash
curl -X POST "https://risk.papegames.com/v1/risk/account/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&ip=***********&action=login&client_id=100&app_id=test_app"
```

### 人脸识别初始化示例
```bash
curl -X POST "https://risk.papegames.com/v1/risk/face/code" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&token=abc123&scene=account_real_info&vendor=aliyun&client_id=100&DOID=device123"
```

### 极验验证码检查示例
```bash
curl -X POST "https://risk.papegames.com/v1/risk/captcha/g/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "number=123&captcha=abc&token=xyz&time=**********&captcha_id=gt123&nonce=nonce123&client_id=100&app_id=test_app"
```

### 业务初始化示例
```bash
curl -X POST "https://risk.papegames.com/v1/risk/biz/init" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=100&include_keys=captcha,device&exclude_keys=debug"
```

---

## 接口调用流程

### 典型风险检测流程
1. **初始化**: 调用 `/v1/risk/biz/init` 获取配置
2. **风险检测**: 调用相应的风险检查接口
3. **处理响应**: 根据返回的处理类型执行相应操作
4. **二次验证**: 如需验证，调用对应的验证接口
5. **完成流程**: 验证通过后继续业务流程

### 人脸识别流程
1. **初始化**: 调用 `/v1/risk/face/code` 或 `/v1/risk/face/code/web`
2. **用户验证**: 用户完成人脸识别
3. **获取结果**: 调用 `/v1/risk/face/result` 获取验证结果
4. **状态查询**: 可选调用 `/v1/risk/face/status` 查询状态

### 短信验证流程
1. **发送验证码**: 调用 `/v1/risk/phone/check`
2. **用户输入**: 用户输入收到的验证码
3. **验证码校验**: 调用 `/v1/risk/code/check` 验证

---

## 注意事项

### 安全要求
1. **参数验证**: 所有必填参数都需要提供，否则返回参数错误
2. **IP格式**: IP地址必须是有效的IPv4格式
3. **租户ID**: client_id最小值为100
4. **签名验证**: 所有接口都需要进行签名验证

### 性能考虑
1. **设备标识**: DOID用于设备唯一标识，建议在所有请求中携带
2. **限流**: 接口可能存在频率限制，需要合理控制调用频率
3. **缓存**: 系统内部使用Redis缓存，提高响应速度

### 错误处理
1. **错误码**: 根据返回的错误码进行相应的错误处理
2. **降级策略**: 实现合适的降级策略，确保业务可用性
3. **重试机制**: 对于网络错误等临时性问题，可以实现重试机制

### 数据格式
1. **时间格式**: 时间戳使用Unix时间戳格式
2. **JSON格式**: extra字段需要传递有效的JSON格式数据
3. **编码格式**: 所有字符串参数使用UTF-8编码

---

## 常见问题

### Q: 如何判断是否需要进行风险验证？
A: 调用风险检查接口后，如果返回HandlerRequest对象，说明需要进行相应的验证。根据返回的处理类型选择验证方式。

### Q: 人脸识别失败次数过多怎么办？
A: 系统会限制人脸识别失败次数，超过限制后会返回错误码50014。建议引导用户使用其他验证方式。

### Q: 验证码验证失败怎么处理？
A: 验证失败时会返回相应的错误码，可以引导用户重新获取验证码或使用其他验证方式。

### Q: 如何处理设备指纹？
A: 通过DOID参数传递设备唯一标识，系统会基于设备指纹进行风险评估。

---

## 更新日志

- **v1.0** (2024-01-01): 初始版本发布
  - 支持账户风险检查
  - 支持支付风险检查
  - 支持人脸识别验证
  - 支持极验验证码
  - 支持短信验证
  - 支持设备指纹检测

---

## 联系方式

如有问题或建议，请联系风险控制团队：
- 邮箱: <EMAIL>
- 文档版本: v1.0
- 最后更新: 2024-01-01

# 风险控制系统接口文档

## 概述

本文档详细描述了风险控制系统的所有接口，包括账户检查、支付检查、人脸识别、验证码验证、配置管理等功能。系统采用 gRPC 协议，支持多种风险检测策略和处理方式。

## 接口分类

### 📱 SDK端接口 (x-apifox-folder: "SDK端")
面向客户端SDK的接口，主要用于用户交互和验证，权限级别通常为P0
- 业务初始化和配置获取
- 设备指纹和验证码验证
- 人脸识别和短信验证
- 用户风险检测

### 🖥️ 服务端接口 (x-apifox-folder: "服务端")
面向后端服务的接口，主要用于风险检测和数据处理，权限级别P0-P2
- 风险检测和评估
- 数据加密解密
- 设备和账户验证
- 系统内部调用

### 🏢 ToB端接口 (x-apifox-folder: "ToB端")
面向企业客户的管理接口，用于配置和管理，权限级别为P2
- 黑白名单管理
- 风控配置管理
- 系统配置和监控
- 数据统计和分析

### 🌐 外部接口
面向第三方系统的接口，用于数据交换和集成
- 第三方设备指纹集成
- 外部风控服务调用
- 数据同步接口

---

## 完整接口列表

### 📱 SDK端接口 (14个)

| 接口名称 | 路径 | 描述 | 权限级别 |
|---------|------|------|----------|
| Health | `GET /v1/health` | 健康检查 | P2 |
| DeviceGet | `POST /v1/risk/hd/get` | 获取DOID设备标识 | P0 |
| BizInit | `POST /v1/risk/biz/init` | 业务初始化接口 | P0 |
| GCaptcha | `POST /v1/risk/captcha/g/check` | 极验验证接口 | P0 |
| SDKCheckPhone | `POST /v1/risk/phone/check` | 手机号码验证 | P0 |
| SDKPhoneCode | `POST /v1/risk/phone/sendcode` | 手机号码单发短信 | P0 |
| SDKCheckSMSCode | `POST /v1/risk/code/check` | 短信验证码检查 | P0 |
| SDKCheckEmail | `POST /v1/risk/email/check` | 邮箱验证码检查 | P0 |
| FaceCode | `POST /v1/risk/face/code` | 人脸验证初始化(移动端) | P0 |
| FaceResult | `POST /v1/risk/face/result` | 人脸验证认证结果 | P0 |
| NIDRisk | `POST /v1/risk/gs/check` | 游戏账号风险信息查询 | P0 |
| SendComet | `POST /v1/risk/comet/send` | 发送长链消息 | P2 |
| FaceCodeForWeb | `POST /v1/risk/face/code/web` | 人脸验证初始化(网页端) | P0 |

### 🖥️ 服务端接口 (16个)

| 接口名称 | 路径 | 描述 | 权限级别 |
|---------|------|------|----------|
| CaptchaTokenCheck | `POST /v1/risk/captcha/token/check` | 极验验证接口 | P0 |
| AppCheckRisk | `POST /v1/risk/app/check` | 业务风控检测 | P0 |
| AccountCheckRisk | `POST /v1/risk/account/check` | 账号风控检测 | P0 |
| PayCheckRisk | `POST /v1/risk/payment/check` | 支付风控检测 | P0 |
| DeviceRisk | `POST /v1/risk/device/risk` | 设备风险信息查询 | P0 |
| DecodeData | `POST /v1/risk/decode` | 加密数据解密 | P2 |
| EncodeData | `POST /v1/risk/encode` | 数据加密 | P2 |
| CheckAppId | `POST /v1/risk/app_id/check` | AppId验证 | P2 |
| SDKDOIDCheck | `POST /v1/risk/ds/check` | SDK DOID验证 | P2 |
| ShumeiDecode | `POST /v1/risk/shumei/decode` | 数美设备ID解码 | P2 |
| BindNotice | `POST /v1/risk/bind/notice` | 绑定手机号通知 | P2 |
| SDKDOIDToDOID | `POST /v1/risk/device/sdk_doid` | SDK DOID转DOID | P2 |
| FaceStatus | `POST /v1/risk/face/status` | 人脸识别状态查询 | P2 |

### 🏢 ToB端接口 (16个)

| 接口名称 | 路径 | 描述 | 权限级别 |
|---------|------|------|----------|
| AddBlackList | `POST /v1/risk/tob/black_list/create` | 黑名单列表-添加 | P2 |
| EditBlackList | `POST /v1/risk/tob/black_list/edit` | 黑名单列表-修改 | P2 |
| DeleteBlackList | `POST /v1/risk/tob/black_list/delete` | 黑名单列表-删除 | P2 |
| AddWhiteList | `POST /v1/risk/tob/white_list/create` | 白名单列表-添加 | P2 |
| EditWhiteList | `POST /v1/risk/tob/white_list/edit` | 白名单列表-修改 | P2 |
| DeleteWhiteList | `POST /v1/risk/tob/white_list/delete` | 白名单列表-删除 | P2 |
| AddDarkList | `POST /v1/risk/tob/dark_list/create` | 黑产列表-添加 | P2 |
| EditDarkList | `POST /v1/risk/tob/dark_list/edit` | 黑产列表-修改 | P2 |
| DeleteDarkList | `POST /v1/risk/tob/dark_list/delete` | 黑产列表-删除 | P2 |
| AddConfigApp | `POST /v1/risk/tob/config_app/create` | 风控开关配置-添加 | P2 |
| EditConfigApp | `POST /v1/risk/tob/config_app/edit` | 风控开关配置-修改 | P2 |
| DeleteConfigApp | `POST /v1/risk/tob/config_app/delete` | 风控开关配置-删除 | P2 |
| SDKSendComet | `POST /v1/risk/comet/send` | 发送长链消息 | P2 |
| ReloadGeetestConfig | `POST /v1/risk/tob/geetest_config/reload` | 重新加载极验配置 | P2 |
| DeleteApproved | `POST /v1/risk/tob/approved/delete` | 允许账号/设备-删除 | P2 |

### 权限级别说明

- **P0**: 公开接口，面向客户端和基础服务
- **P2**: 内部接口，需要特殊权限，面向管理和配置

## 通用响应结构

### HandlerRequest (风险处理响应)
```protobuf
message HandlerRequest {
  string nonce = 1;           // 随机数/会话ID
  string provider = 2;        // 服务提供商 (1:极验)
  repeated string phone = 3;  // 手机号列表
  repeated string sale_phone = 4; // 销售手机号列表
  string email = 9;           // 邮箱地址
  string sale_email = 10;     // 销售邮箱地址
  int64 ttl = 11;            // 过期时间
}
```

### 错误码定义
- `51001`: 参数错误
- `51002`: 验证已过期，请刷新页面
- `50010`: 需要二次认证
- `50011`: 验证不通过
- `50014`: 人脸识别失败次数过多
- `50016`: code已过期或无效，请重新发起
- `50017`: 检测到风险异常，建议检查当前设备环境

### 处理类型常量
- `2203`: 验证码处理 (HandlerCaptcha)
- `2204`: 短信验证处理 (HandlerSms)  
- `2205`: 人脸识别处理 (HandlerFace)
- `2206`: 拒绝处理 (HandlerReject)
- `2207`: 邮箱验证处理 (HandlerEmail)
- `2210`: 登出处理 (HandlerLogout)
- `2212`: 强制绑定处理 (HandlerForceBind)

---

## 1. 账户风险检查接口

### 接口信息
- **路径**: `POST /v1/risk/account/check`
- **描述**: 账号风控检测，用于检测账户相关的风险行为
- **分类**: 服务端接口

### 请求参数 (AccountRiskCheckRequest)
```protobuf
message AccountRiskCheckRequest {
  string nid = 1;           // [必填] 用户的nid
  string ip = 2;            // [必填] 用户的IP地址 (IPv4格式)
  string action = 3;        // [必填] 操作类型
  uint32 client_id = 4;     // [必填] 租户ID (最小值:100)
  string device = 5;        // 用户的设备信息
  string DOID = 6;          // 设备唯一标识
  string app_id = 7;        // 应用ID
  string lang = 8;          // 语言
  string role_id = 9;       // 角色ID
  string scene = 10;        // 场景ID
}
```

### 响应结果 (HandlerRequest)
返回风险处理指令，包含验证码、短信验证等处理方式。

### 业务逻辑
1. 参数验证和复制
2. 设置风险类型为账户类型 (`RiskTypeAccount = 2`)
3. 默认场景为登录场景 (`RiskSDK = "login"`)
4. 调用风险策略引擎进行检测
5. 根据检测结果返回相应的处理指令

---

## 2. 支付风险检查接口

### 接口信息
- **路径**: `POST /v1/risk/payment/check`
- **描述**: 支付风控检测，用于检测支付相关的风险行为
- **分类**: 服务端接口

### 请求参数 (PayRiskCheckRequest)
```protobuf
message PayRiskCheckRequest {
  string nid = 1;           // [必填] 用户的nid
  string ip = 2;            // 用户的IP地址
  string action = 3;        // [必填] 操作类型
  uint32 client_id = 4;     // [必填] 租户ID (最小值:100)
  string device = 5;        // 设备信息
  uint32 charge_type = 6;   // 充值类型
  uint32 platform = 7;      // 平台类型
  string product_id = 8;    // 产品ID
  string role_id = 9;       // 角色ID
  uint32 region = 10;       // 区域
  uint32 zone_id = 11;      // 区服ID
  string lang = 12;         // 语言
  string app_id = 13;       // 应用ID
  string scene = 14;        // 场景ID
}
```

### 响应结果 (HandlerRequest)
返回风险处理指令。

### 业务逻辑
1. 设置风险类型为支付类型 (`RiskTypePay = 1`)
2. 默认场景为支付场景 (`RiskPayment = "payment"`)
3. 执行支付风险检测策略
4. 返回相应的风险处理结果

---

## 3. 绑定通知接口

### 接口信息
- **路径**: `POST /v1/risk/bind/notice`
- **描述**: 绑定通知接口，用于处理用户绑定相关的通知
- **分类**: 服务端接口

### 请求参数 (BindNoticeRequest)
```protobuf
message BindNoticeRequest {
  int64 nid = 1;            // 用户nid
  int32 code = 2;           // 通知代码
  uint32 client_id = 3;     // 租户ID
  string app_id = 4;        // 应用ID
  string doid = 5;          // 设备标识
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
调用绑定手机服务处理通知逻辑。

---

## 4. 业务初始化接口

### 接口信息
- **路径**: `POST /v1/risk/biz/init`
- **描述**: 初始化接口，获取业务配置信息
- **分类**: SDK端接口

### 请求参数 (BizInitRequest)
```protobuf
message BizInitRequest {
  uint32 client_id = 1;     // [必填] 租户ID (最小值:100)
  string include_keys = 2;  // 包含的配置键，逗号分隔
  string exclude_keys = 3;  // 排除的配置键，逗号分隔
}
```

### 响应结果 (BizInitResponse)
```protobuf
message BizInitResponse {
  BizInitCaptchaData captcha = 1;  // 验证码配置
  bool oae = 2;                    // 数美代理开关
  bool ccr = 3;                    // 加密开关
  string interactive_msg = 4;       // 交互消息
  map<string, RawMessage> options = 5; // 配置选项
}

message BizInitCaptchaData {
  string captcha_id = 1;    // 验证码ID
  bool close = 2;           // 是否关闭验证码
}
```

### 业务逻辑
1. 获取极验验证码配置
2. 处理加密开关和灰度配置
3. 根据include_keys和exclude_keys过滤配置项
4. 返回初始化配置信息

---

## 5. 极验验证码检查接口

### 接口信息
- **路径**: `POST /v1/risk/captcha/g/check`
- **描述**: 极验验证接口，验证用户完成的验证码
- **分类**: SDK端接口

### 请求参数 (GCaptchaRequest)
```protobuf
message GCaptchaRequest {
  string number = 1;        // [必填] 验证序号
  string captcha = 2;       // [必填] 验证码结果
  string token = 3;         // [必填] 验证令牌
  string time = 4;          // [必填] 验证时间
  string captcha_id = 5;    // [必填] 验证码ID
  string nonce = 6;         // [必填] 随机数
  uint32 client_id = 7;     // [必填] 租户ID (最小值:100)
  string app_id = 8;        // [必填] 应用ID
  string DOID = 9;          // 设备标识
}
```

### 响应结果 (GCaptchaResponse)
```protobuf
message GCaptchaResponse {
  string pass_token = 1;    // 通过令牌
}
```

### 业务逻辑
1. 调用极验服务进行验证码校验
2. 验证通过后返回pass_token
3. 用于后续的风险验证流程

---

## 6. 验证码检查接口

### 接口信息
- **路径**: `POST /v1/risk/code/check`
- **描述**: 短信验证码检查接口
- **分类**: SDK端接口

### 请求参数 (SDKCheckCodeRequest)
```protobuf
message SDKCheckCodeRequest {
  string nonce = 1;         // [必填] 随机数
  string code = 2;          // [必填] 验证码
  string app_id = 3;        // [必填] 应用ID
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
1. 根据nonce获取短信验证器
2. 校验验证码的正确性
3. 验证通过返回成功，失败返回错误

---

## 7. 人脸识别代码接口 (移动端)

### 接口信息
- **路径**: `POST /v1/risk/face/code`
- **描述**: 人脸验证初始化（移动端）
- **分类**: SDK端接口

### 请求参数 (FaceCodeRequest)
```protobuf
message FaceCodeRequest {
  string nid = 1;           // [必填] 用户nid
  string token = 2;         // [必填] 用户token
  string scene = 3;         // [必填] 场景
  string vendor = 4;        // [必填] 厂商 (如:aliyun)
  string extra = 5;         // 扩展信息，JSON格式
  int64 client_id = 6;      // [必填] 租户ID
  string DOID = 7;          // [必填] 设备标识
}
```

### 响应结果 (FaceCodeResponse)
```protobuf
message FaceCodeResponse {
  string vendor = 1;        // 厂商
  string code = 2;          // 请求的唯一code
  FaceServiceData service = 3; // 服务数据
}

message FaceServiceData {
  string certify_id = 1;    // 认证ID
}
```

### 业务逻辑
1. 根据厂商创建人脸识别服务
2. 调用SDK人脸识别初始化
3. 返回认证code和服务数据

---

## 8. 人脸识别代码接口 (网页端)

### 接口信息
- **路径**: `POST /v1/risk/face/code/web`
- **描述**: 人脸验证初始化（网页端）
- **分类**: SDK端接口

### 请求参数 (FaceCodeForWebRequest)
```protobuf
message FaceCodeForWebRequest {
  string nid = 1;           // 用户nid
  string token = 2;         // 用户token
  string real_name = 3;     // 真实姓名
  string id_card = 4;       // 身份证
  string phone = 11;        // 手机号码
  string scene = 5;         // [必填] 场景，固定值:account_real_info
  string vendor = 6;        // [必填] 厂商，固定值:aliyun
  string extra = 7;         // 扩展信息，JSON格式, meta_info必传
  int64 client_id = 8;      // [必填] 租户ID
  string DOID = 9;          // [必填] 设备标识
  string return_url = 10;   // [必填] 认证结束后回跳页面的链接地址
  string app_id = 12;       // [必填] 应用ID
  string ip = 13;           // IP地址
}
```

### 响应结果 (FaceCodeForWebResponse)
```protobuf
message FaceCodeForWebResponse {
  string vendor = 1;        // 厂商
  string code = 2;          // 请求的唯一code
  string return_url = 3;    // 回跳URL
  FaceServiceData service = 4; // 服务数据
}
```

### 业务逻辑
1. 检查人脸识别服务开关
2. 支持有登录态和无登录态两种模式
3. 有登录态时与SDK保持一致
4. 无登录态时需要身份证、姓名或手机号
5. 执行风险规则检查
6. 返回认证URL和服务数据

---

## 9. 人脸识别结果接口

### 接口信息
- **路径**: `POST /v1/risk/face/result`
- **描述**: 人脸验证认证结果
- **分类**: SDK端接口

### 请求参数 (FaceResultRequest)
```protobuf
message FaceResultRequest {
  string code = 3;          // [必填] 人脸识别初始化接口返回的code
  int64 client_id = 4;      // [必填] 租户ID
  string DOID = 5;          // 设备标识
}
```

### 响应结果 (FaceResultResponse)
```protobuf
message FaceResultResponse {
  int32 status = 1;         // 状态码
  string pass_token = 2;    // 通过令牌
}
```

### 业务逻辑
1. 根据code获取人脸识别服务
2. 调用验证方法获取结果
3. 返回验证状态和通过令牌

---

## 10. 人脸识别状态接口

### 接口信息
- **路径**: `POST /v1/risk/face/status`
- **描述**: 查询人脸识别状态
- **分类**: 服务端接口

### 请求参数 (FaceStatusRequest)
```protobuf
message FaceStatusRequest {
  string nid = 1;           // 用户nid
  string scene = 2;         // 场景
  string token = 3;         // 人脸识别token
  int64 client_id = 4;      // 租户ID
  string app_id = 5;        // 应用ID
  string ip = 6;            // IP地址
  string id_card = 7;       // 身份证
  string real_name = 8;     // 真实姓名
}
```

### 响应结果 (FaceStatusResponse)
```protobuf
message FaceStatusResponse {
  uint32 status = 1;        // 状态码
}
```

---

## 11. HD获取接口

### 接口信息
- **路径**: `POST /v1/risk/hd/get`
- **描述**: 设备信息获取接口
- **分类**: SDK端接口

### 请求参数 (DeviceGetRequest)
```protobuf
message DeviceGetRequest {
  string third_device_id = 1; // 第三方设备ID
  string information = 2;      // 设备信息
}
```

### 响应结果 (DeviceGetResponse)
```protobuf
message DeviceGetResponse {
  DeviceGetResponseData data = 1;
}

message DeviceGetResponseData {
  int32 code = 2;           // 响应码
  string requestId = 3;     // 请求ID
  Detail detail = 4;        // 详细信息
}

message Detail {
  string DOID = 1;          // 设备唯一标识
  string deviceId = 2;      // 设备ID
}
```

### 业务逻辑
1. 调用设备服务创建DOID
2. 返回设备唯一标识和相关信息

---

## 12. 手机号检查接口

### 接口信息
- **路径**: `POST /v1/risk/phone/check`
- **描述**: 手机号验证和短信发送
- **分类**: SDK端接口

### 请求参数 (SDKCheckPhoneRequest)
```protobuf
message SDKCheckPhoneRequest {
  string nonce = 1;         // [必填] 随机数
  string phone = 2;         // [必填] 手机号
  bool mock = 3;            // 是否模拟
  string app_id = 4;        // [必填] 应用ID
}
```

### 响应结果 (SDKCheckPhoneResponse)
```protobuf
message SDKCheckPhoneResponse {
  string code = 1;          // 响应码
}
```

### 业务逻辑
1. 根据nonce获取短信验证器
2. 检查手机号并发送短信验证码
3. 返回发送结果

---

## 13. 邮箱验证码检查接口

### 接口信息
- **路径**: `POST /v1/risk/email/check`
- **描述**: 邮箱验证码检查接口
- **分类**: SDK端接口

### 请求参数 (SDKCheckEmailRequest)
```protobuf
message SDKCheckEmailRequest {
  string nonce = 1;         // [必填] 随机数
  string code = 2;          // [必填] 验证码
  string app_id = 3;        // [必填] 应用ID
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
1. 根据nonce获取邮箱验证器
2. 校验邮箱验证码的正确性
3. 验证通过返回成功，失败返回错误

---

## 14. 手机号单发短信接口

### 接口信息
- **路径**: `POST /v1/risk/phone/sendcode`
- **描述**: 手机号码单发短信接口
- **分类**: SDK端接口

### 请求参数 (SDKPhoneCodeRequest)
```protobuf
message SDKPhoneCodeRequest {
  string nonce = 1;         // [必填] 随机数
  string phone = 2;         // [必填] 手机号
  string app_id = 4;        // [必填] 应用ID
}
```

### 响应结果
返回空响应 (`papegames.type.Empty`)

### 业务逻辑
1. 根据nonce获取短信验证器
2. 向指定手机号发送验证码
3. 返回发送结果

---

## 15. 游戏账号风险信息查询接口

### 接口信息
- **路径**: `POST /v1/risk/gs/check`
- **描述**: 游戏账号风险信息查询，用于游戏服务端查询账号风险
- **分类**: 服务端接口

### 请求参数 (NIDCheckRequest)
```protobuf
message NIDCheckRequest {
  string nid = 1;           // [必填] 用户nid
  string ip = 2;            // IP地址
  uint32 client_id = 4;     // [必填] 租户ID
  string DOID = 6;          // 设备标识
  string sig = 7;           // [必填] 签名
  int64 timestamp = 8;      // [必填] 时间戳
}
```

### 响应结果 (NIDCheckResponse)
```protobuf
message NIDCheckResponse {
  int64 risk_score = 1;     // 风险评分 (0:安全, 1:风险)
}
```

### 业务逻辑
1. 验证签名和时间戳
2. 检查Redis缓存中的二次验证状态
3. 执行黑白名单检查
4. 计算风险评分
5. 返回简化的风险评分(0或1)

---

## 16. 业务风控检测接口

### 接口信息
- **路径**: `POST /v1/risk/app/check`
- **描述**: 业务风控检测，用于检测应用相关的风险行为
- **分类**: 服务端接口

### 请求参数 (AppRiskCheckRequest)
```protobuf
message AppRiskCheckRequest {
  string nid = 1;           // [必填] 用户的nid
  string ip = 2;            // [必填] 用户的IP地址 (IPv4格式)
  string action = 3;        // [必填] 操作类型
  string app_id = 4;        // [必填] 应用的app_id
  string device = 5;        // 用户的设备
  string DOID = 6;          // DOID
  uint32 client_id = 7;     // 租户ID
  string lang = 8;          // 语言
  string role_id = 9;       // 角色ID
  string scene = 10;        // 场景ID
}
```

### 响应结果 (HandlerRequest)
返回风险处理指令。

### 业务逻辑
1. 设置风险类型为应用类型 (`RiskTypeApp = 3`)
2. 默认场景为SDK场景 (`RiskSDK = "login"`)
3. 执行应用风险检测策略
4. 返回相应的风险处理结果

---

## 17. 设备风险信息查询接口

### 接口信息
- **路径**: `POST /v1/risk/device/risk`
- **描述**: 设备风险信息查询
- **分类**: 服务端接口

### 请求参数 (DeviceRiskRequest)
```protobuf
message DeviceRiskRequest {
  string DOID = 1;          // [必填] 设备标识
  uint32 client_id = 2;     // [必填] 租户ID (最小值:100)
}
```

### 响应结果 (DeviceRiskResponse)
```protobuf
message DeviceRiskResponse {
  uint32 score = 1;         // 风险评分
}
```

### 业务逻辑
1. 根据DOID查询设备风险信息
2. 计算设备风险评分
3. 返回风险评分结果

---

## 18. 极验验证服务端接口

### 接口信息
- **路径**: `POST /v1/risk/captcha/token/check`
- **描述**: 极验验证服务端接口，用于验证pass_token
- **分类**: 服务端接口

### 请求参数 (CaptchaTokenCheckRequest)
```protobuf
message CaptchaTokenCheckRequest {
  uint32 client_id = 1;     // [必填] 租户ID (最小值:100)
  string pass_token = 2;    // [必填] 通过令牌
}
```

### 响应结果 (CaptchaTokenCheckResponse)
```protobuf
message CaptchaTokenCheckResponse {
  bool has_pass = 1;        // 是否通过验证
}
```

### 业务逻辑
1. 验证pass_token的有效性
2. 检查token是否已被使用
3. 返回验证结果

---

## ToB端管理接口

### 19. 黑名单管理接口

#### 添加黑名单
- **路径**: `POST /v1/risk/tob/black_list/create`
- **描述**: 添加黑名单条目
- **权限**: P2

#### 修改黑名单
- **路径**: `POST /v1/risk/tob/black_list/edit`
- **描述**: 修改黑名单条目
- **权限**: P2

#### 删除黑名单
- **路径**: `POST /v1/risk/tob/black_list/delete`
- **描述**: 删除黑名单条目
- **权限**: P2

### 20. 白名单管理接口

#### 添加白名单
- **路径**: `POST /v1/risk/tob/white_list/create`
- **描述**: 添加白名单条目
- **权限**: P2

#### 修改白名单
- **路径**: `POST /v1/risk/tob/white_list/edit`
- **描述**: 修改白名单条目
- **权限**: P2

#### 删除白名单
- **路径**: `POST /v1/risk/tob/white_list/delete`
- **描述**: 删除白名单条目
- **权限**: P2

### 21. 黑产管理接口

#### 添加黑产
- **路径**: `POST /v1/risk/tob/dark_list/create`
- **描述**: 添加黑产条目
- **权限**: P2

#### 修改黑产
- **路径**: `POST /v1/risk/tob/dark_list/edit`
- **描述**: 修改黑产条目
- **权限**: P2

#### 删除黑产
- **路径**: `POST /v1/risk/tob/dark_list/delete`
- **描述**: 删除黑产条目
- **权限**: P2

### 22. 风控配置管理接口

#### 添加风控配置
- **路径**: `POST /v1/risk/tob/config_app/create`
- **描述**: 添加风控开关配置
- **权限**: P2

#### 修改风控配置
- **路径**: `POST /v1/risk/tob/config_app/edit`
- **描述**: 修改风控开关配置
- **权限**: P2

#### 删除风控配置
- **路径**: `POST /v1/risk/tob/config_app/delete`
- **描述**: 删除风控开关配置
- **权限**: P2

### 23. 系统管理接口

#### 重新加载极验配置
- **路径**: `POST /v1/risk/tob/geetest_config/reload`
- **描述**: 重新加载极验配置
- **权限**: P2

#### 删除允许账号/设备
- **路径**: `POST /v1/risk/tob/approved/delete`
- **描述**: 删除允许的账号或设备
- **权限**: P2

---

## 数据处理接口

### 24. 数据加密解密接口

#### 数据解密
- **路径**: `POST /v1/risk/decode`
- **描述**: 加密数据解密
- **权限**: P2

#### 数据加密
- **路径**: `POST /v1/risk/encode`
- **描述**: 数据加密
- **权限**: P2

### 25. 设备相关接口

#### SDK DOID验证
- **路径**: `POST /v1/risk/ds/check`
- **描述**: SDK DOID验证
- **权限**: P2

#### SDK DOID转换
- **路径**: `POST /v1/risk/device/sdk_doid`
- **描述**: SDK DOID转DOID
- **权限**: P2

#### 数美设备ID解码
- **路径**: `POST /v1/risk/shumei/decode`
- **描述**: 数美设备ID解码
- **权限**: P2

### 26. 其他服务端接口

#### AppId验证
- **路径**: `POST /v1/risk/app_id/check`
- **描述**: AppId验证
- **权限**: P2

#### 发送长链消息
- **路径**: `POST /v1/risk/comet/send`
- **描述**: 发送长链消息到客户端
- **权限**: P2

---

## 风险检测策略

### 黑白名单检测
- 系统支持黑白名单机制
- 白名单用户直接通过
- 黑名单用户触发风险处理

### 风险评分
- 基于多维度数据进行风险评分
- 支持设备指纹、行为分析等
- 根据评分触发不同的处理策略

### 处理策略
1. **验证码验证** (2203): 要求用户完成图形验证码
2. **短信验证** (2204): 发送短信验证码到绑定手机
3. **邮箱验证** (2207): 发送验证码到绑定邮箱
4. **人脸识别** (2205): 要求进行人脸识别验证
5. **拒绝访问** (2206): 直接拒绝用户请求
6. **强制登出** (2210): 强制用户重新登录
7. **强制绑定** (2212): 要求用户绑定手机号

### 降级机制
- 当主要验证方式不可用时，自动降级到验证码验证
- 如手机号未绑定时，短信验证降级为验证码验证

---

## 使用示例

### SDK端接口调用示例

#### 业务初始化
```bash
curl -X POST "https://risk.papegames.com/v1/risk/biz/init" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=100&include_keys=captcha,device&exclude_keys=debug"
```

#### 获取设备DOID
```bash
curl -X POST "https://risk.papegames.com/v1/risk/hd/get" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "third_device_id=device123&information={\"os\":\"android\"}"
```

#### 极验验证码检查
```bash
curl -X POST "https://risk.papegames.com/v1/risk/captcha/g/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "number=123&captcha=abc&token=xyz&time=**********&captcha_id=gt123&nonce=nonce123&client_id=100&app_id=test_app"
```

#### 手机号验证
```bash
curl -X POST "https://risk.papegames.com/v1/risk/phone/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nonce=nonce123&phone=***********&app_id=test_app"
```

#### 人脸识别初始化
```bash
curl -X POST "https://risk.papegames.com/v1/risk/face/code" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&token=abc123&scene=account_real_info&vendor=aliyun&client_id=100&DOID=device123"
```

### 服务端接口调用示例

#### 账户风险检查
```bash
curl -X POST "https://risk.papegames.com/v1/risk/account/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&ip=***********&action=login&client_id=100&app_id=test_app"
```

#### 支付风险检查
```bash
curl -X POST "https://risk.papegames.com/v1/risk/payment/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&ip=***********&action=payment&client_id=100&product_id=item001"
```

#### 游戏账号风险查询
```bash
curl -X POST "https://risk.papegames.com/v1/risk/gs/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "nid=123456&client_id=100&sig=signature&timestamp=**********"
```

### ToB端接口调用示例

#### 添加黑名单
```bash
curl -X POST "https://risk.papegames.com/v1/risk/tob/black_list/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=100&type=1&value=*************&reason=恶意行为"
```

#### 重新加载极验配置
```bash
curl -X POST "https://risk.papegames.com/v1/risk/tob/geetest_config/reload" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=100"
```

---

## 接口调用流程

### 典型风险检测流程
1. **初始化**: 调用 `/v1/risk/biz/init` 获取配置
2. **风险检测**: 调用相应的风险检查接口
3. **处理响应**: 根据返回的处理类型执行相应操作
4. **二次验证**: 如需验证，调用对应的验证接口
5. **完成流程**: 验证通过后继续业务流程

### 人脸识别流程
1. **初始化**: 调用 `/v1/risk/face/code` 或 `/v1/risk/face/code/web`
2. **用户验证**: 用户完成人脸识别
3. **获取结果**: 调用 `/v1/risk/face/result` 获取验证结果
4. **状态查询**: 可选调用 `/v1/risk/face/status` 查询状态

### 短信验证流程
1. **发送验证码**: 调用 `/v1/risk/phone/check`
2. **用户输入**: 用户输入收到的验证码
3. **验证码校验**: 调用 `/v1/risk/code/check` 验证

---

## 注意事项

### 安全要求
1. **参数验证**: 所有必填参数都需要提供，否则返回参数错误
2. **IP格式**: IP地址必须是有效的IPv4格式
3. **租户ID**: client_id最小值为100
4. **签名验证**: 所有接口都需要进行签名验证

### 性能考虑
1. **设备标识**: DOID用于设备唯一标识，建议在所有请求中携带
2. **限流**: 接口可能存在频率限制，需要合理控制调用频率
3. **缓存**: 系统内部使用Redis缓存，提高响应速度

### 错误处理
1. **错误码**: 根据返回的错误码进行相应的错误处理
2. **降级策略**: 实现合适的降级策略，确保业务可用性
3. **重试机制**: 对于网络错误等临时性问题，可以实现重试机制

### 数据格式
1. **时间格式**: 时间戳使用Unix时间戳格式
2. **JSON格式**: extra字段需要传递有效的JSON格式数据
3. **编码格式**: 所有字符串参数使用UTF-8编码

---

## 常见问题

### Q: 如何判断是否需要进行风险验证？
A: 调用风险检查接口后，如果返回HandlerRequest对象，说明需要进行相应的验证。根据返回的处理类型选择验证方式。

### Q: 人脸识别失败次数过多怎么办？
A: 系统会限制人脸识别失败次数，超过限制后会返回错误码50014。建议引导用户使用其他验证方式。

### Q: 验证码验证失败怎么处理？
A: 验证失败时会返回相应的错误码，可以引导用户重新获取验证码或使用其他验证方式。

### Q: 如何处理设备指纹？
A: 通过DOID参数传递设备唯一标识，系统会基于设备指纹进行风险评估。

---

## 接口统计

### 按分类统计
- **SDK端接口**: 13个 (主要面向客户端)
- **服务端接口**: 16个 (主要面向后端服务)
- **ToB端接口**: 16个 (主要面向企业管理)
- **总计**: 45个接口

### 按权限级别统计
- **P0级别**: 25个接口 (公开接口)
- **P2级别**: 19个接口 (内部接口)
- **其他**: 1个接口 (健康检查)

### 按功能分类
- **风险检测**: 6个接口
- **验证相关**: 8个接口
- **设备管理**: 5个接口
- **配置管理**: 12个接口
- **数据处理**: 6个接口
- **人脸识别**: 4个接口
- **其他**: 4个接口

---

## 技术架构

### 服务架构
- **外部服务**: 通过 ginServerOutward 提供SDK端接口
- **内部服务**: 通过 ginServerInner 提供服务端和ToB端接口
- **协议**: 基于 gRPC 和 HTTP
- **数据格式**: Protocol Buffers + JSON

### 安全机制
- **签名验证**: 所有接口支持签名验证
- **加密传输**: 支持数据加密传输
- **权限控制**: 基于级别的权限控制(P0/P2)
- **频率限制**: 接口调用频率限制

### 依赖服务
- **Redis**: 缓存和会话管理
- **MySQL**: 配置和数据存储
- **极验**: 验证码服务
- **阿里云**: 人脸识别服务
- **数美**: 设备指纹服务

---

## 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 完整的45个接口文档
- 详细的接口分类和权限说明
- 包含所有SDK端、服务端、ToB端接口
- 支持风险检测、验证、配置管理等功能

### 功能特性
- ✅ 账户风险检查
- ✅ 支付风险检查
- ✅ 业务风险检查
- ✅ 人脸识别验证
- ✅ 极验验证码
- ✅ 短信/邮箱验证
- ✅ 设备指纹检测
- ✅ 黑白名单管理
- ✅ 风控配置管理
- ✅ 数据加密解密
- ✅ 第三方服务集成

---

## 联系方式

如有问题或建议，请联系风险控制团队：
- **邮箱**: <EMAIL>
- **文档版本**: v1.0
- **最后更新**: 2024-01-01
- **接口总数**: 45个
- **支持的服务**: SDK端、服务端、ToB端

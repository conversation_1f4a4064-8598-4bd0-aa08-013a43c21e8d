# 风险控制系统第三方依赖文档

## 概述

本文档详细梳理了风险控制系统的所有对外依赖，包括第三方服务、内部服务、基础设施组件等。系统采用微服务架构，依赖多个外部服务来实现完整的风险控制功能。

---

## 🌐 第三方服务依赖

### 1. 阿里云服务

#### 1.1 人脸识别服务 (CloudAuth)
- **服务商**: 阿里云
- **用途**: 实名认证、人脸识别验证
- **接入方式**: REST API
- **SDK版本**: `github.com/alibabacloud-go/cloudauth-20190307/v3 v3.9.2`
- **配置项**:
  ```properties
  dark.aliyun.is_open = 1
  dark.aliyun.app_id = LTAI5tSLGGuVxSQW7RyV6htE
  dark.aliyun.app_secret = ******************************
  dark.aliyun.face_scene_id = 1000003029
  dark.aliyun.face_verify_model = PHOTINUS_LIVENESS
  ```
- **主要API**:
  - `InitFaceVerify`: 初始化人脸识别
  - `DescribeFaceVerify`: 查询人脸识别结果
- **依赖文件**: `repo/face/aliyun.go`

#### 1.2 设备风险检测服务
- **用途**: IP风险检测、设备风险评估
- **配置项**:
  ```properties
  dark.aliyun.ip_risk.risk_source = 80
  dark.aliyun.device_risk.tags = rw_0101,rw_0103,rw_0104,rw_0108,is_openVpn,time_over,token_invalid,token_replay,token_tampered
  ```

#### 1.3 链路追踪服务
- **服务**: 阿里云链路追踪
- **配置项**:
  ```properties
  sparrow.trace.enable = false
  sparrow.trace.endpoint = tracing-analysis-dc-hz.aliyuncs.com:8090
  sparrow.trace.headers.authentication = fj6bj4wuyv@cffa970a60fc7ef_fj6bj4wuyv@53df7ad2afe8301
  ```

### 2. 数美科技 (Ishumei)

#### 2.1 设备指纹服务
- **服务商**: 数美科技
- **用途**: 设备指纹识别、设备风险检测
- **接入方式**: HTTP API
- **配置项**:
  ```properties
  dark.ishumei.is_open = 1
  dark.ishumei.uri = http://api-tianxiang-bj.fengkongcloud.com/tianxiang/v4
  dark.ishumei.access_key = 1tbxeHYLikYsIsk519KG
  device_provider.ishumei.device_profile_uri = http://fp-proxy.fengkongcloud.com/deviceprofile/v4
  device_provider.ishumei.cloud_conf_uri = http://fp-proxy.fengkongcloud.com/v3/cloudconf
  device_provider.ishumei.agency = true
  ```
- **主要功能**:
  - 设备指纹生成和识别
  - 设备风险评分
  - 黑产设备检测
- **依赖文件**: `repo/device/shumei.go`

### 3. 极验验证码 (Geetest)

#### 3.1 验证码服务
- **服务商**: 极验
- **用途**: 图形验证码、行为验证
- **接入方式**: HTTP API
- **配置项**:
  ```properties
  machine.geetest.api_uri = http://gcaptcha4.geetest.com/validate
  machine.geetest.captcha_id = 6aea8087471f762316431f24d83a142c
  machine.geetest.captcha_key = a27b9d75447d91ca370a12a738b94362
  machine.geetest.disable_check_ip = true
  machine.geetest.disable_check_risk_device = true
  ```
- **主要功能**:
  - 图形验证码验证
  - 行为验证分析
  - 风险设备检测
- **依赖文件**: `repo/captcha/geetest.go`

---

## 🏢 内部服务依赖

### 1. Pigeon 服务

#### 1.1 用户服务
- **用途**: 用户信息查询、实名信息获取、Token验证
- **配置项**:
  ```properties
  pigeon.gmhost = http://api-dev.papegames.com:12102
  pigeon.host = https://api-dev.papegames.com:12101
  ```
- **主要API**:
  - `/v1/getrealinfo`: 获取用户实名信息
  - `/v1/getsimpleprofile`: 获取用户基本信息
  - `/v1/chktoken`: Token验证
- **依赖文件**: `rpc/user.go`

#### 1.2 短信服务
- **用途**: 短信验证码发送
- **配置项**:
  ```properties
  sms.is_open = true
  ```
- **主要API**:
  - `/v1/phonecode`: 发送短信验证码
  - `/v1/checkphonecode`: 验证短信验证码
- **依赖文件**: `rpc/sms.go`

#### 1.3 邮件服务
- **用途**: 邮件验证码发送
- **配置项**:
  ```properties
  email.is_open = true
  ```
- **主要API**:
  - `/v1/emailcode`: 发送邮件验证码
  - `/v1/checkemailcode`: 验证邮件验证码
- **依赖文件**: `rpc/email.go`

### 2. gRPC 内部服务

#### 2.1 风险评分引擎
- **服务名**: risk-score-engine
- **用途**: 风险评分计算
- **配置项**:
  ```properties
  grpc_strategy.score_uri.host = risk-score-engine
  grpc_strategy.score_uri.port = 8092
  grpc_strategy.timeout = 100
  grpc_strategy.idle_timeout = 30
  ```

#### 2.2 风险评估引擎
- **服务名**: risk-evaluation-engine
- **用途**: 风险评估和决策
- **配置项**:
  ```properties
  grpc_strategy.evaluation_uri.host = risk-evaluation-engine
  grpc_strategy.evaluation_uri.port = 8092
  ```

#### 2.3 实时标签服务
- **服务名**: livetags
- **用途**: 用户行为标签管理
- **配置项**:
  ```properties
  grpc_strategy.live_tags_uri.host = livetags
  grpc_strategy.live_tags_uri.port = 8090
  ```

#### 2.4 广告黑名单服务
- **服务名**: risk-ad-black
- **用途**: 广告相关黑名单检测
- **配置项**:
  ```properties
  grpc_strategy.ad_black_list_uri.host = risk-ad-black
  grpc_strategy.ad_black_list_uri.port = 8090
  ```

### 3. Comet 长连接服务

#### 3.1 实时消息推送
- **服务名**: comet-graph
- **用途**: 实时消息推送、长连接管理
- **配置项**:
  ```properties
  comet.url = comet-graph-test.papegames.com:8080
  ```
- **主要功能**:
  - 风险检测结果实时推送
  - 客户端状态同步
- **依赖文件**: `rpc/comet.go`

---

## 🗄️ 基础设施依赖

### 1. 数据库服务

#### 1.1 MySQL 数据库
- **用途**: 主要数据存储
- **配置项**:
  ```properties
  sparrow.database.mysql.dataSource = _vault(encrypted_connection_string)_
  sparrow.database.mysql.maxIdleConns = 5
  sparrow.database.mysql.maxOpenConns = 10
  ```
- **主要数据库**:
  - 主库: 风控配置、黑白名单、用户数据
  - verify库: 应用验证数据
  - naccessories库: 辅助配置数据

#### 1.2 Redis 缓存
- **用途**: 缓存、会话管理、限流控制
- **配置项**:
  ```properties
  sparrow.database.redis.addr = 10.10.186.18:6379
  cache.type = memory
  cache.cap = 10000000
  cache.doid_expire = 15552000
  ```
- **主要用途**:
  - 验证码缓存
  - 人脸识别状态缓存
  - 设备指纹缓存
  - 风险检测结果缓存

### 2. 消息队列服务

#### 2.1 Kafka 集群
- **用途**: 异步消息处理、数据同步
- **配置项**:
  ```properties
  # BI数据上报
  sparrow.broker.kafka.bi.writer.Brokers = alikafka-pre-cn-nwy3i48px00a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-3-vpc.alikafka.aliyuncs.com:9092
  sparrow.broker.kafka.bi.writer.Topic = tlog_json_risk_gameqos_test
  
  # 风险数据处理
  sparrow.broker.kafka.risk.writer.Brokers = 10.10.186.19:9092
  sparrow.broker.kafka.risk.writer.Topic = risk-doid
  sparrow.broker.kafka.risk.reader.Brokers = 10.10.186.19:9092
  sparrow.broker.kafka.risk.reader.Topic = risk-doid
  sparrow.broker.kafka.risk.reader.GroupID = risk_service
  ```

### 3. 服务发现与配置

#### 3.1 etcd 集群
- **用途**: 服务注册发现、配置管理
- **配置项**:
  ```properties
  sparrow.registrar.prefix = /sparrow/sd/
  sparrow.registrar.addrs = [127.0.0.1:2379]
  sparrow.watch.config.endpoints = 10.149.17.213:2379
  ```

#### 3.2 Apollo 配置中心
- **用途**: 动态配置管理
- **配置项**:
  ```yaml
  sparrow:
    configure:
      provider: "apollo"
      watch: true
      path: "application"
      config:
        endpoints:
          - "http://10.149.52.104:8080"
        appID: "pt-risk"
        cluster: "default"
        secret: "12c2b0bae4f847938a18164220d28af4"
  ```

---

## 📊 监控与观测

### 1. 链路追踪
- **服务**: 阿里云链路追踪
- **SDK**: OpenTelemetry
- **依赖包**: `go.opentelemetry.io/otel v1.24.0`

### 2. 指标监控
- **服务**: Prometheus
- **依赖包**: `github.com/prometheus/client_golang v1.19.1`

### 3. 日志收集
- **框架**: Zap
- **依赖包**: `go.uber.org/zap v1.27.0`

---

## 🔧 开发工具依赖

### 1. 代码生成工具
- **Protocol Buffers**: `google.golang.org/protobuf v1.34.2`
- **gRPC**: `google.golang.org/grpc v1.64.1`
- **内部工具**: `gitlab.papegames.com/fringe/protoc-gen-gotag v1.0.1`

### 2. 序列化工具
- **JSON**: `github.com/bytedance/sonic v1.13.2`
- **其他**: `github.com/jinzhu/copier v0.4.0`

### 3. ID生成工具
- **UUID**: `github.com/google/uuid v1.6.0`
- **NanoID**: `github.com/matoous/go-nanoid/v2 v2.0.0`

---

## 🚨 依赖风险评估

### 高风险依赖
1. **阿里云人脸识别**: 核心业务功能，单点故障风险
2. **数美设备指纹**: 设备识别核心，影响风控准确性
3. **MySQL数据库**: 数据持久化，需要高可用保障

### 中风险依赖
1. **极验验证码**: 有降级机制，可容忍短时故障
2. **Redis缓存**: 有内存缓存降级，影响性能但不影响功能
3. **内部gRPC服务**: 有超时和重试机制

### 低风险依赖
1. **Kafka消息队列**: 异步处理，可容忍延迟
2. **监控服务**: 不影响核心业务功能

---

## 📋 依赖管理建议

### 1. 版本管理
- 定期更新依赖版本，关注安全漏洞
- 使用语义化版本控制
- 建立依赖升级测试流程

### 2. 故障处理
- 实现熔断和降级机制
- 建立依赖服务监控告警
- 制定应急预案和故障恢复流程

### 3. 安全管控
- 定期安全扫描和漏洞检测
- 敏感配置加密存储
- 网络访问控制和防火墙规则

---

## 📞 联系方式

如有依赖相关问题，请联系：
- **技术负责人**: 风险控制团队
- **邮箱**: <EMAIL>
- **文档版本**: v1.0
- **最后更新**: 2024-01-01

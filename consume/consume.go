package consume

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/segmentio/kafka-go"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/errgroup"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/repo/device"
	"risk/startup"
	"risk/utils"
	"sync"
)

var (
	runnerBuf = make(chan *kafka.Message, 100)
)

type DOIDConsumer struct {
	logger *xlog.Logger
}

func NewDOIDConsumer() *DOIDConsumer {
	return &DOIDConsumer{
		logger: xlog.FromContext(context.Background()).With(xlog.String("key", "kafka")),
	}
}

func (c DOIDConsumer) Kafka(ctx context.Context) {
	if config.Get().ConsumerDisable {
		c.logger.Info("kafka consumer disabled")
		return
	}
	wg := sync.WaitGroup{}
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		c.WorkerRun()
	})
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		c.Run(ctx)
	})
	wg.Wait()
}

func (c DOIDConsumer) Run(ctx context.Context) {
	reader := startup.GetRiskKafkaReader()
	c.logger.Info("kafka consumer start")
	for {
		select {
		case <-ctx.Done():
			c.logger.Info("kafka consumer exit, ctx done")
			close(runnerBuf)
			return
		default:
			m, err := reader.ReadMessage(ctx)
			if err != nil {
				c.logger.Error("read message failed", xlog.Err(err))
				close(runnerBuf)
				return
			}
			xlog.Info("read message", xlog.Any("message", m))
			runnerBuf <- &m
		}
	}
}

func (c DOIDConsumer) WorkerRun() {
	group := new(errgroup.Group)
	num := config.Get().WorkerNum
	if num == 0 {
		num = 2
	}
	for i := 0; i < num; i++ {
		group.Go(func() error {
			for m := range runnerBuf {
				ictx := utils.KafkaCtx(context.Background(), m)
				err := c.HandelMessage(ictx, m)
				if err != nil {
					c.logger.Error("handel message failed", xlog.Err(err))
				}
			}
			c.logger.Info("worker exit")
			return nil
		})
	}
	group.Wait()
}

func (c DOIDConsumer) HandelMessage(ctx context.Context, msg *kafka.Message) error {
	c.logger.Info("handel message msg",
		zap.Any("msg", msg),
		zap.String("msg value", string(msg.Value)))
	if len(msg.Headers) > 0 && msg.Headers[0].Key == constant.KafkaSdkDOIDKey {
		if err := c.insertSDKDOID(ctx, msg.Value); err != nil {
			c.logger.Error("insertSdkDOID error", zap.Error(err))
			return err
		}
		return nil
	} else if err := c.insertDB(ctx, msg.Value); err != nil {
		c.logger.Error("insertDB error", zap.Error(err))
		return err
	}
	return nil
}

func (c DOIDConsumer) insertDB(ctx context.Context, msg []byte) error {
	//异步入库
	md := model.RiskDevice{}
	pmd := model.RiskDeviceProvider{}
	smd := model.RiskSdkDoid{}
	var DOIDData proto.CreateDOID
	if err := sonic.Unmarshal(msg, &DOIDData); err != nil {
		c.logger.Error("Unmarshal error", zap.Error(err))
	}

	information, _ := sonic.MarshalString(DOIDData.SDKDeviceInfo)
	db := startup.GetMySql().WithContext(ctx)

	info, err := pmd.FindByDOID(DOIDData.DOID, db)
	if err != nil {
		c.logger.Error("FindByDOID error", zap.Error(err))
		return err
	} else if info != nil {
		c.logger.Info("FindByDOID has exist", zap.Any("info", info))
		return nil
	}

	data := &model.RiskDeviceProvider{
		DOID:             DOIDData.DOID,
		Provider:         DOIDData.ProviderName,
		SdkDeviceId:      DOIDData.DeviceId,
		ThirdDeviceID:    DOIDData.ThirdDeviceId,
		ThirdData:        "",
		ThirdInformation: information,
		Ip:               DOIDData.Ip,
		Os:               DOIDData.SDKDeviceInfo.Os,
	}

	tDb := startup.GetMySql().WithContext(ctx).Begin()
	if err := pmd.Add(data, tDb); err != nil {
		tDb.Rollback()
		c.logger.Error("Add risk device provider error", zap.Error(err))
		return err
	}

	if err := md.Add(DOIDData.DOID, DOIDData.SDKDeviceInfo.Os, DOIDData.Ip, DOIDData.DeviceId, data.ID, tDb); err != nil {
		tDb.Rollback()
		c.logger.Error("Add risk device error", zap.Error(err))
		return err
	}

	if err := smd.Add(DOIDData.ThirdDeviceIdData.Sdk_DOID, DOIDData.DOID, tDb); err != nil {
		tDb.Rollback()
		c.logger.Error("Add risk sdk doid error", zap.Error(err))
		return err
	}

	tDb.Commit()

	if DOIDData.ProviderName == "shumei" {
		var tagsModel model.RiskDeviceAbnormalTag
		if ok := tagsModel.ExistByDOID(DOIDData.DOID, db); ok {
			return nil
		}

		postBody := fmt.Sprintf("{\"accessKey\":\"%s\",\"data\":{\"deviceId\":\"%s\"}}", config.Get().Dark.Ishumei.AccessKey, DOIDData.ThirdDeviceIdData.Shumei)
		res, err := device.NewDevice("shumei").GetRisk(ctx, postBody)
		if err != nil {
			c.logger.Error("GetRisk shumei error", zap.Error(err))
			return err
		}

		c.logger.Info("GetRisk shumei success", zap.Any("res", res), zap.String("DOID", DOIDData.DOID), zap.String("shumei device id", DOIDData.ThirdDeviceIdData.Shumei))
		var tags []string
		for key, val := range gconv.Map(res.DeviceLabels.FakeDevice) {
			if gvar.New(val).Int32() == 1 {
				tags = append(tags, key)
			}
		}

		for key, val := range gconv.Map(res.DeviceLabels.DeviceSuspiciousLabels) {
			if gvar.New(val).Int32() == 1 {
				tags = append(tags, key)
			}
		}

		if len(tags) == 0 {
			return nil
		}

		tagsStr, _ := sonic.MarshalString(tags)
		c.logger.Info("tags", zap.String("tags", tagsStr))

		err = tagsModel.Add(DOIDData.DOID, DOIDData.ThirdDeviceId, tagsStr, db)
		if err != nil {
			c.logger.Error("Add risk device abnormal tag error", zap.Error(err))
			return err
		}
	}

	return nil
}

func (c DOIDConsumer) insertSDKDOID(ctx context.Context, msg []byte) error {
	var data proto.CreateSdKDOID
	err := sonic.UnmarshalString(string(msg), &data)
	if err != nil {
		c.logger.Error("Unmarshal error", zap.Error(err))
		return err
	}

	smd := model.RiskSdkDoid{}
	db := startup.GetMySql().WithContext(ctx)
	if err := smd.Add(data.SDKDOID, data.DOID, db); err != nil {
		c.logger.Error("Add risk sdk doid error", zap.Error(err))
		return err
	}
	return nil
}

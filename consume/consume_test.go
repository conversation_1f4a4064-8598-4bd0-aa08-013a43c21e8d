package consume

import (
	"context"
	"testing"
)

func TestDOIDConsumer_insertDB(t *testing.T) {
	body := "{\"SDKDeviceInfo\":{\"os\":\"ios\",\"device_id\":\"pd-41465f97-201e-46f8-a579-d9ef5b02fa2c\",\"android_id\":\"\",\"oaid\":\"\",\"idfv\":\"\",\"idfa\":\"\"},\"DOID\":\"UJvH7Bw1pEV4vLT8ANMudQCRIlkstAMczrt3\",\"ip\":\"127.0.0.1\",\"providerName\":\"shumei\",\"thirdDeviceId\":\"ZX+8Fm0jTK+W+NJ1198wNpA7+il1QjE0WvDXJYP7Bcj3AKIRaCHZAqeD9ZoTVOyuj+Gz9tmXlJWcLdmLxi7tuA==\"}"

	consumer := DOIDConsumer{}
	err := consumer.insertDB(context.Background(), []byte(body))
	if err != nil {
		t.Error(err)
	}
}

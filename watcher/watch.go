package watcher

import (
	"context"
	"risk/service"
	"risk/service/conf"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/watch"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
)

const (
	GeetestWatchKey = "/risk/watch/geetest/config"
	etcdRiskScene   = "/sparrow/risk/conf/scene"
	etcdRiskModel   = "/sparrow/risk/conf/model"
)

var watcher *watch.Watcher

func Startup() error {
	var err error
	watcher, err = watch.StdConfig().Build()
	if err != nil {
		xlog.Error("watcher startup failed", zap.Error(err))
		return err
	}

	//geetest
	watcher.Watch(GeetestWatchKey, func(res *watch.Response) error {
		ctx := context.Background()
		logger := xlog.FromContext(ctx)
		var body struct {
			AppID   string `json:"app_id"`
			Version string `json:"version"`
		}
		xlog.Info("watcher ", xlog.String("key", GeetestWatchKey), xlog.String("value", res.Value))
		if res.Value == "" {
			logger.Error("GeetestWatchKey watcher value is empty", xlog.Any("res", res))
			return nil
		}
		value := []byte(res.Value)
		if err := sonic.Unmarshal(value, &body); err != nil {
			logger.Error("GeetestWatchKey watcher Unmarshal failed", xlog.Err(err), xlog.String("value", string(value)))
			return err
		}
		if _, err := conf.GetConfGeetestConfSvrIns().ReloadConfByKey(ctx, body.AppID); err != nil {
			logger.Error("ReloadConfByAppID failed", xlog.Err(err), xlog.Any("body", body))
			return err
		}

		return nil
	})

	//scene
	watcher.Watch(etcdRiskScene, func(res *watch.Response) error {
		s := new(service.Scene)
		xlog.Info("watcher ", xlog.String("key", etcdRiskScene), xlog.String("value", res.Value))
		return s.ReloadRiskScene()
	})

	//model
	watcher.Watch(etcdRiskModel, func(res *watch.Response) error {
		s := new(service.Model)
		xlog.Info("watcher ", xlog.String("key", etcdRiskModel), xlog.String("value", res.Value))
		return s.ReloadRiskModel()
	})

	return nil
}

package config

import (
	"sync/atomic"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type Config struct {
	Host                    string                 `xconf:"host"`
	InnerHost               string                 `xconf:"inner_host"`
	Domain                  string                 `xconf:"domain"`
	Register                bool                   `xconf:"register"`
	Machine                 Machine                `xconf:"machine"` //人机验证
	Dark                    Dark                   `xconf:"dark"`    //黑产
	SignVerifyIgnores       []string               `xconf:"sign_verify_ignores"`
	Cache                   Cache                  `xconf:"cache"`
	DeviceProvider          DeviceProvider         `xconf:"device_provider"`
	Crypto                  Crypto                 `xconf:"crypto"`
	Debug                   bool                   `xconf:"debug"`
	PigeonConfig            PigeonConfig           `xconf:"pigeon"`
	SMSConfig               SMSConfig              `xconf:"sms"`
	EmailConfig             EmailConfig            `xconf:"email"`
	CometConfig             CometConfig            `xconf:"comet"`
	DOIDCheck               bool                   `xconf:"doid_check"`
	DOIDCheckGames          []uint32               `xconf:"doid_check_games"`
	DOIDCheckDBDisable      bool                   `xconf:"doid_check_db_disable"`
	DOIDCheckObserveDisable bool                   `xconf:"doid_check_observe_disable"`
	BlackCheckDisable       bool                   `xconf:"black_check_disable"`
	ConsumerDisable         bool                   `xconf:"consumer_disable"`
	GRPCStrategy            GRPCStrategy           `xconf:"grpc_strategy"`
	CloseRisk               CloseRisk              `xconf:"close_risk"`
	NonceCheckAppId         bool                   `xconf:"nonce_check_app_id"`
	WorkerNum               int                    `xconf:"worker_num"`
	Options                 map[string]interface{} `xconf:"options"`

	//CodeChkAction     []string       `xconf:"code_chk_action"`
	//WebForceOpen      []string `xconf:"web_force_open"`
	//CaptchaCheckAppId bool     `xconf:"captcha_check_app_id"`
}

type CometConfig struct {
	Url string `xconf:"url"`
}

type PigeonConfig struct {
	Host   string `xconf:"host"`
	GMHost string `xconf:"gmhost"`
}

type EmailConfig struct {
	// 是否开启邮件验证
	IsOpen bool `xconf:"is_open"`
}

type SMSConfig struct {
	// 是否开启短信验证
	IsOpen bool `xconf:"is_open"`
}

type Cache struct {
	Type       string `xconf:"type"`   //memory、redis
	Expire     int32  `xconf:"expire"` //缓存过期时间 单位秒
	Cap        int    `xconf:"cap"`
	DOIDExpire int32  `xconf:"doid_expire"` //DOID缓存过期时间 单位秒
}

type Machine struct {
	Geetest         Geetest  `xconf:"geetest"`
	NonceExpire     uint32   `xconf:"nonce_expire"`
	VerifyValExpire uint32   `xconf:"verify_expire"`
	ForceAppId      []string `xconf:"force_app_id"`
	Prompt          string   `xconf:"prompt"`
}

type Geetest struct {
	ApiUri                string   `xconf:"api_uri"`
	CaptchaId             string   `xconf:"captcha_id"`
	CaptchaKey            string   `xconf:"captcha_key"`
	Disable               bool     `xconf:"disable"`
	DeviceCheckAppId      []string `xconf:"device_check_app_id"`
	EnableCheckIp         bool     `xconf:"disable_check_ip"`
	EnableCheckRiskDevice bool     `xconf:"disable_check_risk_device"`
}

type Dark struct {
	Ishumei Ishumei `xconf:"ishumei"`
	Aliyun  Aliyun  `xconf:"aliyun"`
	Expire
}

type Ishumei struct {
	IsOpen    bool   `xconf:"is_open"`
	Uri       string `xconf:"uri"`
	AccessKey string `xconf:"access_key"`
}

type Aliyun struct {
	IsOpen                 bool                  `xconf:"is_open"`
	AppId                  string                `xconf:"app_id"`
	AppSecret              string                `xconf:"app_secret"`
	IpRisk                 IpRisk                `xconf:"ip_risk"`
	DeviceRisk             DeviceRisk            `xconf:"device_risk"`
	FaceSceneId            int64                 `xconf:"face_scene_id"`
	FaceSceneIds           map[string]FaceScene  `xconf:"face_scene_ids"`
	FaceCodeRisks          map[string][]FaceRisk `xconf:"face_code_risks"`
	FaceWebCodeClose       map[string]bool       `xconf:"face_web_code_close"`
	FaceCodeExpire         int32                 `xconf:"face_code_expire"`
	FaceTokenExpire        int32                 `xconf:"face_token_expire"`
	FaceVerifyFailedNum    int64                 `xconf:"face_verify_failed_num"`
	FaceVerifyFailedExpire int64                 `xconf:"face_verify_failed_expire"`
	FaceVerifyModel        string                `xconf:"face_verify_model"`
}

type Expire struct {
	Black uint32 `xconf:"black"`
	White uint32 `xconf:"white"`
}

type IpRisk struct {
	RiskSource int32 `xconf:"risk_source"`
}

type DeviceRisk struct {
	Tags string `xconf:"tags"`
}
type DeviceProvider struct {
	IshumeiProvider IshumeiProvider `xconf:"ishumei"`
}

type IshumeiProvider struct {
	DeviceRiskUri    string `xconf:"device_risk_uri"`
	DeviceProfileUri string `xconf:"device_profile_uri"`
	CloudConfUri     string `xconf:"cloud_conf_uri"`
	Agency           bool   `xconf:"agency"`
	accessKey        string `xconf:"access_key"`
}

type Crypto struct {
	IgnoreAppIds        []string `xconf:"ignore_app_ids"`
	IgnorePaths         []string `xconf:"ignore_paths"`
	Close               bool     `xconf:"close"`
	GrayScalePercentage uint32   `xconf:"gray_scale_percentage"`
	Force               bool     `xconf:"force"`
}

type GRPCStrategy struct {
	LiveTagsUri   Host `xconf:"live_tags_uri"`
	EvaluationUri Host `xconf:"evaluation_uri"`
	Timeout       int  `xconf:"timeout"`
	IdleTimeout   int  `xconf:"idle_timeout"`
}

type CloseRisk struct {
	Login         bool          `xconf:"login"`
	LoginGames    []uint32      `xconf:"login_games"`
	EnterGame     bool          `xconf:"enter_game"`
	EnterGameConf EnterGameConf `xconf:"enter_game_conf"`
}

type EnterGameConf struct {
	ClientId []uint32 `xconf:"client_id"`
	HttpCode int      `xconf:"http_code"`
	Timeout  uint64   `xconf:"timeout"`
}

type FaceScene struct {
	Id    int64  `xconf:"id"`
	Model string `xconf:"model"`
}

type FaceRisk struct {
	Dimension  string `xconf:"dimension"`
	WindowTime uint32 `xconf:"window_time"`
	RateLimit  uint32 `xconf:"rate_limit"`
}

type Host struct {
	Host string `xconf:"host"`
	Port uint32 `xconf:"port"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		Host:     "",
		Register: false,
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(c)
	return nil
}

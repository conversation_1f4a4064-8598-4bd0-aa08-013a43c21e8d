ALTER TABLE `risk`.`risk_config_app` ADD COLUMN `title` VARCHAR ( 255 ) NOT NULL COMMENT '策略标题' AFTER `id`;
ALTER TABLE `risk`.`risk_config_app` MODIFY COLUMN `type` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型 11 登录常用设备 12 登录常在IP 13 是否开启人机校验 14 全局开关 101 账号支付成功率 102 IP支付成功率 103 设备支付成功率 201 账号频率 202 IP频率 203 设备频率 301 黑产IP 302 黑产设备 401 ip请求间隔 402 设备请求间隔 501 ip黑名单检查 502 设备黑名单检查  503 账号黑名单检查 601 ip白名单检查 602 设备白名单检查 603 账号白名单检查' AFTER `is_open`;
ALTER TABLE `risk`.`risk_config_app` MODIFY COLUMN `app_id` VARCHAR ( 50 ) CHARACTER
    SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all' COMMENT 'app ID all:全部租户适用' AFTER `id`,
ALTER TABLE `risk`.`risk_dark_list` MODIFY COLUMN `expired_at` BIGINT NOT NULL DEFAULT 0 COMMENT '过期时间' AFTER `score`;

DELETE
FROM
    `risk_config_app`;
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '人机校验开关', 'all', 0, '[]', 1, 13, 0, '2023-11-07 20:27:35', '2024-03-11 17:34:19', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '风控检测开关', 'all', 0, '[]', 0, 14, 0, '2023-11-16 21:36:43', '2024-03-11 17:34:52', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '账号频率检测', 'all', 0, '[{\"time\": 300, \"count\": 2}, {\"time\": 600, \"count\": 50}, {\"time\": 86400, \"count\": 60}]', 1, 201, 0, '2023-11-07 20:23:31', '2024-03-11 17:33:35', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, 'IP频率检测', 'all', 0, '[{\"time\": 300, \"count\": 20}, {\"time\": 600, \"count\": 50}, {\"time\": 86400, \"count\": 60}]', 1, 202, 0, '2023-11-07 20:26:22', '2024-03-11 14:25:09', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '设备频率检测', 'all', 0, '[{\"time\": 300, \"count\": 20}, {\"time\": 600, \"count\": 50}, {\"time\": 86400, \"count\": 60}]', 1, 203, 0, '2023-11-07 20:27:22', '2024-03-11 14:25:18', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '黑产IP检查', 'all', 0, '[]', 1, 301, 0, '2023-11-23 05:02:23', '2024-03-11 14:26:07', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '黑产设备检测', 'all', 0, '[]', 1, 302, 0, '2024-03-10 00:57:00', '2024-03-11 14:27:12', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, 'IP黑名单检测', 'all', 0, '[]', 1, 501, 0, '2024-03-10 00:57:20', '2024-03-11 14:27:14', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '设备黑名单检测', 'all', 0, '[]', 1, 502, 0, '2024-03-10 00:58:02', '2024-03-11 14:27:08', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '账号黑名单检测', 'all', 0, '[]', 1, 503, 0, '2024-03-10 00:58:13', '2024-03-11 14:27:16', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, 'IP白名单检测', 'all', 0, '[]', 1, 601, 0, '2024-03-10 00:58:33', '2024-03-11 14:27:17', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '设备白名单检测', 'all', 0, '[]', 1, 602, 0, '2024-03-10 00:58:37', '2024-03-11 14:27:19', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '账号白名单检测', 'all', 0, '[]', 1, 603, 0, '2024-03-10 00:58:46', '2024-03-11 14:27:21', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, 'ip请求间隔', 'all', 0, '{\"time\": 60}', 0, 401, 0, '2024-03-10 01:00:07', '2024-03-11 14:28:16', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '设备请求间隔', 'all', 0, '{\"time\": 60}', 0, 402, 0, '2024-03-10 01:00:42', '2024-03-11 14:28:30', 0 );
INSERT INTO `risk_config_app` ( `id`, `title`, `app_id`, `client_id`, `frequency`, `is_open`, `type`, `is_delete`, `created_at`, `updated_at`, `deleted_at` )
VALUES
    ( NULL, '账号请求间隔', 'all', 0, '{\"time\": 60}', 0, 403, 0, '2024-03-10 01:00:57', '2024-03-11 14:28:33', 0 );
sign_verify_ignores = [ "/v1/risk/payment/check", "/v1/risk/app/check", "/v1/risk/account/check" ] sparrow.DATABASE.verifydb.dataSource = root : BozsCMdcrj @( ************ : 3306 )/ verify ? timeout = 5s & parseTime = TRUE & loc = LOCAL & CHARSET = utf8 sparrow.DATABASE.verifydb.maxIdleConns = 5 sparrow.DATABASE.verifydb.maxOpenConns = 5 CACHE.type = redis
black_check_disable: false
consumer_disable: true
cache:
  cap: 10000000
  doid_expire: 15552000
  type: memory
close_risk:
  enter_game_conf:
    client_id: 
      - 1008
    enter_game_timeout: 5
    http_code: 403
code_chk_action: 
  - token_refresh 
  - login
comet:
  url: comet-graph-test.papegames.com:8080
crypto:
  close: true
  force: false
  gray_scale_percentage: 0
  ignore_app_ids: 
    - pc 
    - psn 
    - 1010026
  ignore_paths: 
    - /v1/risk/gs/check
    - /v1/risk/sd/get
    - /v1/risk/sd/config
    - /v1/risk/biz/init
    - /v1/risk/ds/check
dark:
  aliyun:
    app_id: LTAI5tSLGGuVxSQW7RyV6htE
    app_secret: ******************************
    device_risk:
      tags: rw_0101,rw_0103,rw_0104,rw_0108,is_openVpn,time_over,token_invalid,token_replay,token_tampered
    face_code_expire: 1800
    face_code_risks: 
      ********:
       - dimension: phone
         rate_limit: 5
         window_time: 120
       - dimension: id_card
         window_time: 120
         rate_limit: 10
    face_scene_id: **********
    face_scene_ids:
      account_real_info:
        id: **********
        model: PHOTINUS_LIVENESS
    face_token_expire: 43200
    face_verify_failed_expire: 86400
    face_verify_failed_num: 5
    face_verify_model: PHOTINUS_LIVENESS
    face_web_code_close: 
      1010026: false
    ip_risk:
      risk_source: 80
    is_open: 1
  expire:
    black: 604800
    white: 604800
  ishumei:
    access_key: 1tbxeHYLikYsIsk519KG
    is_open: 1
    uri: http://api-tianxiang-bj.fengkongcloud.com/tianxiang/v4
debug: true
device_provider:
  ishumei:
    agency: true
    cloud_conf_uri: http://fp-proxy.fengkongcloud.com/v3/cloudconf
    device_profile_uri: http://fp-proxy.fengkongcloud.com/deviceprofile/v4
doid_check: true
doid_check_db_disable: false
doid_check_games: 
  - 2008
domain: https://risk-api-dev.papegames.com
email:
  is_open: true
grpc_strategy:
  ad_black_list_uri:
    host: risk-ad-black
    port: 8090
  evaluation_uri:
    host: risk-evaluation-engine
    port: 8092
  idle_timeout: 30
  live_tags_uri:
    host: livetags
    port: 8090
  score_uri:
    host: risk-score-engine
    port: 8092
  timeout: 100
host: :8091
inner_host: :8089
machine:
  force_app_id:
  geetest:
    api_uri: http://gcaptcha4.geetest.com/validate
    captcha_check_app_id: ''
    captcha_id: 6aea8087471f762316431f24d83a142c
    captcha_key: a27b9d75447d91ca370a12a738b94362
    device_check_app_id: 
     - gSzx4NMA
    disable_check_ip: true
    disable_check_risk_device: true
  nonce_expire: 3600
  verify_expire: 10800
options: 
  face_verify_agreement:
    - name: 《个人信息处理授权书》
      url : https://reg.papegames.com/m/contract?key=authContract1
    - name: 《叠纸账号隐私协议》
      url : https://www.papegames.com/contract?key=accountPrivacy
  face_verify_toast: 为确保是本人操作,请您完成人脸认证
  face_verify_title: 您将使用以下身份信息进行人脸识别
  face_verify_subtitle: 请确保为本人操作
pigeon:
  gmhost: http://api-dev.papegames.com:12102
  host: https://api-dev.papegames.com:12101
register: false
sign_verify_ignores: 
  - /v1/risk/gs/check
  - /v1/risk/ad/check
  - /v1/risk/payment/check
  - /v1/risk/app/check 
  - /v1/risk/account/check 
  - /v1/risk/tob
  - /v1/risk/sd/get
  - /v1/risk/sd/config
  - /v1/risk/biz/init
  - /v1/risk/ds/check
  - /v1/health
  - /v1/risk/ad/check
sms:
  is_open: true
sparrow:
  broker:
    kafka:
      bi:
        writer:
          Brokers: alikafka-pre-cn-nwy3i48px00a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-3-vpc.alikafka.aliyuncs.com:9092
          Topic: tlog_json_risk_gameqos_test
      risk:
        reader:
          Brokers: ************:9092
          GroupID: risk_service
          Topic: risk-doid
        writer:
          Brokers: ************:9092
          Topic: risk-doid
  database:
    accessoriesdbreadconf:
      dataSource: root:BozsCMdcrj@(************:3306)/naccessories?timeout=5s&parseTime=true&loc=Local&charset=utf8
      maxIdleConns: 5
      maxOpenConns: 5
    mysql:
      dataSource: _vault(niD+qNgwo3mE2tzDcfMxPfs2HaKxVGGfbNV9W7J7SqOhtK57JRRuUxWgKtTcgLbAKEGHh9Fu1V8jKXIqezhO9lQ7bdNYsNDeY4K0/zbpzyXuI9GuJVkx6XCOaQCAUnVe8V3GGcH5wur9oQLua3KtsQ==)_
      maxIdleConns: 5
      maxOpenConns: 10
    redis:
      addr: ************:6379
    verifydb:
      dataSource: root:BozsCMdcrj@(************:3306)/verify?timeout=5s&parseTime=true&loc=Local&charset=utf8
      maxIdleConns: 5
      maxOpenConns: 5
  govern:
    enable: true
    host: 0.0.0.0:9091
  log:
    buffer: 4096
    color: false
    encoding: console
    file: ./logs/pt-risk.log
    level: debug
    rotate: daily
  watch:
    KeyPath: sparrow.watch.config
    config:
      endpoints: *************:2379

package integration

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"risk/service"
	"risk/startup"
	"risk/watcher"
	"sync"
	"testing"
	"time"

	"gitlab.papegames.com/fringe/sparrow"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpexpect"

	_ "risk/model"

	"risk/config"
	"risk/server"
)

var innerExpect = xconvey.NewExpector(server.GetInner().GetGinEngine())
var outerExpect = xconvey.NewExpector(server.GetOutward().GetGinEngine())

func TestMain(m *testing.M) {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		startup.Startup,
		server.Startup,
		service.LoadCache,
		watcher.Startup,
		service.StartModel,
		service.StartScene,
	).Server(server.GetOutward(), server.GetInner())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()

	waitReady(app)
	fmt.Println("TestMain start")
	c := m.Run()
	app.Stop()
	wg.Wait()
	fmt.Println("TestMain done")
	os.Exit(c)
}

func waitReady(app *sparrow.Application) {
	app.WaitReady(context.Background())
	for i := 0; i < 30; i++ {
		resp, err := http.Get("http://" + config.Get().Host + "/v1/health")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			return
		}
		time.Sleep(time.Millisecond * 100)
	}

	panic("waiting too long")
}

func requestObjectExpect(obj *xhttpexpect.Object, withoutData ...bool) {
	var keys []any
	if len(withoutData) != 0 && withoutData[0] {
		keys = []any{"code", "info", "request_id"}
	} else {
		keys = []any{"code", "info", "request_id", "data"}
	}
	obj.Keys().ContainsOnly(keys...)
	obj.Value("code").IsEqual(0)
	obj.Value("info").IsEqual("OK")
	obj.Value("request_id").NotNull()
}

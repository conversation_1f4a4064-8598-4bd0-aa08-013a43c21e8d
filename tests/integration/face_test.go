package integration

import (
	"net/http"
	"testing"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestQrCodeGenerate(t *testing.T) {
	Convey("test qrcode generate ", t, func(c C) {
		r := expect.Build(c).POST("/v1/risk/face/qrcode/generate").
			WithQuery("clientid", "1033").
			WithQuery("nid", "1111").
			Expect().
			Status(http.StatusOK).
			JSON().Object()
		requestObjectExpect(r)
		r.Value("code").Number().IsEqual(0)
	})
}

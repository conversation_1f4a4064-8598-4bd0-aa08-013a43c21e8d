package integration

import (
	"net/http"
	"testing"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

// TestQrCodeGenerate 测试二维码生成接口
func TestQrCodeGenerate(t *testing.T) {
	Convey("test qrcode generate", t, func(c C) {
		Convey("valid request should succeed", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("client_id", "100").
				WithFormField("nid", "test_user_123").
				WithFormField("token", "test_token_abc").
				WithFormField("real_name", "张三").
				WithFormField("id_card", "110101199001011234").
				WithFormField("return_url", "https://example.com/callback").
				WithFormField("scene", "account_real_info").
				WithFormField("vendor", "aliyun").
				WithFormField("doid", "test_device_123").
				WithFormField("app_id", "**********").
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			// 注意：由于控制器返回 nil, nil，这里可能会得到空响应
			// 实际实现后应该验证返回的二维码信息
			if r.Value("code").Number().Raw() == 0 {
				requestObjectExpect(r)
				data := r.Value("data").Object()
				data.Value("qrcode_id").String().NotEmpty()
				data.Value("expired_at").String().NotEmpty()
			}
		})

		Convey("missing required client_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("nid", "test_user_123").
				// missing client_id
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})

		Convey("missing required nid should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("client_id", "100").
				// missing nid
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})

		Convey("invalid client_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("client_id", "50"). // less than minimum 100
				WithFormField("nid", "test_user_123").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().NotEqual(0)
		})
	})
}

// TestQrCodeScan 测试二维码扫描接口
func TestQrCodeScan(t *testing.T) {
	Convey("test qrcode scan", t, func(c C) {
		Convey("valid qrcode_id should succeed", func() {
			// 首先生成二维码
			generateResp := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("client_id", "100").
				WithFormField("nid", "test_user_123").
				WithFormField("token", "test_token_abc").
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			if generateResp.Value("code").Number().Raw() == 0 {
				qrcodeId := generateResp.Value("data").Object().Value("qrcode_id").String().Raw()

				// 扫描二维码
				r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/scan").
					WithFormField("qrcode_id", qrcodeId).
					Expect().
					Status(http.StatusOK).
					JSON().Object()

				// 注意：由于控制器返回 nil, nil，这里可能会得到空响应
				if r.Value("code").Number().Raw() == 0 {
					requestObjectExpect(r)
					data := r.Value("data").Object()
					data.Value("uid").String().NotEmpty()
					data.Value("nid").String().NotEmpty()
					data.Value("token").String().NotEmpty()
				}
			}
		})

		Convey("missing qrcode_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/scan").
				// missing qrcode_id
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})

		Convey("invalid qrcode_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/scan").
				WithFormField("qrcode_id", "invalid_qrcode_123").
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})
	})
}

// TestFaceStatusQueryByQrCode 测试通过二维码查询人脸识别状态接口
func TestFaceStatusQueryByQrCode(t *testing.T) {
	Convey("test face status query by qrcode", t, func(c C) {
		Convey("valid qrcode_id should return status", func() {
			// 首先生成二维码
			generateResp := innerExpect.Build(c).POST("/v1/risk/face/qrcode/generate").
				WithFormField("client_id", "100").
				WithFormField("nid", "test_user_123").
				WithFormField("token", "test_token_abc").
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			if generateResp.Value("code").Number().Raw() == 0 {
				qrcodeId := generateResp.Value("data").Object().Value("qrcode_id").String().Raw()

				// 查询状态
				r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/status").
					WithFormField("qrcode_id", qrcodeId).
					Expect().
					Status(http.StatusOK).
					JSON().Object()

				// 注意：由于控制器返回 nil, nil，这里可能会得到空响应
				if r.Value("code").Number().Raw() == 0 {
					requestObjectExpect(r)
					data := r.Value("data").Object()
					data.Value("status").Number().InRange(0, 2) // 0:未认证, 1:认证通过, 2:认证不通过
				}
			}
		})

		Convey("missing qrcode_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/status").
				// missing qrcode_id
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})

		Convey("invalid qrcode_id should fail", func() {
			r := innerExpect.Build(c).POST("/v1/risk/face/qrcode/status").
				WithFormField("qrcode_id", "invalid_qrcode_123").
				Expect().
				Status(http.StatusOK).
				JSON().Object()

			r.Value("code").Number().NotEqual(0)
		})
	})
}

package xmetric

import "gitlab.papegames.com/fringe/sparrow/pkg/metric"

var Metrics = metric.NewCounterVec(&metric.CounterVecOpts{
	Namespace:   "all",
	Name:        "total",
	Help:        "risk chick total",
	Labels:      []string{"name", "op", "state"},
	ConstLabels: metric.TargetLabels,
})

var metricRequest = metric.NewHistogramVec(&metric.HistogramVecOpts{
	Namespace:   "xapi",
	Subsystem:   "requests",
	Name:        "rt_ms",
	Help:        "xapi requests rt(ms)",
	Labels:      []string{"uri", "code", "client_id", "app_id"},
	Buckets:     metric.BucketRT,
	ConstLabels: metric.TargetLabels,
})

var GrpcMetricRequest = metric.NewHistogramVec(&metric.HistogramVecOpts{
	Namespace:   "xapi",
	Subsystem:   "grpc_requests",
	Name:        "rt_ms",
	Help:        "xapi grpc requests rt(ms)",
	Labels:      []string{"service", "path", "id"},
	Buckets:     metric.BucketRT,
	ConstLabels: metric.TargetLabels,
})

var MetricsDark = metric.NewCounterVec(&metric.CounterVecOpts{
	Namespace:   "dark",
	Name:        "total",
	Help:        "dark total",
	Labels:      []string{"app_id", "client_id", "type", "value", "state"},
	ConstLabels: metric.TargetLabels,
})

var MetricsFrequency = metric.NewCounterVec(&metric.CounterVecOpts{
	Namespace:   "frequency",
	Name:        "total",
	Help:        "frequency total",
	Labels:      []string{"app_id", "client_id", "type", "action", "value", "state"},
	ConstLabels: metric.TargetLabels,
})

var MetricsInterval = metric.NewCounterVec(&metric.CounterVecOpts{
	Namespace:   "interval",
	Name:        "total",
	Help:        "frequency total",
	Labels:      []string{"app_id", "client_id", "type", "action", "value", "state"},
	ConstLabels: metric.TargetLabels,
})

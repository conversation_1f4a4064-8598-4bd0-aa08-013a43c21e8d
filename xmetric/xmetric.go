package xmetric

import (
	"context"
	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xbytes"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"net/http"
	"strconv"
	"time"
)

func NewXMetric() gin.HandlerFunc {
	return func(c *gin.Context) {
		body, err := xnet.DrainBody(c.Request)
		if err != nil {
			c.Abort()
			c.String(http.StatusRequestEntityTooLarge, "Request Entity Too Large")
			return
		}
		err = c.Request.ParseForm()
		if err != nil {
			c.Abort()
			c.String(http.StatusBadRequest, "bad request")
			return
		}
		// 重新设置body
		c.Request.Body = xbytes.NewBuffer(body)

		clientId := c.Request.FormValue("client_id")
		appId := c.Request.FormValue("app_id")
		begin := time.Now()
		defer func() {
			var (
				code    string
				path    = c.FullPath()
				status  = c.Writer.Status()
				latency = float64(time.Since(begin).Milliseconds())
			)

			if status == http.StatusNotFound {
				path, code = "/404", "404"
			} else {
				r := xgin.GetReturn(c)
				if r != nil {
					code = strconv.Itoa(r.Code)
				} else {
					code = strconv.Itoa(status)
				}
			}
			metricRequest.Observe(latency, path, code, clientId, appId)
		}()

		requestID := xnet.GetRequestId(c.Request)
		logger := xlog.L().With(xlog.RequestID(requestID))
		ctx := context.Background()
		ctx = xgin.NewContext(ctx, c)
		ctx = xlog.NewContext(ctx, logger)
		ctx = xnet.NewContext(ctx, xnet.Forwarded, c.GetHeader(xnet.Forwarded))
		ctx = xnet.NewContext(ctx, xnet.RequestId, requestID)

		xgin.WithContext(c, ctx)

		c.Next()
	}
}

syntax = "proto3";

package papegames.sparrow.cometgraph;

import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "papegames/type/empty.proto";
import "openapiv3/annotations.proto";

option go_package = "gitlab.papegames.com/fringe/comet-graph/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "CometGraphProto";
option java_package = "com.papegames.sparrow.cometgraph";

service CometGraphService {
  option (google.api.default_host) = "comet-graph.papegames.com";
  rpc Broadcast(BroadcastRequest) returns (papegames.type.Empty) {
  }

  rpc Subscribe(SubscribeRequest) returns (stream SubscribeResponse) {}

  rpc AcquireToken(AcquireTokenRequest) returns (AcquireTokenResponse) {}

  rpc UpstreamData(UpstreamDataRequest) returns (papegames.type.Empty) {}
}

message BroadcastRequest {
  bytes message = 1
  [(google.api.field_behavior) = REQUIRED,
  (openapi.v3.property) = {
    min_length: 1
  }];
}

message SubscribeRequest {
  string channel = 1
  [(google.api.field_behavior) = REQUIRED,
  (openapi.v3.property) = {
    min_length: 1
  }];
}

message SubscribeResponse{
  bytes message = 1;
}

message AcquireTokenRequest {
  string name = 1[(google.api.field_behavior) = REQUIRED,
  (openapi.v3.property) = {
    min_length: 1,
    max_length: 32,
  }];
  repeated string scopes  = 2[(google.api.field_behavior) = REQUIRED,
  (openapi.v3.property) = {
    min_length: 1
  }];
}

message AcquireTokenResponse {
  string token = 1;
}

message UpstreamDataRequest {
  string biz  =1;
  bytes message =2;
}
// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.1
// source: proto/comet-graph.proto

package proto

import (
	context "context"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CometGraphService_Broadcast_FullMethodName    = "/papegames.sparrow.cometgraph.CometGraphService/Broadcast"
	CometGraphService_Subscribe_FullMethodName    = "/papegames.sparrow.cometgraph.CometGraphService/Subscribe"
	CometGraphService_AcquireToken_FullMethodName = "/papegames.sparrow.cometgraph.CometGraphService/AcquireToken"
	CometGraphService_UpstreamData_FullMethodName = "/papegames.sparrow.cometgraph.CometGraphService/UpstreamData"
)

// CometGraphServiceClient is the client API for CometGraphService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CometGraphServiceClient interface {
	Broadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	Subscribe(ctx context.Context, in *SubscribeRequest, opts ...grpc.CallOption) (CometGraphService_SubscribeClient, error)
	AcquireToken(ctx context.Context, in *AcquireTokenRequest, opts ...grpc.CallOption) (*AcquireTokenResponse, error)
	UpstreamData(ctx context.Context, in *UpstreamDataRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
}

type cometGraphServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCometGraphServiceClient(cc grpc.ClientConnInterface) CometGraphServiceClient {
	return &cometGraphServiceClient{cc}
}

func (c *cometGraphServiceClient) Broadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, CometGraphService_Broadcast_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cometGraphServiceClient) Subscribe(ctx context.Context, in *SubscribeRequest, opts ...grpc.CallOption) (CometGraphService_SubscribeClient, error) {
	stream, err := c.cc.NewStream(ctx, &CometGraphService_ServiceDesc.Streams[0], CometGraphService_Subscribe_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &cometGraphServiceSubscribeClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type CometGraphService_SubscribeClient interface {
	Recv() (*SubscribeResponse, error)
	grpc.ClientStream
}

type cometGraphServiceSubscribeClient struct {
	grpc.ClientStream
}

func (x *cometGraphServiceSubscribeClient) Recv() (*SubscribeResponse, error) {
	m := new(SubscribeResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *cometGraphServiceClient) AcquireToken(ctx context.Context, in *AcquireTokenRequest, opts ...grpc.CallOption) (*AcquireTokenResponse, error) {
	out := new(AcquireTokenResponse)
	err := c.cc.Invoke(ctx, CometGraphService_AcquireToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cometGraphServiceClient) UpstreamData(ctx context.Context, in *UpstreamDataRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, CometGraphService_UpstreamData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CometGraphServiceServer is the server API for CometGraphService service.
// All implementations must embed UnimplementedCometGraphServiceServer
// for forward compatibility
type CometGraphServiceServer interface {
	Broadcast(context.Context, *BroadcastRequest) (*xtype.Empty, error)
	Subscribe(*SubscribeRequest, CometGraphService_SubscribeServer) error
	AcquireToken(context.Context, *AcquireTokenRequest) (*AcquireTokenResponse, error)
	UpstreamData(context.Context, *UpstreamDataRequest) (*xtype.Empty, error)
	mustEmbedUnimplementedCometGraphServiceServer()
}

// UnimplementedCometGraphServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCometGraphServiceServer struct {
}

func (UnimplementedCometGraphServiceServer) Broadcast(context.Context, *BroadcastRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Broadcast not implemented")
}
func (UnimplementedCometGraphServiceServer) Subscribe(*SubscribeRequest, CometGraphService_SubscribeServer) error {
	return status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}
func (UnimplementedCometGraphServiceServer) AcquireToken(context.Context, *AcquireTokenRequest) (*AcquireTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcquireToken not implemented")
}
func (UnimplementedCometGraphServiceServer) UpstreamData(context.Context, *UpstreamDataRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpstreamData not implemented")
}
func (UnimplementedCometGraphServiceServer) mustEmbedUnimplementedCometGraphServiceServer() {}

// UnsafeCometGraphServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CometGraphServiceServer will
// result in compilation errors.
type UnsafeCometGraphServiceServer interface {
	mustEmbedUnimplementedCometGraphServiceServer()
}

func RegisterCometGraphServiceServer(s grpc.ServiceRegistrar, srv CometGraphServiceServer) {
	s.RegisterService(&CometGraphService_ServiceDesc, srv)
}

func _CometGraphService_Broadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CometGraphServiceServer).Broadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CometGraphService_Broadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CometGraphServiceServer).Broadcast(ctx, req.(*BroadcastRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CometGraphService_Subscribe_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SubscribeRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(CometGraphServiceServer).Subscribe(m, &cometGraphServiceSubscribeServer{stream})
}

type CometGraphService_SubscribeServer interface {
	Send(*SubscribeResponse) error
	grpc.ServerStream
}

type cometGraphServiceSubscribeServer struct {
	grpc.ServerStream
}

func (x *cometGraphServiceSubscribeServer) Send(m *SubscribeResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _CometGraphService_AcquireToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcquireTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CometGraphServiceServer).AcquireToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CometGraphService_AcquireToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CometGraphServiceServer).AcquireToken(ctx, req.(*AcquireTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CometGraphService_UpstreamData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpstreamDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CometGraphServiceServer).UpstreamData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CometGraphService_UpstreamData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CometGraphServiceServer).UpstreamData(ctx, req.(*UpstreamDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CometGraphService_ServiceDesc is the grpc.ServiceDesc for CometGraphService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CometGraphService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.cometgraph.CometGraphService",
	HandlerType: (*CometGraphServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Broadcast",
			Handler:    _CometGraphService_Broadcast_Handler,
		},
		{
			MethodName: "AcquireToken",
			Handler:    _CometGraphService_AcquireToken_Handler,
		},
		{
			MethodName: "UpstreamData",
			Handler:    _CometGraphService_UpstreamData_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Subscribe",
			Handler:       _CometGraphService_Subscribe_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/comet-graph.proto",
}

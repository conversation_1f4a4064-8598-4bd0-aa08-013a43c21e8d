// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/comet-graph.proto

package proto

func (x *BroadcastRequest) Validate() error {
	return nil
}

func (x *SubscribeRequest) Validate() error {
	if len(x.GetChannel()) == 0 {
		return SubscribeRequestValidationError{
			field:   "Channel",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetChannel()) < 1 {
		return SubscribeRequestValidationError{
			field:   "Channel",
			reason:  "min_length",
			message: "value must be at least 1 bytes",
		}
	}
	return nil
}

func (x *SubscribeResponse) Validate() error {
	return nil
}

func (x *AcquireTokenRequest) Validate() error {
	if len(x.GetName()) == 0 {
		return AcquireTokenRequestValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.<PERSON>()) < 1 {
		return AcquireTokenRequestValidationError{
			field:   "Name",
			reason:  "min_length",
			message: "value must be at least 1 bytes",
		}
	}
	if len(x.GetName()) > 32 {
		return AcquireTokenRequestValidationError{
			field:   "Name",
			reason:  "max_length",
			message: "value length must be at most 32 bytes",
		}
	}
	if len(x.GetScopes()) == 0 {
		return AcquireTokenRequestValidationError{
			field:   "Scopes",
			reason:  "required",
			message: "value is required",
		}
	}
	for _, item := range x.GetScopes() {
		if len(item) == 0 {
			return AcquireTokenRequestValidationError{
				field:   "Scopes",
				reason:  "required",
				message: "value is required",
			}
		}
		if len(item) < 1 {
			return AcquireTokenRequestValidationError{
				field:   "Scopes",
				reason:  "min_length",
				message: "value must be at least 1 bytes",
			}
		}
	}
	return nil
}

func (x *AcquireTokenResponse) Validate() error {
	return nil
}

func (x *UpstreamDataRequest) Validate() error {
	return nil
}

type BroadcastRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BroadcastRequestValidationError) Field() string { return e.field }

func (e BroadcastRequestValidationError) Reason() string { return e.reason }

func (e BroadcastRequestValidationError) Message() string { return e.message }

func (e BroadcastRequestValidationError) Cause() error { return e.cause }

func (e BroadcastRequestValidationError) ErrorName() string { return "BroadcastRequestValidationError" }

func (e BroadcastRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BroadcastRequest." + e.field + ": " + e.message + cause
}

type SubscribeRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SubscribeRequestValidationError) Field() string { return e.field }

func (e SubscribeRequestValidationError) Reason() string { return e.reason }

func (e SubscribeRequestValidationError) Message() string { return e.message }

func (e SubscribeRequestValidationError) Cause() error { return e.cause }

func (e SubscribeRequestValidationError) ErrorName() string { return "SubscribeRequestValidationError" }

func (e SubscribeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SubscribeRequest." + e.field + ": " + e.message + cause
}

type SubscribeResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SubscribeResponseValidationError) Field() string { return e.field }

func (e SubscribeResponseValidationError) Reason() string { return e.reason }

func (e SubscribeResponseValidationError) Message() string { return e.message }

func (e SubscribeResponseValidationError) Cause() error { return e.cause }

func (e SubscribeResponseValidationError) ErrorName() string {
	return "SubscribeResponseValidationError"
}

func (e SubscribeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SubscribeResponse." + e.field + ": " + e.message + cause
}

type AcquireTokenRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AcquireTokenRequestValidationError) Field() string { return e.field }

func (e AcquireTokenRequestValidationError) Reason() string { return e.reason }

func (e AcquireTokenRequestValidationError) Message() string { return e.message }

func (e AcquireTokenRequestValidationError) Cause() error { return e.cause }

func (e AcquireTokenRequestValidationError) ErrorName() string {
	return "AcquireTokenRequestValidationError"
}

func (e AcquireTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AcquireTokenRequest." + e.field + ": " + e.message + cause
}

type AcquireTokenResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AcquireTokenResponseValidationError) Field() string { return e.field }

func (e AcquireTokenResponseValidationError) Reason() string { return e.reason }

func (e AcquireTokenResponseValidationError) Message() string { return e.message }

func (e AcquireTokenResponseValidationError) Cause() error { return e.cause }

func (e AcquireTokenResponseValidationError) ErrorName() string {
	return "AcquireTokenResponseValidationError"
}

func (e AcquireTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AcquireTokenResponse." + e.field + ": " + e.message + cause
}

type UpstreamDataRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UpstreamDataRequestValidationError) Field() string { return e.field }

func (e UpstreamDataRequestValidationError) Reason() string { return e.reason }

func (e UpstreamDataRequestValidationError) Message() string { return e.message }

func (e UpstreamDataRequestValidationError) Cause() error { return e.cause }

func (e UpstreamDataRequestValidationError) ErrorName() string {
	return "UpstreamDataRequestValidationError"
}

func (e UpstreamDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UpstreamDataRequest." + e.field + ": " + e.message + cause
}

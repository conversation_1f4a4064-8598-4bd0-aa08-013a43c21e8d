// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.1
// source: proto/comet-graph.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BroadcastRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []byte `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *BroadcastRequest) Reset() {
	*x = BroadcastRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BroadcastRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastRequest) ProtoMessage() {}

func (x *BroadcastRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastRequest.ProtoReflect.Descriptor instead.
func (*BroadcastRequest) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{0}
}

func (x *BroadcastRequest) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

type SubscribeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel string `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *SubscribeRequest) Reset() {
	*x = SubscribeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequest) ProtoMessage() {}

func (x *SubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequest.ProtoReflect.Descriptor instead.
func (*SubscribeRequest) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{1}
}

func (x *SubscribeRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type SubscribeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []byte `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SubscribeResponse) Reset() {
	*x = SubscribeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeResponse) ProtoMessage() {}

func (x *SubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeResponse.ProtoReflect.Descriptor instead.
func (*SubscribeResponse) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{2}
}

func (x *SubscribeResponse) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

type AcquireTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Scopes []string `protobuf:"bytes,2,rep,name=scopes,proto3" json:"scopes,omitempty"`
}

func (x *AcquireTokenRequest) Reset() {
	*x = AcquireTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquireTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireTokenRequest) ProtoMessage() {}

func (x *AcquireTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireTokenRequest.ProtoReflect.Descriptor instead.
func (*AcquireTokenRequest) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{3}
}

func (x *AcquireTokenRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AcquireTokenRequest) GetScopes() []string {
	if x != nil {
		return x.Scopes
	}
	return nil
}

type AcquireTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *AcquireTokenResponse) Reset() {
	*x = AcquireTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquireTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireTokenResponse) ProtoMessage() {}

func (x *AcquireTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireTokenResponse.ProtoReflect.Descriptor instead.
func (*AcquireTokenResponse) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{4}
}

func (x *AcquireTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UpstreamDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Biz     string `protobuf:"bytes,1,opt,name=biz,proto3" json:"biz,omitempty"`
	Message []byte `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *UpstreamDataRequest) Reset() {
	*x = UpstreamDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_graph_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpstreamDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpstreamDataRequest) ProtoMessage() {}

func (x *UpstreamDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_graph_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpstreamDataRequest.ProtoReflect.Descriptor instead.
func (*UpstreamDataRequest) Descriptor() ([]byte, []int) {
	return file_proto_comet_graph_proto_rawDescGZIP(), []int{5}
}

func (x *UpstreamDataRequest) GetBiz() string {
	if x != nil {
		return x.Biz
	}
	return ""
}

func (x *UpstreamDataRequest) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_proto_comet_graph_proto protoreflect.FileDescriptor

var file_proto_comet_graph_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2d, 0x67, 0x72,
	0x61, 0x70, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d,
	0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x10, 0x42, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42,
	0x09, 0xe0, 0x41, 0x02, 0xba, 0x47, 0x03, 0x80, 0x01, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x37, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xe0, 0x41, 0x02, 0xba, 0x47, 0x03,
	0x80, 0x01, 0x01, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x2d, 0x0a, 0x11,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x59, 0x0a, 0x13, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0xe0, 0x41, 0x02, 0xba, 0x47, 0x05, 0x78, 0x20, 0x80, 0x01, 0x01, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x09, 0xe0, 0x41, 0x02, 0xba, 0x47, 0x03, 0x80, 0x01, 0x01, 0x52, 0x06,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x22, 0x2c, 0x0a, 0x14, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x41, 0x0a, 0x13, 0x55, 0x70, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x62,
	0x69, 0x7a, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xce, 0x03, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x65,
	0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54, 0x0a,
	0x09, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63,
	0x6f, 0x6d, 0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x12, 0x2e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x77, 0x0a, 0x0c, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d,
	0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a,
	0x0a, 0x0c, 0x55, 0x70, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x55, 0x70,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x1a, 0x1c, 0xca, 0x41, 0x19, 0x63,
	0x6f, 0x6d, 0x65, 0x74, 0x2d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x42, 0x6a, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x42, 0x0f, 0x43, 0x6f,
	0x6d, 0x65, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a,
	0x33, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6d,
	0x65, 0x74, 0x2d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_comet_graph_proto_rawDescOnce sync.Once
	file_proto_comet_graph_proto_rawDescData = file_proto_comet_graph_proto_rawDesc
)

func file_proto_comet_graph_proto_rawDescGZIP() []byte {
	file_proto_comet_graph_proto_rawDescOnce.Do(func() {
		file_proto_comet_graph_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_comet_graph_proto_rawDescData)
	})
	return file_proto_comet_graph_proto_rawDescData
}

var file_proto_comet_graph_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_comet_graph_proto_goTypes = []interface{}{
	(*BroadcastRequest)(nil),     // 0: papegames.sparrow.cometgraph.BroadcastRequest
	(*SubscribeRequest)(nil),     // 1: papegames.sparrow.cometgraph.SubscribeRequest
	(*SubscribeResponse)(nil),    // 2: papegames.sparrow.cometgraph.SubscribeResponse
	(*AcquireTokenRequest)(nil),  // 3: papegames.sparrow.cometgraph.AcquireTokenRequest
	(*AcquireTokenResponse)(nil), // 4: papegames.sparrow.cometgraph.AcquireTokenResponse
	(*UpstreamDataRequest)(nil),  // 5: papegames.sparrow.cometgraph.UpstreamDataRequest
	(*xtype.Empty)(nil),          // 6: papegames.type.Empty
}
var file_proto_comet_graph_proto_depIdxs = []int32{
	0, // 0: papegames.sparrow.cometgraph.CometGraphService.Broadcast:input_type -> papegames.sparrow.cometgraph.BroadcastRequest
	1, // 1: papegames.sparrow.cometgraph.CometGraphService.Subscribe:input_type -> papegames.sparrow.cometgraph.SubscribeRequest
	3, // 2: papegames.sparrow.cometgraph.CometGraphService.AcquireToken:input_type -> papegames.sparrow.cometgraph.AcquireTokenRequest
	5, // 3: papegames.sparrow.cometgraph.CometGraphService.UpstreamData:input_type -> papegames.sparrow.cometgraph.UpstreamDataRequest
	6, // 4: papegames.sparrow.cometgraph.CometGraphService.Broadcast:output_type -> papegames.type.Empty
	2, // 5: papegames.sparrow.cometgraph.CometGraphService.Subscribe:output_type -> papegames.sparrow.cometgraph.SubscribeResponse
	4, // 6: papegames.sparrow.cometgraph.CometGraphService.AcquireToken:output_type -> papegames.sparrow.cometgraph.AcquireTokenResponse
	6, // 7: papegames.sparrow.cometgraph.CometGraphService.UpstreamData:output_type -> papegames.type.Empty
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_comet_graph_proto_init() }
func file_proto_comet_graph_proto_init() {
	if File_proto_comet_graph_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_comet_graph_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BroadcastRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_graph_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_graph_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscribeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_graph_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquireTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_graph_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquireTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_graph_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpstreamDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_comet_graph_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_comet_graph_proto_goTypes,
		DependencyIndexes: file_proto_comet_graph_proto_depIdxs,
		MessageInfos:      file_proto_comet_graph_proto_msgTypes,
	}.Build()
	File_proto_comet_graph_proto = out.File
	file_proto_comet_graph_proto_rawDesc = nil
	file_proto_comet_graph_proto_goTypes = nil
	file_proto_comet_graph_proto_depIdxs = nil
}

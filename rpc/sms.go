package rpc

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"net/url"
	"risk/config"
	"risk/constant"
	"risk/utils"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
)

type GetPhoneCodeReq struct {
	Phone             string `required:"true"`
	InternationalCode string `required:"false"`
	Scene             string `required:"false"`

	Country string `required:"false" format:"country"`
	Forward string `required:"false"`
}

type RetResult struct {
	Ret int    `json:"ret"`
	Msg string `json:"msg"`
}

type GetPhoneCodeResp struct {
}

type GetPhoneCodeRespTest struct {
	RetResult
	Code string
}

func GetPhoneCode(ctx context.Context, req *GetPhoneCodeReq) (*GetPhoneCodeResp, error) {
	uri := "/v1/phonecode"
	vals := url.Values{}
	vals.Set("phone", req.Phone)
	vals.Set("internationalcode", req.InternationalCode)
	vals.Set("scene", req.Scene)
	vals.Set("country", req.Country)
	vals.Set("forward", req.Forward)

	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.Host+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("GetPhoneCode request pigeon failed", zap.Any("req", req), zap.Error(err))
		return nil, constant.ErrSmsSendFail
	}
	result := &utils.PigeonResp{}
	err = sonic.Unmarshal(body, result)
	if err != nil {
		xlog.FromContext(ctx).Error("GetPhoneCode json unmarshal failed", zap.Any("req", req), zap.Error(err), zap.Any("body", body))
		return nil, constant.ErrSmsSendFail
	}
	if result.Ret != 0 {
		xlog.FromContext(ctx).Error("GetPhoneCode request pigeon failed", zap.Any("req", req), zap.Any("result", result))
		return nil, ecode.Error(constant.ErrSmsSendFail, result.Msg)
	}
	return &GetPhoneCodeResp{}, nil
}

func GetPhoneCodeTest(ctx context.Context, req *GetPhoneCodeReq) (*GetPhoneCodeRespTest, error) {
	uri := "/v1/phonecodetest"
	vals := url.Values{}
	vals.Set("phone", req.Phone)
	vals.Set("scene", req.Scene)

	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.GMHost+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("GetPhoneCode Test request pigeon failed", zap.Any("req", req), zap.Error(err))
		return nil, constant.ErrSmsSendFail
	}
	result := &GetPhoneCodeRespTest{}
	err = sonic.Unmarshal(body, result)
	if err != nil {
		xlog.FromContext(ctx).Error("GetPhoneCode Test json unmarshal failed", zap.Any("req", req), zap.Error(err), zap.Any("body", body))
		return nil, constant.ErrSmsSendFail
	}
	if result.Ret != 0 {
		xlog.FromContext(ctx).Error("GetPhoneCode Test request pigeon failed", zap.Any("req", req), zap.Any("result", result))
		return nil, ecode.Error(constant.ErrSmsSendFail, result.Msg)
	}
	return result, nil
}

type CheckPhoneCodeReq struct {
	Phone             string `required:"true"`
	InternationalCode string `required:"false"`
	Code              string `required:"true"`
	Scene             string `required:"false"`

	Country string `required:"false" format:"country"`
}

type CheckPhoneCodeResp struct {
	Verify string `json:"verify"`
}

func CheckPhoneCode(ctx context.Context, req *CheckPhoneCodeReq) error {
	uri := "/v1/phonecodecheck"
	vals := url.Values{}
	vals.Set("phone", req.Phone)
	vals.Set("internationalcode", req.InternationalCode)
	vals.Set("code", req.Code)
	vals.Set("country", req.Country)
	vals.Set("scene", req.Scene)

	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.Host+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("CheckPhoneCode request pigeon failed", zap.Any("req", req), zap.Error(err))
		return nil
	}
	result := &utils.PigeonResp{}
	err = sonic.Unmarshal(body, result)
	if err != nil {
		xlog.FromContext(ctx).Error("CheckPhoneCode json unmarshal failed", zap.Any("req", req), zap.Error(err), zap.Any("body", body))
		return nil
	}
	if result.Ret != 0 {
		xlog.FromContext(ctx).Error("CheckPhoneCode request pigeon failed", zap.Any("req", req), zap.Any("result", result))
		return constant.ErrSmsCodeNotMatch
	}
	var resp CheckPhoneCodeResp
	if err := sonic.Unmarshal(body, &resp); err != nil {
		xlog.FromContext(ctx).Error("CheckPhoneCode json unmarshal failed", zap.Any("req", req), zap.Error(err), zap.Any("data", result.Data))
		return nil
	}
	if resp.Verify != "ok" {
		return constant.ErrSmsCodeNotMatch
	}

	return nil
}

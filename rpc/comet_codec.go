package rpc

import (
	"encoding/json"
)

var (
	CometVer = "1"
	Delim    = []byte("\r\n")
)

type CmdType string

const (
	CmdTypeSub   CmdType = "subscribe"
	CmdTypeHb    CmdType = "heartbeat"
	CmdTypeClose CmdType = "close"
	CmdTypeMsg   CmdType = "message"
)

var CmdTypeMap = map[CmdType]struct{}{
	CmdTypeSub:   {},
	CmdTypeHb:    {},
	CmdTypeClose: {},
	CmdTypeMsg:   {},
}

type Option struct {
	Channel string            `json:"channel,omitempty"`
	Header  map[string]string `json:"header,omitempty"`
	Module  string            `json:"module"`
}

type Header struct {
	Command   CmdType `json:"command"`
	Version   string  `json:"version"`
	Option    Option  `json:"option,omitempty"`
	RequestId string  `json:"request_id,omitempty"`
}

func (h *Header) ToBytes() ([]byte, error) {
	b, err := json.Marshal(h)
	if err != nil {
		return nil, err
	}
	return b, nil
}

type StreamMessage struct {
	Header  *Header
	Payload []byte
}

func NewStreamMessage(h *Header, payload []byte) *StreamMessage {
	return &StreamMessage{
		Header:  h,
		Payload: payload,
	}
}

func (h *StreamMessage) ToBytes() ([]byte, error) {
	b, err := json.Marshal(h.Header)
	if err != nil {
		return nil, err
	}
	if len(h.Payload) > 0 {
		b = append(b, Delim...)
		b = append(b, h.Payload...)
	}
	return b, nil
}

package rpc

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"net/url"
	"risk/config"
	"risk/constant"
	"risk/utils"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
)

type GetSimpleProfileResp struct {
	Ret   int    `json:"ret"`
	Msg   string `json:"msg"`
	Phone string `json:"phone"`
	Email string `json:"email"`
}

type GetRealInfoResp struct {
	Ret      int    `json:"ret"`
	Msg      string `json:"msg"`
	RealName string `json:"realname"`
	RealId   string `json:"realid"`
}

func GetUserSimpleProfile(ctx context.Context, nid, account string) (*GetSimpleProfileResp, error) {
	uri := "/v1/user/getsimpleprofile"
	vals := url.Values{}
	if nid != "" {
		vals.Set("nid", nid)
	}
	if account != "" {
		vals.Set("account", account)
	}

	var resp GetSimpleProfileResp
	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.GMHost+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserInfo request pigeon failed", zap.Any("nid", nid), zap.String("account", account), zap.Error(err))
		return &resp, constant.ErrGetUserInfoFail
	}

	xlog.FromContext(ctx).Info("GetUserInfo request pigeon success", zap.Any("nid", nid), zap.Any("body", body))

	err = sonic.Unmarshal(body, &resp)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserInfo json unmarshal failed", zap.Any("nid", nid), zap.Error(err), zap.Any("body", body))
		return &resp, constant.ErrGetUserInfoFail
	}
	if resp.Ret != 0 {
		xlog.FromContext(ctx).Error("GetUserInfo request pigeon failed", zap.Any("nid", nid), zap.Any("result", resp))
		return &resp, constant.ErrGetUserInfoFail
	}

	return &resp, nil
}

func GetRealInfo(ctx context.Context, nid string) (*GetRealInfoResp, error) {
	uri := "/v1/getrealinfo"
	vals := url.Values{}
	vals.Set("nid", nid)
	vals.Set("withoutmark", "1")

	var resp GetRealInfoResp
	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.GMHost+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("GetRealInfo request pigeon failed", zap.Any("nid", nid), zap.Error(err))
		return &resp, ecode.ServerError
	}

	xlog.FromContext(ctx).Info("GetRealInfo request pigeon success", zap.Any("nid", nid), zap.Any("body", body))

	err = sonic.Unmarshal(body, &resp)
	if err != nil {
		xlog.FromContext(ctx).Error("GetRealInfo json unmarshal failed", zap.Any("nid", nid), zap.Error(err), zap.Any("body", body))
		return &resp, ecode.ServerError
	}
	if resp.Ret == 1024 {
		xlog.FromContext(ctx).Error("GetRealInfo request pigeon failed", zap.Any("nid", nid), zap.Any("result", resp))
		return &resp, ecode.Error(constant.ErrParam, resp.Msg)
	}

	return &resp, nil
}

func ChkToken(ctx context.Context, nid, token string) (bool, error) {
	uri := "/v1/token/verify"
	vals := url.Values{}
	vals.Set("token", token)
	vals.Set("nid", nid)

	var resp GetSimpleProfileResp
	body, err := utils.PigeonRequest(ctx, config.Get().PigeonConfig.Host+uri, vals)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserInfo request pigeon failed", zap.Any("nid", nid), zap.String("token", token), zap.Error(err))
		return false, constant.ErrUserInfoInvalid
	}

	xlog.FromContext(ctx).Info("GetUserInfo request pigeon success", zap.Any("nid", nid), zap.Any("body", body))

	err = sonic.Unmarshal(body, &resp)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserInfo json unmarshal failed", zap.Any("nid", nid), zap.Error(err), zap.Any("body", body))
		return false, constant.ErrUserInfoInvalid
	}
	if resp.Ret != 0 {
		xlog.FromContext(ctx).Error("GetUserInfo request pigeon failed", zap.Any("nid", nid), zap.Any("result", resp))
		return false, constant.ErrUserInfoInvalid
	}

	return true, nil
}

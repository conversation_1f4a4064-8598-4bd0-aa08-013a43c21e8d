package rpc

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"risk/config"
	"risk/constant"
	"risk/rpc/proto"
	"risk/utils"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func CometBroadcast(ctx context.Context, clientid, nid, payload string) error {
	conn, err := grpc.Dial(config.Get().CometConfig.Url,
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		xlog.FromContext(ctx).Error("SendComet grpc.Dial failed",
			xlog.String("clientid", clientid),
			xlog.String("nid", nid),
			xlog.String("payload", payload),
			xlog.Err(err))
		return constant.ErrCometDialFail
	}
	defer conn.Close()
	client := proto.NewCometGraphServiceClient(conn)

	var (
		channel = "sdk#" + clientid + ":" + nid + "/rw"
		module  = "risk"
	)
	msg := StreamMessage{
		Header: &Header{
			Command: CmdTypeMsg,
			Option: Option{
				Channel: channel,
				Module:  module,
			},
			Version:   CometVer,
			RequestId: xnet.GetRequestId(xgin.FromContext(ctx).Request),
		},
		Payload: []byte(payload),
	}
	b, err := msg.ToBytes()
	if err != nil {
		xlog.FromContext(ctx).Error("SendComet msg.ToBytes failed", xlog.Any("msg", msg), xlog.Err(err))
		return constant.ErrJsonMarshalFail
	}
	xlog.FromContext(ctx).Info("SendComet", xlog.ByteString("msg", b))
	request := &proto.BroadcastRequest{
		Message: b,
	}
	_, err = client.Broadcast(utils.GrpcSetContext(ctx), request)
	if err != nil {
		xlog.FromContext(ctx).Error("SendComet client.Broadcast failed", xlog.Any("msg", msg), xlog.Err(err))
		return constant.ErrCometBroadcastFail
	}

	return nil
}

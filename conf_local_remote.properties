host = :8091
inner_host = :8089
register = false
domain = https://risk-api-dev.papegames.com
sign_verify_ignores = ["/v1/risk/gs/check","/v1/risk/ad/check","/v1/risk/payment/check","/v1/risk/app/check","/v1/risk/account/check","/v1/risk/tob","/v1/risk/sd/get","/v1/risk/sd/config","/v1/risk/biz/init","/v1/risk/ds/check","/v1/health","/v1/risk/ad/check"]
debug = true
crypto.ignore_paths = ["/v1/risk/gs/check","/v1/risk/sd/get","/v1/risk/sd/config","/v1/risk/biz/init","/v1/risk/ds/check"]
crypto.ignore_app_ids = ["pc","psn","1010026"]
crypto.force = false
crypto.close = true
crypto.gray_scale_percentage = 0
sparrow.log.color = true
sparrow.log.file = stdout
sparrow.log.level = debug
sparrow.log.encoding = stdout
sparrow.log.buffer = 4096
sparrow.log.rotate = daily
sparrow.govern.enable = true
sparrow.govern.host = 0.0.0.0:9091
sparrow.registrar.prefix = /sparrow/sd/
sparrow.registrar.addrs = [127.0.0.1:2379]
sparrow.database.mysql.dataSource = _vault(niD+qNgwo3mE2tzDcfMxPfs2HaKxVGGfbNV9W7J7SqOhtK57JRRuUxWgKtTcgLbAKEGHh9Fu1V8jKXIqezhO9lQ7bdNYsNDeY4K0/zbpzyXuI9GuJVkx6XCOaQCAUnVe8V3GGcH5wur9oQLua3KtsQ==)_
sparrow.database.mysql.maxIdleConns = 5
sparrow.database.mysql.maxOpenConns = 10
sparrow.database.verifydb.dataSource = root:BozsCMdcrj@(************:3306)/verify?timeout=5s&parseTime=true&loc=Local&charset=utf8
sparrow.database.verifydb.maxIdleConns = 5
sparrow.database.verifydb.maxOpenConns = 5
sparrow.database.accessoriesdbreadconf.dataSource = root:BozsCMdcrj@(************:3306)/naccessories?timeout=5s&parseTime=true&loc=Local&charset=utf8
sparrow.database.accessoriesdbreadconf.maxIdleConns = 5
sparrow.database.accessoriesdbreadconf.maxOpenConns = 5
machine.geetest.api_uri = http://gcaptcha4.geetest.com/validate
machine.geetest.captcha_id = 6aea8087471f762316431f24d83a142c
machine.geetest.captcha_key = a27b9d75447d91ca370a12a738b94362
machine.nonce_expire = 3600
machine.verify_expire = 10800
machine.force_app_id = []
dark.expire.black = 604800
dark.expire.white = 604800
dark.ishumei.is_open = 1
dark.ishumei.uri = http://api-tianxiang-bj.fengkongcloud.com/tianxiang/v4
dark.ishumei.access_key = 1tbxeHYLikYsIsk519KG
dark.aliyun.is_open = 1
dark.aliyun.app_id = LTAI5tSLGGuVxSQW7RyV6htE
dark.aliyun.app_secret = ******************************
dark.aliyun.ip_risk.risk_source = 80
dark.aliyun.device_risk.tags = rw_0101,rw_0103,rw_0104,rw_0108,is_openVpn,time_over,token_invalid,token_replay,token_tampered
sparrow.database.redis.addr = ************:6379
sparrow.trace.async = true
sparrow.trace.enable = false
sparrow.trace.endpoint = tracing-analysis-dc-hz.aliyuncs.com:8090
sparrow.trace.headers.authentication = fj6bj4wuyv@cffa970a60fc7ef_fj6bj4wuyv@53df7ad2afe8301
cache.type = memory
cache.cap = 10000000
cache.doid_expire = 15552000
device_provider.ishumei.device_profile_uri = http://fp-proxy.fengkongcloud.com/deviceprofile/v4
device_provider.ishumei.cloud_conf_uri = http://fp-proxy.fengkongcloud.com/v3/cloudconf
device_provider.ishumei.agency = true

pigeon.gmhost = http://api-dev.papegames.com:12102
pigeon.host = https://api-dev.papegames.com:12101
email.is_open = true
sms.is_open = true
comet.url = comet-graph-test.papegames.com:8080
doid_check = true
grpc_strategy.live_tags_uri.host = livetags
grpc_strategy.live_tags_uri.port = 8090
grpc_strategy.score_uri.host = risk-score-engine
grpc_strategy.score_uri.port = 8092
grpc_strategy.evaluation_uri.host = risk-evaluation-engine
grpc_strategy.evaluation_uri.port = 8092
grpc_strategy.idle_timeout = 30
grpc_strategy.timeout = 100
sparrow.broker.kafka.bi.writer.Brokers = alikafka-pre-cn-nwy3i48px00a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-nwy3i48px00a-3-vpc.alikafka.aliyuncs.com:9092
sparrow.broker.kafka.bi.writer.Topic = tlog_json_risk_gameqos_test
code_chk_action = ["token_refresh","login"]

sparrow.watch.KeyPath = sparrow.watch.config
sparrow.watch.config.endpoints = *************:2379


sparrow.broker.kafka.risk.writer.Brokers = ************:9092
sparrow.broker.kafka.risk.writer.Topic = risk-doid
sparrow.broker.kafka.risk.reader.Brokers = ************:9092
sparrow.broker.kafka.risk.reader.Topic = risk-doid
sparrow.broker.kafka.risk.reader.GroupID = risk_service

close_risk.enter_game_conf.client_id = [1008]
close_risk.enter_game_conf.http_code = 403
close_risk.enter_game_conf.enter_game_timeout = 5

doid_check_db_disable = false
black_check_disable = false

machine.geetest.device_check_app_id = ["gSzx4NMA"]]
machine.geetest.captcha_check_app_id = 
machine.geetest.disable_check_ip = true
machine.geetest.disable_check_risk_device = true


grpc_strategy.ad_black_list_uri.host = risk-ad-black
grpc_strategy.ad_black_list_uri.port = 8090

doid_check_games = [2008]

dark.aliyun.face_scene_id = **********
dark.aliyun.face_code_expire = 1800
dark.aliyun.face_token_expire = 43200
dark.aliyun.face_verify_failed_num = 5
dark.aliyun.face_verify_failed_expire = 86400
dark.aliyun.face_verify_model = PHOTINUS_LIVENESS

dark.aliyun.face_scene_ids.account_real_info = {"id":**********,"model":"PHOTINUS_LIVENESS"}
dark.aliyun.face_code_risks = {"********":[{"dimension":"phone","window_time":120,"rate_limit":5},{"dimension":"id_card","window_time":120,"rate_limit":10}]}
dark.aliyun.face_web_code_close = {"1010026":false}

options = {"face_verify_agreement":[{"name":"《个人信息处理授权书》","url":"https://reg.papegames.com/m/contract?key=authContract1"},{"name":"《叠纸账号隐私协议》","url":"https://www.papegames.com/contract?key=accountPrivacy"}],"face_verify_toast":"为确保是本人操作,请您完成人脸认证","face_verify_title":"您将使用以下身份信息进行人脸识别","face_verify_subtitle":"请确保为本人操作"}

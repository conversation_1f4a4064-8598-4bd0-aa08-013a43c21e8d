apiVersion: v1
data:
  conf.yaml: |
    sparrow:
      configure:
        provider: "apollo"
        watch: true
        path: "application"
        config:
          endpoints:
            - "http://10.149.52.104:8080"
          appID: "pt-risk"
          cluster: "default"
          secret: "12c2b0bae4f847938a18164220d28af4"
kind: ConfigMap
metadata:
  name: pt-risk
  namespace: platsdk
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: pt-risk
  name: pt-risk
  namespace: platsdk
spec:
  ports:
    - name: http
      port: 8091
      protocol: TCP
      targetPort: http
    - name: inner
      port: 8089
      protocol: TCP
      targetPort: inner
  selector:
    app: pt-risk
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: pt-risk
  name: pt-risk
  namespace: platsdk
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pt-risk
  template:
    metadata:
      annotations:
        k8s.aliyun.com/eci-eviction-enable: 'true'
        k8s.aliyun.com/eci-extra-ephemeral-storage: 100G
        prometheus.io/path: /metrics
        prometheus.io/port: '4006'
        prometheus.io/scrape: 'true'
        sidecar.istio.io/inject: 'false'
      labels:
        app: pt-risk
    spec:
      containers:
        - env:
            - name: APP_NAME
              value: pt-risk
            - name: APP_ENV
              value: dev
            - name: ROLE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['role']
            - name: TZ
              value: Asia/Shanghai
          image: dev-registry-vpc.cn-hangzhou.cr.aliyuncs.com/platform/pt-risk:VERSION
          imagePullPolicy: Always
          livenessProbe:
            initialDelaySeconds: 45
            periodSeconds: 12
            tcpSocket:
              port: 8091
            timeoutSeconds: 5
          name: pt-risk
          ports:
            - containerPort: 8091
              name: http
              protocol: TCP
            - containerPort: 8089
              name: inner
              protocol: TCP
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            tcpSocket:
              port: 8091
            timeoutSeconds: 5
          securityContext: { }
          volumeMounts:
            - mountPath: /data/logs
              name: k8s-log
            - mountPath: /app/conf.yaml
              name: pt-risk
              subPath: conf.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: lingyun
      restartPolicy: Always
      securityContext: { }
      serviceAccountName: default
      terminationGracePeriodSeconds: 45
      volumes:
        - emptyDir: { }
          name: k8s-log
        - configMap:
            name: pt-risk
          name: pt-risk
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: pt-risk
  namespace: netops
spec:
  gateways:
    - common-inbound-gateway
  hosts:
    - risk-api.papegames.it
    - risk-api-dev.papegames.com
  http:
    - route:
        - destination:
            host: pt-risk.platsdk.svc.cluster.local
            port:
              number: 8091
          weight: 100
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: pt-risk-inner
  namespace: netops
spec:
  gateways:
    - common-inbound-gateway
  hosts:
    - risk-api-inner.papegames.it
    - risk-api-inner-dev.papegames.com
  http:
    - route:
        - destination:
            host: pt-risk.platsdk.svc.cluster.local
            port:
              number: 8089
          weight: 100
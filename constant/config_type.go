package constant

const (

	// 11 登录常用设备 12 登录常在IP 13 是否开启人机校验 14 全局开关
	// 101 账号支付成功率 102 IP支付成功率 103 设备支付成功率
	// 201 账号频率 202 IP频率 203 设备频率
	// 301 黑产IP 302 黑产设备
	// 401 ip请求间隔 402 设备请求间隔 403 账号请求间隔
	// 501 ip黑名单检查 502 设备黑名单检查  503 账号黑名单检查
	// 601 ip白名单检查 602 设备白名单检查 603 账号白名单检查

	ChkLoginAlwaysDevice uint16 = 11 //登录常用设备
	ChkLoginAlwaysIP     uint16 = 12 //登录常在IP
	ChkOpenCaptcha       uint16 = 13 //是否开启人机校验
	ChkAll               uint16 = 14 //全局开关

	//支付成功率
	ChkPayAccountSuccess uint16 = 101 //账号支付成功率
	ChkPayIPSuccess      uint16 = 102 //IP支付成功率
	ChkPayDeviceSuccess  uint16 = 103 //设备支付成功率

	//频率
	ChkActionAccountFrequency uint16 = 201 //账号频率
	ChkActionIPFrequency      uint16 = 202 //IP频率
	ChkActionDeviceFrequency  uint16 = 203 //设备频率

	//黑产
	ChkDarkIP     uint16 = 301 //黑产IP检查
	ChkDarkDevice uint16 = 302 //黑产设备检查

	//请求间隔
	ChkIntervalIp      uint16 = 401 //ip请求间隔
	ChkIntervalDevice  uint16 = 402 //设备请求间隔
	ChkIntervalAccount uint16 = 403 //账号请求间隔

	//黑名单检查
	ChkBlackListIp      uint16 = 501 //ip黑名单检查
	ChkBlackListDevice  uint16 = 502 //设备黑名单检查
	ChkBlackListAccount uint16 = 503 //账号黑名单检查

	//白名单检查
	ChkWhiteListIp      uint16 = 601 //ip白名单检查
	ChkWhiteListDevice  uint16 = 602 //设备白名单检查
	ChkWhiteListAccount uint16 = 603 //账号白名单检查
)

const (
	Disable = iota
	Enable
)

const (
	DataAccountType = 1
	DataIPType      = 2
	DataDeviceType  = 3
)

const ActionCheckDOID = "check_DOID"

const (
	DOIDNone     = "NONE"
	DOIDAbnormal = "device_doid_exception_format_invaid"
)

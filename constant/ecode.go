package constant

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	necode "gitlab.papegames.com/nuclear/ecode"
)

var ErrParam = ecode.New(51001, "参数错误！")
var ErrId = ecode.New(51002, "验证已过期，请刷新页面")
var ErrAuth = ecode.New(50010, "需要二次认证！")
var ErrNoPass = ecode.New(50011, "验证不通过!")
var ErrAppId = ecode.New(50012, "app id错误！")
var ErrDBCode = ecode.New(50013, "数据库错误！")
var ErrFaceVerifyFailed = ecode.New(50014, "人脸识别失败次数过多！")
var ErrFaceCodeErr = ecode.New(50016, "code已过期或无效，请重新发起！")
var ErrFaceRisk = ecode.New(50017, "检测到风险异常，建议检查当前设备环境")

var ErrRiskCaptcha = necode.RetErrRiskCaptchaCheck
var ErrRiskSms = necode.RetErrRiskSMSCheck
var ErrRiskEmail = necode.RetErrRiskEmailCheck
var ErrRiskFace = necode.RetErrRiskFaceCheck
var ErrRiskReject = necode.RetErrRiskBlockCheck
var ErrRiskLogout = necode.RetErrRiskRefreshToken
var ErrRiskForceBind = necode.RetErrRiskPhoneForceBind

var (
	// 白名单已存在
	ErrWhiteListExist = ecode.New(60001, "白名单已存在")
	// 白名单不存在
	ErrWhiteListNotExist = ecode.New(60002, "白名单不存在")
	// 白名单添加失败
	ErrWhiteListAddFail = ecode.New(60003, "白名单添加失败")
	// 白名单更新失败
	ErrWhiteListUpdateFail = ecode.New(60004, "白名单更新失败")
	// 白名单删除失败
	ErrWhiteListDeleteFail = ecode.New(60005, "白名单删除失败")
	// 白名单查询失败
	ErrWhiteListQueryFail = ecode.New(60006, "白名单查询失败")

	// 黑名单已存在
	ErrBlackListExist = ecode.New(60011, "黑名单已存在")
	// 黑名单不存在
	ErrBlackListNotExist = ecode.New(60012, "黑名单不存在")
	// 黑名单添加失败
	ErrBlackListAddFail = ecode.New(60013, "黑名单添加失败")
	// 黑名单更新失败
	ErrBlackListUpdateFail = ecode.New(60014, "黑名单更新失败")
	// 黑名单删除失败
	ErrBlackListDeleteFail = ecode.New(60015, "黑名单删除失败")
	// 黑名单查询失败
	ErrBlackListQueryFail = ecode.New(60016, "黑名单查询失败")

	// 风控开关已存在
	ErrClientConfigExist = ecode.New(60021, "风控开关已存在")
	// 风控开关不存在
	ErrClientConfigNotExist = ecode.New(60022, "风控开关不存在")
	// 风控开关添加失败
	ErrClientConfigAddFail = ecode.New(60023, "风控开关添加失败")
	// 风控开关更新失败
	ErrClientConfigUpdateFail = ecode.New(60024, "风控开关更新失败")
	// 风控开关删除失败
	ErrClientConfigDeleteFail = ecode.New(60025, "风控开关删除失败")
	// 风控开关查询失败
	ErrClientConfigQueryFail = ecode.New(60026, "风控开关查询失败")

	// 黑产已存在
	ErrDarkListExist = ecode.New(60031, "黑产已存在")
	// 黑产不存在
	ErrDarkListNotExist = ecode.New(60032, "黑产不存在")
	// 黑产添加失败
	ErrDarkListAddFail = ecode.New(60033, "黑产添加失败")
	// 黑产更新失败
	ErrDarkListUpdateFail = ecode.New(60034, "黑产更新失败")
	// 黑产删除失败
	ErrDarkListDeleteFail = ecode.New(60035, "黑产删除失败")
	// 黑产查询失败
	ErrDarkListQueryFail = ecode.New(60036, "黑产查询失败")

	// 验证码配置不存在
	ErrGeetestConfigNotFound = ecode.New(60041, "验证码配置不存在")

	// 手机验证配置不存在
	ErrSmsNonceNotFound = ecode.New(60052, "手机号验证失败")
	// 手机验证手机号不匹配
	ErrSmsPhoneNotMatch = ecode.New(60054, "验证失败")
	// 手机验证验证码不匹配
	ErrSmsCodeNotMatch = ecode.New(60056, "短信验证失败")
	// 手机验证码发送失败
	ErrSmsSendFail = ecode.New(60057, "验证码发送失败，请联系客服！")
	// 手机验证码校验失败
	ErrSmsCheckFail = ecode.New(60059, "短信验证码错误")

	// 邮箱验证配置不存在
	ErrEmailNonceNotFound = ecode.New(60061, "邮箱验证信息不存在")
	// 邮箱验证配置不合法
	ErrEmailNonceInvalid = ecode.New(60062, "邮箱验证信息不合法")
	// 邮箱验证邮箱不匹配
	ErrEmailAddrNotMatch = ecode.New(60063, "邮箱验证邮箱不匹配")
	// 邮箱验证验证码过期
	ErrEmailCodeExpired = ecode.New(60064, "邮箱验证验证码过期")
	// 邮箱验证验证码不匹配
	ErrEmailCodeNotMatch = ecode.New(60065, "邮箱验证验证码不匹配")
	// 邮箱验证码发送失败
	ErrEmailSendFail = ecode.New(60066, "邮箱验证码发送失败")

	// 获取用户信息失败
	ErrGetUserInfoFail = ecode.New(60071, "获取用户信息失败")
	// 用户信息不合法
	ErrUserInfoInvalid = ecode.New(60072, "用户信息不合法")

	ErrCometDialFail      = ecode.New(60081, "comet dial fail")
	ErrCometBroadcastFail = ecode.New(60082, "comet broadcast fail")

	ErrJsonMarshalFail = ecode.New(60091, "json marshal fail")

	ErrFaceWebCodeClose = ecode.New(60101, "人脸识别服务已关闭")
)

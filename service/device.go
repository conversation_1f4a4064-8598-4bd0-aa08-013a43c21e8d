package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/jaevor/go-nanoid"
	"github.com/redis/go-redis/v9"
	"github.com/segmentio/kafka-go"
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"math/rand"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/repo/device"
	"risk/startup"
	"risk/utils"
	"strconv"
	"strings"
	"time"
)

type Device struct {
	ctx    context.Context
	db     *xgorm.DB
	logger *xlog.Logger
}

func NewDevice(ctx context.Context) *Device {
	return &Device{
		ctx:    ctx,
		db:     startup.GetMySql().WithContext(ctx),
		logger: xlog.FromContext(ctx),
	}
}
func (d Device) GenDOID() (string, error) {
	canonicID, err := nanoid.Custom("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz", 36)
	if err != nil {
		return "", err
	}
	return canonicID(), nil
}

// SDKDOIDCheck
/*
  1. device_id进行md5计算取大写md5_device_id；
  2. 取md5_device_id的6到29位，得到一个24位的split_device_id_24；
  3. 从split_device_id随机从中取不连续的4个字符，得到一个4位的rand_chars；并记录4个字符在split_device_id中的位置，得到一个4位的insertion_positions；
  4. insertion_positions对应的数字用A-X表示1-23位，得到一个4位的insertion_positions_chars;
  5. 将insertion_positions_chars四个字符，分别插入split_device_id的6、11、19、23位上，得到一个28位的inserted_device_id_28；
  6. 将rand_chars的4位字符分别与insertion_positions对应的位数进行异或计算，结果进行16进制转换取最后一位，得到一个4位的non_consecutive_chars;
  7. 将non_consecutive_chars分别插入inserted_device_id_28的8、19、25与28位，得到一个32位的字符就是DOID；
*/
func (d Device) GenSDKDOID(deviceID string) string {
	// Step 1: MD5 calculation
	hasher := md5.New()
	hasher.Write([]byte(deviceID))
	md5Hash := strings.ToUpper(hex.EncodeToString(hasher.Sum(nil)))
	d.logger.Info("Step 1:\tmd5Hash data:", zap.String("value", md5Hash))

	// Step 2: Get 24 characters from 6 to 29
	splitDeviceID24 := md5Hash[6:30]
	d.logger.Info("Step 2:\tsplitDeviceID 24 data:", zap.String("value", splitDeviceID24))

	// Step 3: Get random 4 non-consecutive characters
	rand.Seed(time.Now().UnixNano())
	var randChars string
	var insertionPositions []int
	for i := 0; i < 4; i++ {
		index := rand.Intn(len(splitDeviceID24))
		randChars += string(splitDeviceID24[index])
		insertionPositions = append(insertionPositions, index)
	}

	d.logger.Info("Step 3:\trandChars data:", zap.String("value", randChars))
	d.logger.Info("Step 3:\tpositions data:", zap.Any("value", insertionPositions))

	return d.randDOID(splitDeviceID24, insertionPositions)
}

func (d Device) randDOID(splitDeviceID24 string, insertionPositions []int) string {

	var randChars string
	for i := 0; i < 4; i++ {
		index := insertionPositions[i]
		if index > len(splitDeviceID24) {
			return ""
		}
		randChars += string(splitDeviceID24[index])
	}

	// Step 4: Convert insertion positions to chars
	insertionPositionsChars := ""
	for _, pos := range insertionPositions {
		posInt := int(pos) + 65 // Convert to ASCII uppercase letter
		insertionPositionsChars += string(rune(posInt))
	}

	d.logger.Info("Step 4:\tinsertionPositionsChars data:", zap.String("value", insertionPositionsChars))
	// Step 5: Insert insertion positions chars to specific positions
	insertedDeviceID28 := splitDeviceID24[:5] + insertionPositionsChars[0:1] +
		splitDeviceID24[5:10] + insertionPositionsChars[1:2] +
		splitDeviceID24[10:18] + insertionPositionsChars[2:3] +
		splitDeviceID24[18:22] + insertionPositionsChars[3:4] + splitDeviceID24[22:]

	d.logger.Info("Step 5:\tinsertedDeviceID 28 data:", zap.String("value", insertedDeviceID28))

	// Step 6: XOR calculation and hexadecimal conversion
	nonConsecutiveChars := ""
	for i := 0; i < 4; i++ {
		xorResult := randChars[i] ^ insertionPositionsChars[i]
		d.logger.Info("Step 6\txorResult data:", zap.Uint8("value", xorResult))
		xorHex := fmt.Sprintf("%X", xorResult)
		nonConsecutiveChars += xorHex[len(xorHex)-1:]
		d.logger.Info("Step 6\txorHex data:", zap.String("value", xorHex))
	}

	d.logger.Info("Step 6\tnonConsecutiveChars data:", zap.String("value", nonConsecutiveChars))

	// Step 7: Insert non-consecutive chars to specific positions
	DOID := insertedDeviceID28[:7] + nonConsecutiveChars[0:1] +
		insertedDeviceID28[7:18] + nonConsecutiveChars[1:2] +
		insertedDeviceID28[18:24] + nonConsecutiveChars[2:3] +
		insertedDeviceID28[24:27] + nonConsecutiveChars[3:4] + insertedDeviceID28[27:]

	d.logger.Info("Step 7:\tDOID data:", zap.String("value", DOID))

	return DOID
}

// 验证DOID
func (d Device) SDKDOIDCheck(DOID string) (string, error) {
	if len(DOID) != 32 {
		return "", errors.New("DOID length error")
	} else if DOID != strings.ToUpper(DOID) {
		return "", errors.New("DOID format error")
	}

	log := ""
	d.logger.Info("DOID data:", zap.String("value", DOID))
	log = fmt.Sprintf("DOID data: %s", DOID)

	// Step 1: Get non_consecutive_chars
	nonConsecutiveChars := string(DOID[7]) + string(DOID[19]) + string(DOID[26]) + string(DOID[30])
	d.logger.Info("Step 1:\tnonConsecutiveChars data:", zap.String("value", nonConsecutiveChars))
	log = fmt.Sprintf("%s Step 1: nonConsecutiveChars: %s", log, nonConsecutiveChars)
	log = fmt.Sprintf("%s  在DOID的位置：7，19，26，30", log)
	// Step 2: Get insertion_positions_chars
	insertionPositionsChars := string(DOID[5]) + string(DOID[12]) + string(DOID[22]) + string(DOID[28])
	d.logger.Info("Step 2:\tinsertionPositionsChars data:", zap.String("value", insertionPositionsChars))
	log = fmt.Sprintf("%s Step 2:insertionPositionsChars: %s", log, insertionPositionsChars)
	log = fmt.Sprintf("%s  在DOID的位置：5，12，22，28", log)

	// Step 3: Get splitDeviceID24
	splitDeviceID24 := DOID[:5] + DOID[6:7] + DOID[8:12] + DOID[13:19] + DOID[20:22] + DOID[23:26] + DOID[27:28] + DOID[29:30] + DOID[31:]
	d.logger.Info("Step 3:\tsplitDeviceID24 data:", zap.String("value", splitDeviceID24))
	log = fmt.Sprintf("%s Step 3:splitDeviceID24: %s", log, splitDeviceID24)

	var insertionPositions []int
	for _, char := range insertionPositionsChars {
		pos := char - 65
		insertionPositions = append(insertionPositions, int(pos))
	}

	d.logger.Info("Step 3:\tpositions data:", zap.Ints("value", insertionPositions))
	log = fmt.Sprintf("%s Step 4: positions: %v", log, insertionPositions)

	genDOID := d.randDOID(splitDeviceID24, insertionPositions)
	if genDOID != DOID {
		xlog.Warn("DOID check failed", zap.String("genDOID", genDOID), zap.String("DOID", DOID))
		log = fmt.Sprintf("%s DOID check failed, genDOID: %s, DOID: %s", log, genDOID, DOID)
		errMsg := "DOID check failed"
		if config.Get().Debug {
			errMsg = fmt.Sprintf("DOID check failed, step: %v", log)
		}
		return "", errors.New(errMsg)
	}
	return splitDeviceID24, nil
}

func (d Device) ThirdSDKCreate(body, ip, os, providerName string) (*proto.DeviceGetResponseData, error) {
	pmd := model.RiskDeviceProvider{}
	providerRepo := device.NewDevice(providerName)

	deviceInfo, err := providerRepo.DecryptBody(d.ctx, os, body)
	if err != nil {
		d.logger.Error("DecryptBody error", zap.Error(err))
		return nil, err
	}

	thirdDeviceData, err := providerRepo.GetDeviceId(d.ctx, body)
	if err != nil {
		d.logger.Error("GetDeviceId error", zap.Error(err))
		return nil, err
	}
	d.logger.Info("GetDeviceId success", zap.Any("thirdDeviceData", thirdDeviceData))

	//异步写入
	go func() {
		d.logger.Info("Add risk device provider", zap.String("providerName", providerName), zap.Any("thirdDeviceData", thirdDeviceData))
		if err := pmd.FirstOrCreateByThirdDeviceID(providerName, "", thirdDeviceData.ThirdDeviceId, thirdDeviceData.ThirdData, deviceInfo.ThirdInfo, deviceInfo.DeviceId, d.db); err != nil {
			d.logger.Error("Add risk device provider error", zap.Error(err))
		}
	}()

	return &proto.DeviceGetResponseData{
		Code:      1100,
		RequestId: strconv.FormatInt(time.Now().Unix(), 10),
		Detail: &proto.Detail{
			DeviceId: thirdDeviceData.ThirdData,
		},
	}, nil
}

func (d Device) SDKCreateDOID(thirdDeviceIds, information, ip string) (*proto.DeviceGetResponse, error) {
	var informationJson proto.SDKDeviceInfo
	if err := sonic.UnmarshalString(information, &informationJson); err != nil {
		d.logger.Error("Unmarshal error", zap.Error(err))
		if config.Get().Debug {
			return nil, ecode.Error(ecode.BadRequest, "Unmarshal error")
		} else {
			return nil, ecode.BadRequest
		}
	} else if informationJson.Os == "" || informationJson.DeviceId == "" {
		d.logger.Error("informationJson error", zap.Any("informationJson", &informationJson))
		if config.Get().Debug {
			return nil, ecode.Error(ecode.BadRequest, "os or device_id is empty")
		} else {
			return nil, ecode.BadRequest
		}
	}

	d.logger.Info("informationJson", zap.Any("data", &informationJson))
	var SdkDeviceId utils.SdkDeviceId
	deviceId, err := SdkDeviceId.GetDeviceId(information)
	if err != nil {
		d.logger.Error("GetDeviceId error", zap.Error(err))
		if config.Get().Debug {
			return nil, ecode.Error(ecode.ServerError, err.Error())
		} else {
			return nil, ecode.ServerError
		}
	}

	var thirdDeviceId string
	var thirdDeviceIdStr proto.ThirdDeviceIdData

	if err := sonic.Unmarshal([]byte(thirdDeviceIds), &thirdDeviceIdStr); err != nil {
		d.logger.Error("Unmarshal error", zap.Error(err))
		return nil, err
	}

	d.logger.Info("thirdDeviceIdStr", zap.Any("data", &thirdDeviceIdStr))

	var providerName string
	var isDark bool

	if len(thirdDeviceIdStr.GetShumei()) > 0 {
		if len(thirdDeviceIdStr.GetShumei()) != 89 || thirdDeviceIdStr.GetShumei()[0] != 'B' {
			isDark = true
		} else {
			var shumei device.Shumei //数美
			thirdDeviceId, err = shumei.OutputBoxId(thirdDeviceIdStr.GetShumei())
			if err != nil {
				d.logger.Error("OutputBoxId error", zap.Error(err))
				return nil, err
			}
			providerName = "shumei"
		}
	}

	if thirdDeviceId == "" {
		if thirdDeviceIdStr.GetFingerprint() != "" {
			//web
			thirdDeviceId = thirdDeviceIdStr.GetFingerprint()
			providerName = "fingerprint"
		} else if thirdDeviceIdStr.GetSdk_DOID() != "" {
			//sdk
			thirdDeviceId = deviceId
			providerName = "sdk"
		} else {
			if config.Get().Debug {
				return nil, ecode.Error(ecode.BadRequest, "thirdDeviceId is empty")
			} else {
				return nil, ecode.BadRequest
			}
		}
	}

	providerRdbKey := fmt.Sprintf("%s%s:%s", constant.RdbRrovideDOID, providerName, thirdDeviceId)
	deviceIdRdbKey := fmt.Sprintf("%s%s:%s", constant.RdbRrovideDOID, "device_id", deviceId)
	DOIDByProvider := startup.GetRedis().Get(d.ctx, providerRdbKey).Val()
	DOIDByDeviceId := startup.GetRedis().Get(d.ctx, deviceIdRdbKey).Val()

	var DOID string
	if DOIDByProvider != "" {
		d.logger.Info("DOIDByProvider", zap.String("DOIDByProvider", DOIDByProvider))
		DOID = DOIDByProvider
	} else if DOIDByDeviceId != "" {
		d.logger.Info("DOIDByDeviceId", zap.String("DOIDByDeviceId", DOIDByDeviceId))
		DOID = DOIDByDeviceId
	}

	if DOID != "" {
		//对应的SDK DOID不一致新增一条
		_ = d.insertSdkDOID(thirdDeviceIdStr.Sdk_DOID, DOID)
		startup.GetRedis().SetEx(d.ctx, deviceIdRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
		return &proto.DeviceGetResponse{DOID: DOID}, nil
	}

	if !config.Get().DOIDCheckDBDisable {
		pmd := model.RiskDeviceProvider{}
		md := model.RiskDevice{}
		pInfo, err := pmd.FindByProviderAndThirdDeviceID(providerName, thirdDeviceId, d.db)
		if err != nil {
			d.logger.Error("FindByProviderAndThirdDeviceID error", zap.Error(err))
			return nil, err
		} else if pInfo != nil && pInfo.DOID != "" {
			//对应的SDK DOID不一致新增一条
			_ = d.insertSdkDOID(thirdDeviceIdStr.Sdk_DOID, DOID)
			startup.GetRedis().SetEx(d.ctx, providerRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
			startup.GetRedis().SetEx(d.ctx, deviceIdRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
			return &proto.DeviceGetResponse{DOID: pInfo.DOID}, nil
		}

		dInfo, err := md.FindByOSAndDeviceId(SdkDeviceId.Os, deviceId, d.db)
		if err != nil {
			d.logger.Error("FindByDeviceID error", zap.Error(err))
			return nil, err
		} else if dInfo != nil && dInfo.DOID != "" {
			_ = d.insertSdkDOID(thirdDeviceIdStr.Sdk_DOID, DOID)
			startup.GetRedis().SetEx(d.ctx, providerRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
			startup.GetRedis().SetEx(d.ctx, deviceIdRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
			return &proto.DeviceGetResponse{DOID: dInfo.DOID}, nil
		}

	}

	DOID, err = d.GenDOID()
	if err != nil {
		d.logger.Error("GenDOID error", zap.Error(err))
		return nil, err
	}

	DOIDRdbKey := fmt.Sprintf("%s%s", constant.RdbDOID, DOID)
	rdb := startup.GetRedis()
	pipeline := rdb.Pipeline()

	pipeline.SetEx(d.ctx, providerRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
	pipeline.SetEx(d.ctx, deviceIdRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
	pipeline.SetEx(d.ctx, DOIDRdbKey, 1, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
	if thirdDeviceIdStr.Sdk_DOID != "" {
		SdkDOIDRdbKey := fmt.Sprintf("%s%s", constant.RdbSdkDOID, thirdDeviceIdStr.Sdk_DOID)
		pipeline.SetEx(d.ctx, SdkDOIDRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
	}
	cmders, err := pipeline.Exec(d.ctx)
	if err != nil {
		d.logger.Error("Exec redis providerRdbKey error", zap.Error(err))
		return nil, err
	}

	for _, cmder := range cmders {
		cmd := cmder.(*redis.StatusCmd)
		_, err := cmd.Result()
		if err != nil {
			d.logger.Error("Exec redis providerRdbKey error", zap.Error(err))
			return nil, err
		}
	}

	informationJson.IsDark = isDark
	//写入kafka
	data := &proto.CreateDOID{
		SDKDeviceInfo:     &informationJson,
		ProviderName:      providerName,
		ThirdDeviceId:     thirdDeviceId,
		ThirdDeviceIdData: &thirdDeviceIdStr,
		DOID:              DOID,
		Ip:                ip,
		DeviceId:          deviceId,
	}
	b, err := sonic.Marshal(data)
	if err != nil {
		d.logger.Error("Marshal error", zap.Error(err))
		return nil, err
	}

	d.logger.Info("kafka send msg", zap.Any("data", data))

	if err := startup.GetRiskKafkaWriter().
		WriteMessages(d.ctx, xkafka.Message{
			Value: b,
			Headers: []kafka.Header{
				{Key: constant.KafkaDOIDKey, Value: []byte(constant.KafkaDOIDKey)},
			},
		}); err != nil {
		d.logger.Error("kafka WriteMessages error", zap.Error(err))
	}

	return &proto.DeviceGetResponse{DOID: DOID}, nil
}

func (d Device) insertSdkDOID(sdkDOID, DOID string) error {
	if sdkDOID == "" {
		return nil
	}
	rdb := startup.GetRedis()
	SdkDOIDRdbKey := fmt.Sprintf("%s%s", constant.RdbSdkDOID, sdkDOID)
	isExist, err := rdb.Exists(d.ctx, SdkDOIDRdbKey).Result()
	if err != nil {
		d.logger.Error("Get error", zap.Error(err))
		return err
	} else if isExist == 1 {
		d.logger.Info("Get is not empty", zap.String("key", SdkDOIDRdbKey))
		return nil
	}

	ok, err := rdb.SetEx(d.ctx, SdkDOIDRdbKey, DOID, time.Duration(config.Get().Cache.DOIDExpire)*time.Second).Result()
	if err != nil {
		d.logger.Error("SetEx error", zap.Error(err))
		return err
	} else if strings.ToLower(ok) != "ok" {
		d.logger.Error("SetEx is not ok！", zap.String("key", SdkDOIDRdbKey))
		return nil
	}
	data := &proto.CreateSdKDOID{DOID: DOID, SDKDOID: sdkDOID}

	b, err := sonic.Marshal(data)
	if err != nil {
		d.logger.Error("Marshal error", zap.Error(err))
		return err
	}
	if err := startup.GetRiskKafkaWriter().WriteMessages(d.ctx, xkafka.Message{
		Value: b,
		Headers: []kafka.Header{
			{Key: constant.KafkaSdkDOIDKey, Value: []byte(constant.KafkaSdkDOIDKey)},
		},
	}); err != nil {
		d.logger.Error("kafka WriteMessages error", zap.Error(err))
	}
	return nil
}

func (d Device) SDKDOIDToDOID(sdkDOID string) (string, error) {
	if sdkDOID == "" || len(sdkDOID) != 32 || strings.ToUpper(sdkDOID) != sdkDOID {
		d.logger.Warn("invalid sdkDOID", zap.String("sdkDOID", sdkDOID))
		return "", nil
	}

	md5DOID, err := d.SDKDOIDCheck(sdkDOID)
	if err != nil {
		d.logger.Error("SDK decode error", xlog.Err(err))
		return "", nil
	}

	md5DOIDRdbKey := fmt.Sprintf("%s%s", constant.RdbSDKMd5DeviceId, md5DOID)
	doid, err := startup.GetRedis().Get(d.ctx, md5DOIDRdbKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		d.logger.Error("Get error", zap.Error(err), zap.String("md5DOIDRdbKey", md5DOIDRdbKey))
		return "", err
	} else if doid != "" {
		return doid, nil
	}

	SdkToRdbKey := fmt.Sprintf("%s%s", constant.RdbDOIDToSDKDOID, sdkDOID)
	doid, err = startup.GetRedis().Get(d.ctx, SdkToRdbKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		d.logger.Error("Get error", zap.Error(err), zap.String("SdkToRdbKey", SdkToRdbKey))
		return "", err
	} else if doid != "" {
		return doid, nil
	}

	SdkDOIDRdbKey := fmt.Sprintf("%s%s", constant.RdbSdkDOID, sdkDOID)
	doid, err = startup.GetRedis().Get(d.ctx, SdkDOIDRdbKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		d.logger.Error("Get error", zap.Error(err), zap.String("SdkDOIDRdbKey", SdkDOIDRdbKey))
		return "", err
	} else if doid == "" {
		doid = sdkDOID
	}
	startup.GetRedis().Set(d.ctx, SdkToRdbKey, doid, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)
	startup.GetRedis().Set(d.ctx, md5DOIDRdbKey, doid, time.Duration(config.Get().Cache.DOIDExpire)*time.Second)

	return doid, nil
	//smd := model.RiskSdkDoid{}
	//db := startup.GetMySql().WithContext(d.ctx)
	//return smd.GetBySDKDOID(sdkDOID, db)
}

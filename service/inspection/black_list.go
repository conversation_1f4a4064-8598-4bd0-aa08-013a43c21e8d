package inspection

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"risk/constant"
	conf2 "risk/service/conf"
	"risk/utils"
	"risk/xmetric"
	"strconv"
)

func init() {

	InspectionList[constant.ChkBlackListAccount] = blackList{
		blackListType: constant.DataAccountType,
	}

	InspectionList[constant.ChkBlackListIp] = blackList{
		blackListType: constant.DataIPType,
	}

	InspectionList[constant.ChkBlackListDevice] = blackList{
		blackListType: constant.DataDeviceType,
	}
}

type blackList struct {
	blackListType uint8
}

func (b blackList) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, uid, action string) (bool, error) {
	if uid == "" {
		return true, nil
	}
	ctx, span := utils.GetSpan(ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appIds[0]))
	defer span.End()

	blackSvr := conf2.NewConfBlacklistSvr(ctx)

	isExist := blackSvr.OneExistAndNoExpire(uid, appIds, clientId, b.blackListType)
	if isExist {
		xmetric.Metrics.Inc("black", strconv.Itoa(int(b.blackListType)), "success")
		xlog.FromContext(ctx).Info(fmt.Sprintf("1 账号 2 IP 3 type:%v UID:%v app_id:%v client_id:%v 在黑名单内", b.blackListType, uid, appIds[0], clientId))
		return false, nil
	}

	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	return true, nil
}

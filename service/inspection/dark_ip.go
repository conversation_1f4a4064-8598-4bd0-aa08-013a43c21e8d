package inspection

import (
	"context"
	"errors"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/repo/dark"
	conf2 "risk/service/conf"
	dark2 "risk/service/dark"
	"risk/startup"
	"risk/utils"
	"risk/xmetric"
	"strconv"
	"time"
)

func init() {
	InspectionList[constant.ChkDarkIP] = darkIP{}
}

type darkIP struct {
}

func (darkIP) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, uid, action string) (bool, error) {
	if uid == "" {
		return true, nil
	}
	var span trace.Span
	logger := xlog.FromContext(ctx)
	ctx, span = utils.GetSpan(ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appIds[0]))
	defer span.End()

	var darkModel model.RiskDarkList
	darkSvr := conf2.NewConfDarklistSvr(ctx)
	//IP是否在自己的黑产库中，评分>6
	info, err := darkSvr.OneExistAndNoExpire(uid, 2)
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		//DB异常，不做处理继续剩下的逻辑
		xmetric.Metrics.Inc("db", "dark", "error")
		logger.Error(fmt.Sprintf("Check ip OneByUid 查询DB error: %v", err))
	} else if info != nil && info.Score < 6 {
		xmetric.MetricsDark.Inc(appIds[0], strconv.Itoa(int(clientId)), "ip-db", uid, "no-pass")
		logger.Warn(fmt.Sprintf("ip: %v 在dark名单内 score<6", uid))
		return false, nil
	} else if info != nil && info.Score > 6 {
		xmetric.MetricsDark.Inc(appIds[0], strconv.Itoa(int(clientId)), "ip-db", uid, "pass")
		logger.Warn(fmt.Sprintf("ip: %v 在dark名单内 score>6", uid))
		return true, nil
	}

	svc := dark2.NewDark(ctx)
	//开启数美验证
	var ishumeiRepo dark.Ishumei
	conf := config.Get().Dark
	if !conf.Ishumei.IsOpen {
		return true, nil
	}

	if ok := svc.CheckIP(appIds[0], clientId, uid, &ishumeiRepo); !ok {
		_ = darkModel.Add(appIds[0], clientId, uid, constant.DataIPType, ishumeiRepo.GetName(), 4, time.Now().Add(time.Duration(config.Get().Dark.Black)).Unix(), startup.GetMySql().WithContext(ctx))
		span.SetStatus(codes.Ok, ecode.Aborted.Error())
		return false, nil
	} else {
		_ = darkModel.Add(appIds[0], clientId, uid, constant.DataIPType, ishumeiRepo.GetName(), 8, time.Now().Add(time.Duration(config.Get().Dark.White)).Unix(), startup.GetMySql().WithContext(ctx))
		span.SetStatus(codes.Ok, ecode.OK.Error())
		return true, nil
	}
}

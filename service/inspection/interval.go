package inspection

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/constant"
	"risk/model"
	"risk/service/conf"
	"risk/startup"
	"risk/xmetric"
	"strconv"
	"strings"
	"time"
)

func init() {
	InspectionList[constant.ChkIntervalAccount] = interval{
		intervalType: constant.ChkIntervalAccount,
	}

	InspectionList[constant.ChkIntervalIp] = interval{
		intervalType: constant.ChkIntervalIp,
	}

	InspectionList[constant.ChkIntervalDevice] = interval{
		intervalType: constant.ChkIntervalDevice,
	}
}

type interval struct {
	intervalType uint16
}

func (i interval) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, uid, action string) (bool, error) {
	if uid == "" {
		return true, nil
	}
	//11 请求间隔
	logger := xlog.FromContext(ctx)
	//开启了检测
	key := ""
	switch i.intervalType {
	case constant.RiskTypePay:
		key = constant.RdbIntervalPay
	case constant.RiskTypeAccount:
		key = constant.RdbIntervalAccount
	case constant.RiskTypeApp:
		key = constant.RdbIntervalApp
	}

	var interval model.FrequencyArray
	c, err := conf.NewConfigClient(ctx).OneByClientIdAndType(appIds, clientId, i.intervalType)
	err = sonic.UnmarshalString(c.Frequency, &interval)
	if err != nil {
		logger.Error(fmt.Sprintf("UnmarshalString error: %v data:%#v", err, c.Frequency))
		return true, nil
	}

	rdb := startup.GetRedis()
	rdbNidKey := fmt.Sprintf("%sc-%d:u-%s:a-%s:ac-%s", key, clientId, uid, appIds[0], action)
	nidCmder := rdb.SetEx(ctx, rdbNidKey, 1, time.Duration(interval.Time)*time.Second)
	if nidCmder.Err() != nil {
		xmetric.MetricsInterval.Inc(appIds[0], strconv.Itoa(int(clientId)), "account", strconv.Itoa(int(i.intervalType)), uid, "error")
		logger.Error(fmt.Sprintf("redis SetNX 保持，rdbNidKey:%v,err:%v", rdbNidKey, nidCmder.Err()))
		return true, nil
	} else if strings.ToLower(nidCmder.Val()) != "ok" {
		xmetric.MetricsInterval.Inc(appIds[0], strconv.Itoa(int(clientId)), "account", strconv.Itoa(int(i.intervalType)), uid, "no-pass")
		logger.Info(fmt.Sprintf("nid:%v 间隔太短被限，间隔要求:%vs，rdbNidKey:%v", uid, interval.Time, rdbNidKey))
		return false, nil
	}
	xmetric.MetricsInterval.Inc(appIds[0], strconv.Itoa(int(clientId)), "nid", strconv.Itoa(int(i.intervalType)), uid, "pass")
	return true, nil
}

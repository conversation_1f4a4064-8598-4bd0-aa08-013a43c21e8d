package inspection

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/constant"
	"risk/model"
	"risk/service/conf"
	"risk/utils"
	"risk/xmetric"
	"strconv"
)

func init() {
	InspectionList[constant.ChkActionAccountFrequency] = frequency{
		frequencyType: constant.ChkActionAccountFrequency,
	}
	InspectionList[constant.ChkActionIPFrequency] = frequency{
		frequencyType: constant.ChkActionIPFrequency,
	}
	InspectionList[constant.ChkActionDeviceFrequency] = frequency{
		frequencyType: constant.ChkActionDeviceFrequency,
	}
}

type frequency struct {
	frequencyType uint16
}

// uid 是唯一标识，比如account,ip DOID
func (f frequency) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, uid, action string) (bool, error) {
	if uid == "" {
		return true, nil
	}
	//4 账号频率 5 IP频率 6 设备频率
	logger := xlog.FromContext(ctx)

	c, err := conf.NewConfigClient(ctx).OneByClientIdAndType(appIds, clientId, f.frequencyType)

	//开启了检测
	key := ""
	switch riskType {
	case constant.RiskTypePay:
		key = constant.RdbFrequencyPay
	case constant.RiskTypeAccount:
		key = constant.RdbFrequencyAccount
	case constant.RiskTypeApp:
		key = constant.RdbFrequencyApp

	}
	rdbKey := fmt.Sprintf("%sc-%d:t-%d:u-%s:a-%s:ac-%s", key, clientId, f.frequencyType, uid, appIds[0], action)

	frequency := make([]model.FrequencyArray, 0)
	err = sonic.UnmarshalString(c.Frequency, &frequency)
	if err != nil {
		logger.Error(fmt.Sprintf("UnmarshalString error: %v data:%#v", err, c.Frequency))
		return true, nil
	}
	ok, err := f.checkFrequencyByUID(ctx, rdbKey, frequency)
	if err != nil {
		xmetric.MetricsFrequency.Inc(appIds[0], strconv.Itoa(int(clientId)), strconv.Itoa(int(f.frequencyType)), action, uid, "error")
		logger.Error(fmt.Sprintf("check frequency,err:%v", err))
	} else if !ok {
		xmetric.MetricsFrequency.Inc(appIds[0], strconv.Itoa(int(clientId)), strconv.Itoa(int(f.frequencyType)), action, uid, "no_pass")
		logger.Info(fmt.Sprintf("策略:%d UID:%s 被限制", f.frequencyType, uid))
		return false, nil
	}
	xmetric.MetricsFrequency.Inc(appIds[0], strconv.Itoa(int(clientId)), strconv.Itoa(int(f.frequencyType)), action, uid, "pass")

	return true, nil

}

func (f frequency) checkFrequencyByUID(ctx context.Context, rdbKey string, conf []model.FrequencyArray) (bool, error) {
	if len(conf) == 0 {
		xlog.FromContext(ctx).Error("Frequency config为空！")
		return true, nil
	}
	limiter := utils.NewLimiter(ctx)
	for _, c := range conf {
		rdbKeys := fmt.Sprintf("%s:%d", rdbKey, c.Time)
		if ok := limiter.SyncWindowLimit(rdbKeys, c.Count, c.Time); !ok {
			xlog.FromContext(ctx).Error(fmt.Sprintf("频率限制 config:%+v", c))
			return false, nil
		}
	}
	return true, nil
}

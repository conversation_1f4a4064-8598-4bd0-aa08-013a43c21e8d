package inspection

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"risk/constant"
	conf2 "risk/service/conf"
	"risk/utils"
	"risk/xmetric"
	"strconv"
)

func init() {

	InspectionList[constant.ChkWhiteListAccount] = whiteList{
		whiteListType: constant.DataAccountType,
	}

	InspectionList[constant.ChkWhiteListIp] = whiteList{
		whiteListType: constant.DataIPType,
	}

	InspectionList[constant.ChkWhiteListDevice] = whiteList{
		whiteListType: constant.DataDeviceType,
	}
}

type whiteList struct {
	whiteListType uint8
}

func (w whiteList) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, uid, action string) (bool, error) {
	if uid == "" {
		return true, nil
	}
	ctx, span := utils.GetSpan(ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appIds[0]))
	defer span.End()

	whiteSvr := conf2.NewConfWhitelistSvr(ctx)
	isExist := whiteSvr.OneExistAndNoExpire(uid, appIds, clientId, w.whiteListType)
	if isExist {
		xmetric.Metrics.Inc("white", strconv.Itoa(int(w.whiteListType)), "success")
		xlog.FromContext(ctx).Info(fmt.Sprintf("1 账号 2 IP 3 type:%v UID:%v app_id:%v client_id:%v 在白名单内", w.whiteListType, uid, appIds[0], clientId))
		return true, nil
	}
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	return false, nil
}

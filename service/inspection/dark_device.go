package inspection

import (
	"context"
	"errors"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/repo/dark"
	conf2 "risk/service/conf"
	dark2 "risk/service/dark"
	"risk/startup"
	"risk/utils"
	"time"
)

func init() {
	InspectionList[constant.ChkDarkDevice] = darkDevice{}
}

type darkDevice struct {
}

func (darkDevice) Inspection(ctx context.Context, appIds []string, clientId, riskType uint32, DOID, action string) (bool, error) {
	var span trace.Span
	logger := xlog.FromContext(ctx)
	ctx, span = utils.GetSpan(ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appIds[0]))
	defer span.End()

	conf := config.Get().Dark

	var darkModel model.RiskDarkList
	darkSvr := conf2.NewConfDarklistSvr(ctx)

	//fid是否在自己的黑产库中，评分>6
	info, err := darkSvr.OneExistAndNoExpire(DOID, 3)
	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
		//DB异常，不做处理继续剩下的逻辑
		logger.Error(fmt.Sprintf("Check fid OneByUid 查询DB error: %v", err))
	} else if info != nil && info.Score >= 6 {
		logger.Warn(fmt.Sprintf("DOID: %v 在dark名单内 score>=6", DOID))
		return true, nil
	} else if info != nil && info.Score < 6 {
		logger.Warn(fmt.Sprintf("DOID: %v 在dark名单内 score<6", DOID))
		return false, nil
	}

	svc := dark2.NewDark(ctx)
	//开启数美验证
	var ishumeiRepo dark.Ishumei
	if !conf.Ishumei.IsOpen || RiskDevice == nil || RiskDevice.Ishumei == "" {
		return true, nil
	}

	if ok := svc.CheckDevice(appIds[0], clientId, RiskDevice.Ishumei, RiskDevice.Fid, &ishumeiRepo); !ok {
		_ = darkModel.Add(appIds[0], clientId, DOID, constant.DataDeviceType, ishumeiRepo.GetName(), 4, time.Now().Add(time.Duration(config.Get().Dark.Black)).Unix(), startup.GetMySql().WithContext(ctx))
		span.SetStatus(codes.Ok, ecode.Aborted.Error())
		return false, nil
	} else {
		_ = darkModel.Add(appIds[0], clientId, DOID, constant.DataDeviceType, ishumeiRepo.GetName(), 8, time.Now().Add(time.Duration(config.Get().Dark.White)).Unix(), startup.GetMySql().WithContext(ctx))
		span.SetStatus(codes.Ok, ecode.OK.Error())
		return true, nil
	}
}

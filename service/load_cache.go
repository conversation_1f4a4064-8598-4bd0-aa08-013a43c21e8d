package service

import (
	"context"
	"risk/service/conf"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func LoadCache() error {
	ctx := context.Background()
	//if err := conf.NewConfBlacklistSvr(ctx).LoadAllConfigToCache(); err != nil {
	//	return err
	//}
	//if err := conf.NewConfWhitelistSvr(ctx).LoadAllConfigToCache(); err != nil {
	//	return err
	//}
	//if err := conf.NewConfDarklistSvr(ctx).LoadAllConfigToCache(); err != nil {
	//	return err
	//}
	//if err := conf.NewConfigClient(ctx).LoadAllConfigToCache(); err != nil {
	//	return err
	//}
	if err := conf.InitConfGeetestConfSvr(); err != nil {
		return err
	}

	xlog.FromContext(ctx).Info("LoadCache success")

	return nil
}

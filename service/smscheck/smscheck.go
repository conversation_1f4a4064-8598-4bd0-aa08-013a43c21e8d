package smscheck

import (
	"context"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/proto"
	"risk/repo"
	"risk/rpc"
	"risk/service"
	"strconv"
)

const smsScene = "risk_phone_check"

type SMSCheck struct {
	Nonce  string
	logger *xlog.Logger
}

func GetSMSChecker(ctx context.Context, nonce string) *SMSCheck {
	return &SMSCheck{Nonce: nonce, logger: xlog.FromContext(ctx)}
}

func (s *SMSCheck) CheckPhoneAndSendSMS(ctx context.Context, phone string, mock int32) (*proto.SDKCheckPhoneResponse, error) {
	if !config.Get().SMSConfig.IsOpen {
		return nil, nil
	}

	c := service.NewNonceMgr(ctx)
	vals, err := c.GetFromRedis(s.Nonce, ctx)
	if err != nil {
		return nil, err
	}
	if vals == nil {
		return nil, constant.ErrId
	}
	if vals.Phone != phone {
		return nil, constant.ErrSmsNonceNotFound
	}
	var code string
	if mock == 1 && config.Get().Debug {
		var res *rpc.GetPhoneCodeRespTest
		if res, err = rpc.GetPhoneCodeTest(ctx, &rpc.GetPhoneCodeReq{
			Phone: phone,
			Scene: smsScene,
		}); res != nil {
			code = res.Code
		}
	} else {
		_, err = rpc.GetPhoneCode(ctx, &rpc.GetPhoneCodeReq{
			Phone: phone,
			Scene: smsScene,
		})
	}
	// 发送验证码
	if err != nil {
		return nil, err
	}

	return &proto.SDKCheckPhoneResponse{
		Code: code,
	}, nil
}

func (s *SMSCheck) CheckSMSCode(ctx context.Context, params *proto.SDKCheckCodeRequest) error {
	if !config.Get().SMSConfig.IsOpen {
		return nil
	}

	nonceMgr := service.NewNonceMgr(ctx)
	nonceVal, err := nonceMgr.GetFromRedis(s.Nonce, ctx)
	if err != nil {
		s.logger.Error("获取getCaptchaRedis出错,err:%v", xlog.Err(err))
		return nil
	}
	if nonceVal == nil {
		s.logger.Error("nonce not found", zap.String("nonce", s.Nonce))
		return constant.ErrSmsNonceNotFound
	} else if nonceVal.Handler != constant.HandlerSms {
		s.logger.Error("nonce Handler is not sms", zap.String("nonce", s.Nonce), zap.Any("nonceVal", nonceVal))
		return constant.ErrSmsNonceNotFound
	}

	doid := nonceVal.DeviceId
	if params.DOID != "" {
		doid = params.DOID
	}

	svc := service.NewStrategy(ctx)
	// 检查验证码
	err = rpc.CheckPhoneCode(ctx, &rpc.CheckPhoneCodeReq{
		Phone: nonceVal.Phone,
		Code:  params.Code,
		Scene: smsScene,
	})
	if err != nil {
		go func() {
			svc.SetLiveTags(
				&service.Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: doid, RoleId: nonceVal.RoleId}, //记请求的账户与DOID的数，不从nonce vale中取
				constant.HandlerSms,
				nonceVal.ModelCode,
				false)

			event := repo.FromMapToResultEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = nonceVal.Nid
			event.RoleId = nonceVal.RoleId
			event.AppID = params.AppId
			event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
			event.CaptchaEventID = nonceVal.Id
			event.CaptchaStatus = 2
			event.CaptchaResult = 2
			event.CaptchaType = uint32(constant.HandlerSms)
			event.FailedReason = "invalid phone-code"
			repo.ReportBI(ctx, event)

		}()
		return err
	}
	//验证通过
	var isForceAppId bool
	for _, forceAppId := range config.Get().Machine.ForceAppId {
		if params.AppId == forceAppId {
			isForceAppId = true
		}
	}

	if nonceVal.Action == constant.ActionCheckDOID || isForceAppId {
		if _, err := nonceMgr.SetToRedis(nonceVal, s.Nonce, ctx); err != nil {
			return err
		}
	} else {
		//清除特殊标签计时器
		svc.SetLiveTags(
			&service.Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: doid, RoleId: nonceVal.RoleId}, //清请求的账户与DOID的数，不从nonce vale中取
			constant.HandlerSms,
			nonceVal.ModelCode,
			true)

		//上报验证成功
		svc.ReportCheckSuccess(nonceVal.SceneCode,
			constant.HandlerSms,
			&service.Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: doid, RoleId: nonceVal.RoleId},
		)

		go func() {
			event := repo.FromMapToResultEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = nonceVal.Nid
			event.RoleId = nonceVal.RoleId
			event.AppID = params.AppId
			event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
			event.CaptchaEventID = nonceVal.Id
			event.CaptchaStatus = 3
			event.CaptchaResult = 1
			event.CaptchaType = uint32(constant.HandlerSms)
			repo.ReportBI(ctx, event)
		}()

	}

	_ = nonceMgr.DelFromRedis(s.Nonce, ctx)
	return nil

}

func (s *SMSCheck) SendCode(ctx context.Context) error {
	return nil
}

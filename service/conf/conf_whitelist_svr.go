package conf

import (
	"context"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/rdbs"
	"risk/startup"
	"risk/utils"
	"time"

	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type ConfWhitelistSvr struct {
	db     *xgorm.DB
	ctx    context.Context
	logger *xlog.Logger
}

func NewConfWhitelistSvr(ctx context.Context) *ConfWhitelistSvr {
	return &ConfWhitelistSvr{
		db:     startup.GetMySql().WithContext(ctx),
		ctx:    ctx,
		logger: xlog.FromContext(ctx),
	}
}

// Add 新增
func (c *ConfWhitelistSvr) Add(ctx context.Context, params *proto.AddWhiteListRequest) (uint64, error) {
	// 校验是否存在
	var m model.RiskWhiteList
	conf, err := c.GetByUnionUnique(params.ClientId, params.AppId, params.Uid, params.Type)
	if err != nil {
		if !errors.Is(err, xgorm.ErrRecordNotFound) {
			return 0, constant.ErrDBCode
		}
	}
	if conf != nil {
		return 0, constant.ErrWhiteListExist
	}

	// 新增
	if err := copier.Copy(&m, params); err != nil {
		c.logger.Error("[copier.Copy] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return 0, constant.ErrParam
	}

	if params.ExpiredAt > 0 {
		m.ExpiredAt = time.Now().Add(time.Duration(params.ExpiredAt) * time.Second).Unix()
	}
	if err := m.Add(c.db); err != nil {
		c.logger.Error("[Add] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return 0, constant.ErrWhiteListAddFail
	}

	if err := c.LoadOneConfigToCache(uint32(m.ClientID), m.AppId, m.Uid, int32(m.Type), model.RiskWhiteListRdb{ExpiredAt: m.ExpiredAt}); err != nil {
		return 0, constant.ErrWhiteListAddFail
	}

	return m.ID, nil
}

// Update 修改
func (c *ConfWhitelistSvr) Update(ctx context.Context, params *proto.EditWhiteListRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return false, constant.ErrWhiteListNotExist
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskWhiteList
	id := int64(conf.ID)
	var expiredAt int64 //0代表永不过期
	if params.ExpiredAt > 0 {
		expiredAt = time.Now().Add(time.Duration(params.ExpiredAt) * time.Second).Unix()
	}
	conf.ExpiredAt = expiredAt
	if err := m.Update(c.db, id, expiredAt); err != nil {
		c.logger.Error("[Update] failed",
			xlog.Int64("id", id),
			xlog.Int64("expiredAt", expiredAt),
			xlog.Err(err),
		)
		return false, constant.ErrWhiteListUpdateFail
	}

	rdbKey := fmt.Sprintf("%sa-%s:c-%d:t-%d-%s", constant.RdbWhiteList, conf.AppId, conf.ClientID, conf.Type, conf.Uid)
	if err := rdbs.NewCache(c.ctx).Remove(rdbKey); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrWhiteListUpdateFail
	}

	if err := c.LoadOneConfigToCache(uint32(conf.ClientID), conf.AppId, conf.Uid, int32(conf.Type), model.RiskWhiteListRdb{ExpiredAt: conf.ExpiredAt}); err != nil {
		return false, constant.ErrWhiteListUpdateFail
	}

	return true, nil
}

// Delete 删除
func (c *ConfWhitelistSvr) Delete(ctx context.Context, params *proto.ClientIdRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskWhiteList
	id := int64(conf.ID)
	if err := m.Delete(c.db, id); err != nil {
		c.logger.Error("[Delete] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return false, constant.ErrWhiteListDeleteFail
	}
	c.logger.Info(fmt.Sprintf("[Delete] delete data: %+v", conf))
	rdbKey := fmt.Sprintf("%sa-%s:c-%d:t-%d-%s", constant.RdbWhiteList, conf.AppId, conf.ClientID, conf.Type, conf.Uid)
	if err := rdbs.NewCache(c.ctx).Remove(rdbKey); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrWhiteListDeleteFail
	}

	return true, nil
}

// GetById 查询 by id
func (c *ConfWhitelistSvr) GetById(id int64) (*model.RiskWhiteList, error) {
	var m model.RiskWhiteList
	conf, err := m.GetById(c.db, id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetById] not found",
				xlog.Int64("id", id),
			)
			return nil, err
		}
		c.logger.Error("[GetById] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetById] not found, conf is nil",
			xlog.Int64("id", id),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

// GetByUnionUnique 查询
func (c *ConfWhitelistSvr) GetByUnionUnique(clientId uint32, appId, uid string, t int32) (*model.RiskWhiteList, error) {
	var m model.RiskWhiteList
	conf, err := m.GetByUnionUnique(c.db, clientId, appId, uid, t)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetByUnionUnique] not found",
				xlog.Uint32("clientId", clientId),
				xlog.String("appId", appId),
				xlog.String("uid", uid),
				xlog.Int32("t", t),
			)
			return nil, err
		}
		c.logger.Error("[GetByUnionUnique] failed",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.String("uid", uid),
			xlog.Int32("t", t),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetByUnionUnique] not found, conf is nil",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.String("uid", uid),
			xlog.Int32("t", t),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

func (c *ConfWhitelistSvr) OneByClientIdAndType(clientId uint32, appId, uid string, t int32) (*model.RiskWhiteList, error) {
	var conf model.RiskWhiteList
	//rdbKey := fmt.Sprintf("%sa-%s:c-%d:t-%d-%s", constant.RdbWhiteList, appId, clientId, t, uid)
	//appId clientId未匹配到，找appId=all
	rdbKey := c.getCacheKey(clientId, appId, uid, t)
	rdbVal, err := rdbs.NewCache(c.ctx).Get(rdbKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		c.logger.Error("GetByCache failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return nil, err
	} else if rdbVal != "" {
		if err := sonic.UnmarshalString(rdbVal, &conf); err == nil {
			return &conf, nil
		}
	}

	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfWhitelistSvr) OneExistAndNoExpire(uid string, appIds []string, clientId uint32, t uint8) bool {
	//找appId对应的与全局all的
	for _, appId := range appIds {
		conf, err := c.OneByClientIdAndType(clientId, appId, uid, int32(t))
		if err != nil {
			continue
		}
		if conf.ExpiredAt == 0 || conf.ExpiredAt >= utils.NowUnix() { //0是永久生效
			return true
		}
	}
	return false
}

func (c *ConfWhitelistSvr) LoadAllConfigToCache() error {
	var conf model.RiskWhiteList
	list, err := conf.FindAll(c.db)
	if err != nil {
		return err
	}

	for _, conf := range list {
		if err := c.LoadOneConfigToCache(uint32(conf.ClientID), conf.AppId, conf.Uid, int32(conf.Type), model.RiskWhiteListRdb{ExpiredAt: conf.ExpiredAt}); err != nil {
			return err
		}
	}
	return nil
}

func (c *ConfWhitelistSvr) LoadOneConfigToCache(clientId uint32, appId, uid string, t int32, conf model.RiskWhiteListRdb) error {
	soncVal, err := sonic.MarshalString(conf)
	if err != nil {
		return err
	}

	rdbKey := c.getCacheKey(clientId, appId, uid, t)

	if err := rdbs.NewCache(c.ctx).Set(rdbKey, soncVal); err != nil {
		c.logger.Error("[LoadOneConfigToCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return err
	}

	c.logger.Info("[LoadOneConfigToCache] Config white_list data:",
		zap.String("rdbKey", rdbKey),
		zap.String("config", fmt.Sprintf("%+v", conf)))
	return nil
}

func (c *ConfWhitelistSvr) getCacheKey(clientId uint32, appId, uid string, t int32) string {
	return fmt.Sprintf("%sa-%s:c-%d:t-%d:u-%s", constant.RdbWhiteList, appId, clientId, t, uid)
}

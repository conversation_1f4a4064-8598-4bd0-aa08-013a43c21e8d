package conf

import (
	"context"
	"errors"
	"fmt"
	"risk/model"
	"risk/startup"
	"time"

	"github.com/allegro/bigcache/v3"
	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

const (
	redisCacheKey = "geetest:config:hash"
	cacheDDL      = 5 * time.Minute
	defaultAppID  = "all"
)

type ConfGeetestConfSvr struct {
	localcache *bigcache.BigCache
}

var confGeetestConfSvrIns *ConfGeetestConfSvr

func GetConfGeetestConfSvrIns() *ConfGeetestConfSvr {
	return confGeetestConfSvrIns
}

func InitConfGeetestConfSvr() error {
	ctx := context.Background()
	c, err := bigcache.New(ctx, bigcache.DefaultConfig(cacheDDL))
	if err != nil {
		return err
	}
	confGeetestConfSvrIns = &ConfGeetestConfSvr{
		localcache: c,
	}
	if err := confGeetestConfSvrIns.LoadAllConfigToRedis(ctx); err != nil {
		return err
	}
	return nil
}

type GeetestCacheItem struct {
	AppID      string `json:"app_id"`
	CaptchaID  string `json:"captcha_id"`
	CaptchaKey string `json:"captcha_key"`
	IsClose    bool   `json:"is_close"`
}

func (c *ConfGeetestConfSvr) LoadAllConfigToRedis(ctx context.Context) error {
	logger := xlog.FromContext(ctx)
	list, err := model.GeetestConfigList(ctx)
	if err != nil {
		logger.Error("LoadAllConfigToRedis GeetestConfigList failed", xlog.Err(err))
		return err
	}
	args := make([]interface{}, 0, len(list)*2)
	for _, v := range list {
		val, _ := sonic.MarshalString(&GeetestCacheItem{
			AppID:      v.AppID,
			CaptchaID:  v.CaptchaID,
			CaptchaKey: v.CaptchaKey,
			IsClose:    v.IsClose,
		})
		// HMSet key val key val key val
		args = append(args, v.AppID, val, v.CaptchaID, val)
	}
	if len(args) == 0 {
		return nil
	}

	if err := startup.GetRedis().HSet(ctx, redisCacheKey, args).Err(); err != nil {
		logger.Error("LoadAllConfigToRedis HMSet failed", xlog.Err(err))
		return err
	}

	for _, v := range list {
		if _, err := c.ReloadConfByKey(ctx, v.AppID); err != nil {
			logger.Error("LoadAllConfigToRedis ReloadConfByAppID failed", xlog.Err(err))
			return err
		}
	}

	return nil
}

// ReloadConfByKey 重新加载配置, key的取值可以是appid或者captcha_id
func (c *ConfGeetestConfSvr) ReloadConfByKey(ctx context.Context, appidOrCaptchId string) (string, error) {
	logger := xlog.FromContext(ctx)
	res, err := startup.GetRedis().HGet(ctx, redisCacheKey, appidOrCaptchId).Result()
	if err != nil && err != redis.Nil {
		logger.Error("ReloadConfByAppID HGet failed", xlog.Err(err))
		return "", err
	}
	if res == "" {
		if appidOrCaptchId == defaultAppID {
			return `{"app_id":"all","captcha_id":"","captcha_key":"","is_close":true}`, nil
		}
		// 获取全局默认配置
		return c.ReloadConfByKey(ctx, defaultAppID)
	}
	cacheKey := c.getCacheKey(appidOrCaptchId)

	if err := c.localcache.Set(cacheKey, []byte(res)); err != nil {
		logger.Error("ReloadConfByAppID Set failed", xlog.Err(err))
		return "", err
	}

	return res, nil
}

func (c *ConfGeetestConfSvr) GetByCaptchaId(ctx context.Context, captchaID string) (*GeetestCacheItem, error) {
	logger := xlog.FromContext(ctx)
	key := c.getCacheKey(captchaID)
	res, err := c.localcache.Get(key)
	if err != nil {
		if err == bigcache.ErrEntryNotFound {
			tmp, err := c.ReloadConfByKey(ctx, captchaID)
			if err != nil {
				return nil, err
			}
			res = []byte(tmp)
		} else {
			logger.Error("GetByID Get failed", xlog.Err(err))
			return nil, err
		}
	}
	var item GeetestCacheItem
	if err := sonic.Unmarshal(res, &item); err != nil {
		logger.Error("GetByID UnmarshalString failed", xlog.Err(err))
		return nil, err
	}

	return &item, nil
}

func (c *ConfGeetestConfSvr) GetByAppID(ctx context.Context, appid string) (*GeetestCacheItem, error) {
	logger := xlog.FromContext(ctx)
	key := c.getCacheKey(appid)
	res, err := c.localcache.Get(key)
	xlog.FromContext(ctx).Info("本地获取 GetByAppID", xlog.String("key", key), xlog.String("res", string(res)))
	if err != nil {
		if errors.Is(err, bigcache.ErrEntryNotFound) {
			tmp, err := c.ReloadConfByKey(ctx, appid)
			if err != nil {
				return nil, err
			}
			res = []byte(tmp)
		} else {
			logger.Error("GetByID Get failed", xlog.Err(err))
			return nil, err
		}
	}
	var item GeetestCacheItem
	if err := sonic.Unmarshal(res, &item); err != nil {
		logger.Error("GetByID UnmarshalString failed", xlog.Err(err))
		return nil, err
	}

	return &item, nil
}

func (c *ConfGeetestConfSvr) getCacheKey(key string) string {
	return fmt.Sprintf("geetest:captcha:%s", key)
}

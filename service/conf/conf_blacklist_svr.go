package conf

import (
	"context"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/rdbs"
	"risk/startup"
	"risk/utils"
	"time"

	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type ConfBlacklistSvr struct {
	db     *xgorm.DB
	ctx    context.Context
	logger *xlog.Logger
}

func NewConfBlacklistSvr(ctx context.Context) *ConfBlacklistSvr {
	return &ConfBlacklistSvr{
		db:     startup.GetMySql().WithContext(ctx),
		ctx:    ctx,
		logger: xlog.FromContext(ctx),
	}
}

// 新增
func (c *ConfBlacklistSvr) Add(ctx context.Context, params *proto.AddBlackListRequest) (uint64, error) {
	// 校验是否存在
	var m model.RiskBlackList
	conf, err := c.GetByUnionUnique(params.ClientId, params.AppId, params.Uid, params.Type)
	if err != nil {
		if !errors.Is(err, xgorm.ErrRecordNotFound) {
			return 0, constant.ErrDBCode
		}
	}
	if conf != nil {
		return 0, constant.ErrBlackListExist
	}

	// 新增
	if err := copier.Copy(&m, params); err != nil {
		c.logger.Error("[copier.Copy] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return 0, constant.ErrParam
	}

	if params.ExpiredAt > 0 {
		m.ExpiredAt = time.Now().Add(time.Duration(params.ExpiredAt) * time.Second).Unix()
	}
	if err := m.Add(c.db); err != nil {
		c.logger.Error("[Add] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return 0, constant.ErrBlackListAddFail
	}

	if err := c.LoadOneConfigToCache(uint32(m.ClientID), m.AppId, m.Uid, m.Type, model.RiskBlackListRdb{ExpiredAt: m.ExpiredAt}); err != nil {
		return 0, constant.ErrBlackListAddFail
	}

	return m.ID, nil
}

// 修改
func (c *ConfBlacklistSvr) Update(ctx context.Context, params *proto.EditBlackListRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return false, constant.ErrBlackListNotExist
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskBlackList
	id := int64(conf.ID)
	var expiredAt int64 // 0代表永不过期
	if params.ExpiredAt > 0 {
		expiredAt = time.Now().Add(time.Duration(params.ExpiredAt) * time.Second).Unix()
	}
	conf.ExpiredAt = expiredAt
	if err := m.Update(c.db, id, expiredAt); err != nil {
		c.logger.Error("[Update] failed",
			xlog.Int64("id", id),
			xlog.Int64("expiredAt", expiredAt),
			xlog.Err(err),
		)
		return false, constant.ErrBlackListUpdateFail
	}

	rdbKey := c.getCacheKey(conf.AppId, uint32(conf.ClientID), conf.Uid, conf.Type)
	if err := rdbs.NewCache(c.ctx).Remove(rdbKey); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrBlackListUpdateFail
	}

	if err := c.LoadOneConfigToCache(uint32(conf.ClientID), conf.AppId, conf.Uid, conf.Type, model.RiskBlackListRdb{ExpiredAt: expiredAt}); err != nil {
		return false, constant.ErrBlackListUpdateFail
	}

	return true, nil
}

// 删除
func (c *ConfBlacklistSvr) Delete(ctx context.Context, params *proto.ClientIdRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskBlackList
	id := int64(conf.ID)
	if err := m.Delete(c.db, id); err != nil {
		c.logger.Error("[Delete] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return false, constant.ErrBlackListDeleteFail
	}

	c.logger.Info(fmt.Sprintf("[Delete] delete data: %+v", conf))
	rdbKey := c.getCacheKey(conf.AppId, uint32(conf.ClientID), conf.Uid, conf.Type)
	if err := rdbs.NewCache(c.ctx).Remove(rdbKey); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrBlackListDeleteFail
	}

	return true, nil
}

// 查询 by id
func (c *ConfBlacklistSvr) GetById(id int64) (*model.RiskBlackList, error) {
	var m model.RiskBlackList
	conf, err := m.GetById(c.db, id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetById] not found",
				xlog.Int64("id", id),
			)
			return nil, err
		}
		c.logger.Error("[GetById] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetById] not found, conf is nil",
			xlog.Int64("id", id),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

// 查询
func (c *ConfBlacklistSvr) GetByUnionUnique(clientId uint32, appId, uid string, t int32) (*model.RiskBlackList, error) {
	var m model.RiskBlackList
	conf, err := m.GetByUnionUnique(c.db, clientId, appId, uid, t)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetByUnionUnique] not found",
				xlog.Uint32("clientId", clientId),
				xlog.String("appId", appId),
				xlog.String("uid", uid),
				xlog.Int32("t", t),
			)
			return nil, err
		}
		c.logger.Error("[GetByUnionUnique] failed",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.String("uid", uid),
			xlog.Int32("t", t),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetByUnionUnique] not found, conf is nil",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.String("uid", uid),
			xlog.Int32("t", t),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

func (c *ConfBlacklistSvr) OneByClientIdAndType(clientId uint32, appId, uid string, t uint8) (*model.RiskBlackList, error) {
	var conf model.RiskBlackList
	rdbKey := c.getCacheKey(appId, clientId, uid, t)
	rdbVal, err := rdbs.NewCache(c.ctx).Get(rdbKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		c.logger.Error("GetByCache failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return nil, err
	}
	if err == nil && rdbVal != "" {
		if err := sonic.UnmarshalString(rdbVal, &conf); err == nil {
			return &conf, nil
		}
	}

	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfBlacklistSvr) OneExistAndNoExpire(uid string, appIds []string, clientId uint32, t uint8) bool {
	for _, id := range appIds {
		conf, err := c.OneByClientIdAndType(clientId, id, uid, t)
		if err != nil {
			continue
		}

		if conf.ExpiredAt == 0 || conf.ExpiredAt >= utils.NowUnix() {
			return true
		}
	}
	return false
}

func (c *ConfBlacklistSvr) LoadAllConfigToCache() error {
	var conf model.RiskBlackList
	list, err := conf.FindAll(c.db)
	if err != nil {
		return err
	}

	for _, conf := range list {
		if err := c.LoadOneConfigToCache(uint32(conf.ClientID), conf.AppId, conf.Uid, conf.Type, model.RiskBlackListRdb{ExpiredAt: conf.ExpiredAt}); err != nil {
			return err
		}
	}
	return nil
}

func (c *ConfBlacklistSvr) LoadOneConfigToCache(clientId uint32, appId, uid string, t uint8, conf model.RiskBlackListRdb) error {
	soncVal, err := sonic.MarshalString(conf)
	if err != nil {
		return err
	}

	rdbKey := c.getCacheKey(appId, clientId, uid, t)

	if err := rdbs.NewCache(c.ctx).Set(rdbKey, soncVal); err != nil {
		c.logger.Error("[LoadOneConfigToCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return err
	}

	c.logger.Info("[LoadOneConfigToCache] Config black_list data:",
		zap.String("rdbKey", rdbKey),
		zap.String("config", fmt.Sprintf("%+v", conf)))
	return nil
}

func (c *ConfBlacklistSvr) getCacheKey(appId string, clientId uint32, uid string, t uint8) string {
	return fmt.Sprintf("%sa-%s:c-%d:t-%d:u-%s", constant.RdbBlackList, appId, clientId, t, uid)
}

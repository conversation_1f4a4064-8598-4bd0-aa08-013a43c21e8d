package conf

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"github.com/redis/go-redis/v9"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/rdbs"
	"risk/startup"
	"strconv"
	"time"
)

type ConfigClient struct {
	db     *xgorm.DB
	ctx    context.Context
	rdb    *xredis.Client
	logger *xlog.Logger
}

func NewConfigClient(ctx context.Context) *ConfigClient {
	return &ConfigClient{
		db:     startup.GetMySql().WithContext(ctx),
		ctx:    ctx,
		rdb:    startup.GetRedis(),
		logger: xlog.FromContext(ctx),
	}
}
func (c *ConfigClient) OneByClientIdAndType(appIds []string, clientId uint32, t uint16) (*model.RiskConfigApp, error) {
	var conf model.RiskConfigApp
	//优先取appId、clientId下的配置
	var rdbKey string
	for _, appId := range appIds {
		for _, cid := range []uint32{clientId, 0} {
			rdbKey = c.GetConfigCacheKey(appId, cid)
			c.logger.Info("[OneByClientIdAndType] rdbKey", zap.String("rdbKey", rdbKey))
			rdbVal, err := rdbs.NewCache(c.ctx).HGet(rdbKey, strconv.Itoa(int(t)))
			c.logger.Info("[OneByClientIdAndType] rdbVal", zap.String("rdbVal", rdbVal))
			if err != nil && !errors.Is(err, redis.Nil) {
				c.logger.Error("GetByCache failed",
					xlog.Err(err),
					xlog.String("rdbKey", rdbKey),
				)
			} else if rdbVal != "" {
				if err := sonic.UnmarshalString(rdbVal, &conf); err == nil {
					return &conf, nil
				}
			}
		}
	}

	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfigClient) ListByClientId(appIds []string, clientId uint32) (map[uint16]model.RiskConfigApp, error) {
	//优先取appId、clientId下的配置
	var rdbKey string
	for _, appId := range appIds {
		for _, cid := range []uint32{clientId, 0} {
			rdbKey = c.GetConfigCacheKey(appId, cid)
			rdbVal, err := rdbs.NewCache(c.ctx).HGetAll(rdbKey)
			c.logger.Info("[ListByClientId] rdbVal", zap.Any("rdbVal", rdbVal))
			if err != nil && !errors.Is(err, redis.Nil) {
				c.logger.Warn("GetByCache failed",
					xlog.Err(err),
					xlog.String("rdbKey", rdbKey),
				)
			}
			if len(rdbVal) > 0 {
				return c.getConfListByRedisVal(rdbVal)
			}
		}
	}
	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfigClient) getConfListByRedisVal(rdbVal map[string]string) (map[uint16]model.RiskConfigApp, error) {
	list := make(map[uint16]model.RiskConfigApp)
	for key, val := range rdbVal {
		var conf model.RiskConfigApp
		parseInt, err := strconv.ParseInt(key, 10, 64)

		if err != nil {
			continue
		}
		if err := sonic.UnmarshalString(val, &conf); err == nil && (parseInt > 100 && conf.IsOpen == 1) {
			list[uint16(parseInt)] = conf
		}
	}
	return list, nil
}

func (c *ConfigClient) LoadAllConfigToCache() error {
	var configModel model.RiskConfigApp
	list, err := configModel.FindAll(c.db)
	if err != nil {
		return err
	}

	for _, conf := range list {
		if err := c.LoadOneConfigToCache(uint32(conf.ClientID), conf.AppId, conf.Type, model.RiskConfigAppRdb{
			Frequency: conf.Frequency,
			Handle:    conf.Handle,
			IsOpen:    conf.IsOpen,
			UpdatedAt: conf.UpdatedAt,
		}); err != nil {
			return err
		}
	}
	return nil
}

func (c *ConfigClient) LoadOneConfigToCache(clientId uint32, appId string, t uint16, conf model.RiskConfigAppRdb) error {
	soncVal, err := sonic.MarshalString(conf)
	if err != nil {
		return err
	}

	rdbKey := c.GetConfigCacheKey(appId, clientId)
	if err := rdbs.NewCache(c.ctx).HSet(rdbKey, strconv.Itoa(int(t)), soncVal); err != nil {
		c.logger.Error("[LoadOneConfigToCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return err
	}

	c.logger.Info("[LoadOneConfigToCache] Config client_config data:",
		zap.String("rdbKey", rdbKey),
		zap.String("config", fmt.Sprintf("%+v", conf)))
	return nil
}

// Add 新增
func (c *ConfigClient) Add(params *proto.AddConfigAppRequest) (bool, error) {
	// 校验是否存在
	var m model.RiskConfigApp
	conf, err := c.GetByUnionUnique(params.ClientId, params.AppId, params.Type)
	if err != nil {
		if !errors.Is(err, xgorm.ErrRecordNotFound) {
			return false, constant.ErrDBCode
		}
	}
	if conf != nil {
		return false, constant.ErrClientConfigExist
	}

	// 新增
	if err := copier.Copy(&m, params); err != nil {
		c.logger.Error("[copier.Copy] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return false, constant.ErrParam
	}

	if err := m.Add(c.db); err != nil {
		c.logger.Error("[Add] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return false, constant.ErrClientConfigAddFail
	}

	val, _ := sonic.MarshalString(model.RiskConfigAppRdb{
		Frequency: conf.Frequency,
		IsOpen:    conf.IsOpen,
		Handle:    conf.Handle,
		UpdatedAt: time.Now(),
	})

	rdbKey := c.GetConfigCacheKey(conf.AppId, uint32(conf.ClientID))
	if err := rdbs.NewCache(c.ctx).HSet(rdbKey, strconv.Itoa(int(conf.Type)), val); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrClientConfigUpdateFail
	}

	return true, nil
}

// Update 修改
func (c *ConfigClient) Update(params *proto.EditConfigAppRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return false, constant.ErrClientConfigNotExist
		}
		return false, constant.ErrDBCode
	}

	id := int64(conf.ID)
	if params.Frequency != "" {
		conf.Frequency = params.Frequency
	}

	if params.Handle > 0 {
		conf.Handle = uint64(params.Handle)
	}

	conf.IsOpen = uint8(params.IsOpen)

	if err := conf.Update(c.db, id); err != nil {
		c.logger.Error("[Update] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return false, constant.ErrClientConfigUpdateFail
	}

	val, _ := sonic.MarshalString(model.RiskConfigAppRdb{
		Frequency: conf.Frequency,
		IsOpen:    conf.IsOpen,
		Handle:    conf.Handle,
		UpdatedAt: time.Now(),
	})
	rdbKey := c.GetConfigCacheKey(conf.AppId, uint32(conf.ClientID))

	if err := rdbs.NewCache(c.ctx).HSet(rdbKey, strconv.Itoa(int(conf.Type)), val); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrClientConfigUpdateFail
	}

	return true, nil
}

// 删除
func (c *ConfigClient) Delete(params *proto.ClientIdRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskConfigApp
	id := int64(conf.ID)
	if err := m.Delete(c.db, id); err != nil {
		c.logger.Error("[Delete] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return false, constant.ErrClientConfigDeleteFail
	}

	//rdbKey := fmt.Sprintf("%sa-%s:c-%d:t-%d", constant.RdbClientConfig, conf.AppId, conf.ClientID, conf.Type)
	rdbKey := c.GetConfigCacheKey(conf.AppId, uint32(conf.ClientID))
	if err := rdbs.NewCache(c.ctx).HDel(rdbKey, strconv.Itoa(int(conf.Type))); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrClientConfigDeleteFail
	}

	return true, nil
}

// 查询 by id
func (c *ConfigClient) GetById(id int64) (*model.RiskConfigApp, error) {
	var m model.RiskConfigApp
	conf, err := m.GetById(c.db, id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetById] not found",
				xlog.Int64("id", id),
			)
			return nil, err
		}
		c.logger.Error("[GetById] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetById] not found, conf is nil",
			xlog.Int64("id", id),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

// 查询
func (c *ConfigClient) GetByUnionUnique(clientId uint32, appId string, t int32) (*model.RiskConfigApp, error) {
	var m model.RiskConfigApp
	conf, err := m.GetByUnionUnique(c.db, clientId, appId, t)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetByUnionUnique] not found",
				xlog.Uint32("clientId", clientId),
				xlog.String("appId", appId),
				xlog.Int32("t", t),
			)
			return nil, err
		}
		c.logger.Error("[GetByUnionUnique] failed",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.Int32("t", t),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetByUnionUnique] not found, conf is nil",
			xlog.Uint32("clientId", clientId),
			xlog.String("appId", appId),
			xlog.Int32("t", t),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

func (c *ConfigClient) GetConfigCacheKey(appId string, clientId uint32) string {
	return fmt.Sprintf("%sc-%d:a-%s", constant.RdbClientConfig, clientId, appId)
}

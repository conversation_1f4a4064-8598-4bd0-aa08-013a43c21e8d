package conf

import (
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/rdbs"
	"risk/startup"
	"risk/utils"

	"github.com/redis/go-redis/v9"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type ConfDarklistSvr struct {
	db     *xgorm.DB
	ctx    context.Context
	logger *xlog.Logger
}

func NewConfDarklistSvr(ctx context.Context) *ConfDarklistSvr {
	return &ConfDarklistSvr{
		db:     startup.GetMySql().WithContext(ctx),
		ctx:    ctx,
		logger: xlog.FromContext(ctx),
	}
}

// 新增
func (c *ConfDarklistSvr) Add(ctx context.Context, params *proto.AddDarkListRequest) (uint64, error) {
	// 校验是否存在
	var m model.RiskDarkList
	conf, err := c.GetByUnionUnique(params.Uid, params.Type)
	if err != nil {
		if !errors.Is(err, xgorm.ErrRecordNotFound) {
			return 0, constant.ErrDBCode
		}
	}
	if conf != nil {
		return 0, constant.ErrDarkListExist
	}

	// 新增
	if err := m.Create(params.AppId, params.ClientId, params.Uid, uint8(params.Type), 0, uint8(params.Score), params.ExpiredAt, c.db); err != nil {
		c.logger.Error("[Create] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return 0, constant.ErrDarkListAddFail
	}

	return m.Id, nil
}

// 修改
func (c *ConfDarklistSvr) Update(ctx context.Context, params *proto.EditDarkListRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return false, constant.ErrDarkListNotExist
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskDarkList
	if err := m.UpdateScoreById(uint8(params.Score), params.ExpiredAt, uint64(params.Id), c.db, conf.Uid, conf.Type); err != nil {
		c.logger.Error("[UpdateScoreById] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return false, constant.ErrDarkListUpdateFail
	}
	return false, nil
}

// 删除
func (c *ConfDarklistSvr) Delete(ctx context.Context, params *proto.ClientIdRequest) (bool, error) {
	// 校验是否存在
	conf, err := c.GetById(params.Id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, constant.ErrDBCode
	}
	var m model.RiskDarkList
	id := int64(conf.Id)
	if err := m.Delete(c.db, id); err != nil {
		c.logger.Error("[DeleteById] failed",
			xlog.Any("params", params),
			xlog.Err(err))
		return false, constant.ErrDarkListDeleteFail
	}

	c.logger.Info(fmt.Sprintf("[DeleteById] delete data: %+v", conf))
	rdbKey := c.getCacheKey(conf.Uid, conf.Type)
	if err := rdbs.NewCache(context.Background()).Remove(rdbKey); err != nil {
		c.logger.Error("[DelByCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return false, constant.ErrDarkListDeleteFail
	}

	return true, nil
}

// 查询
func (c *ConfDarklistSvr) GetByUnionUnique(uid string, t int32) (*model.RiskDarkList, error) {
	var m model.RiskDarkList
	conf, err := m.GetByUnionUnique(c.db, uid, t)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetByUnionUnique] not found",
				xlog.String("uid", uid),
				xlog.Int32("t", t),
			)
			return nil, err
		}
		c.logger.Error("[GetByUnionUnique] failed",
			xlog.String("uid", uid),
			xlog.Int32("t", t),
			xlog.Err(err),
		)
		return nil, err
	}

	if conf == nil {
		c.logger.Info("[GetByUnionUnique] not found, conf is nil",
			xlog.String("uid", uid),
			xlog.Int32("t", t),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

// 查询 by id
func (c *ConfDarklistSvr) GetById(id int64) (*model.RiskDarkList, error) {
	var m model.RiskDarkList
	conf, err := m.GetById(c.db, id)
	if err != nil {
		if errors.Is(err, xgorm.ErrRecordNotFound) {
			c.logger.Info("[GetById] not found",
				xlog.Int64("id", id),
			)
			return nil, err
		}
		c.logger.Error("[GetById] failed",
			xlog.Int64("id", id),
			xlog.Err(err),
		)
		return nil, err
	}
	if conf == nil {
		c.logger.Info("[GetById] not found, conf is nil",
			xlog.Int64("id", id),
		)
		return nil, xgorm.ErrRecordNotFound
	}

	return conf, nil
}

func (c *ConfDarklistSvr) OneByClientIdAndType(uid string, t uint8) (*model.RiskDarkList, error) {
	var conf model.RiskDarkList
	rdbKey := c.getCacheKey(uid, t)
	rdbVal, err := rdbs.NewCache(c.ctx).Get(rdbKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		c.logger.Error("GetByCache failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
	}
	if rdbVal != "" {
		if err := sonic.UnmarshalString(rdbVal, &conf); err == nil {
			return &conf, nil
		}
	}
	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfDarklistSvr) OneExistAndNoExpire(uid string, t uint8) (*model.RiskDarkList, error) {
	conf, err := c.OneByClientIdAndType(uid, t)
	if err != nil {
		return nil, err
	}
	if conf.ExpiredAt == 0 || conf.ExpiredAt >= utils.NowUnix() {
		return conf, nil
	}
	return nil, xgorm.ErrRecordNotFound
}

func (c *ConfDarklistSvr) LoadAllConfigToCache() error {
	var conf model.RiskDarkList
	list, err := conf.FindAll(c.db)
	if err != nil {
		return err
	}

	for _, conf := range list {
		if err := c.LoadOneConfigToCache(conf.Uid, conf.Type, model.RiskDarkListRdb{
			Score:     conf.Score,
			ExpiredAt: conf.ExpiredAt,
		}); err != nil {
			return err
		}
	}
	return nil
}

func (c *ConfDarklistSvr) LoadOneConfigToCache(uid string, t uint8, conf model.RiskDarkListRdb) error {
	soncVal, err := sonic.MarshalString(conf)
	if err != nil {
		return err
	}

	rdbKey := c.getCacheKey(uid, t)

	if err := rdbs.NewCache(c.ctx).Set(rdbKey, soncVal); err != nil {
		c.logger.Error("[LoadOneConfigToCache] failed",
			xlog.Err(err),
			xlog.String("rdbKey", rdbKey),
		)
		return err
	}

	c.logger.Info("[LoadOneConfigToCache] Config dark_list data:",
		zap.String("rdbKey", rdbKey),
		zap.String("config", fmt.Sprintf("%+v", conf)))
	return nil
}

func (c *ConfDarklistSvr) getCacheKey(uid string, t uint8) string {
	return fmt.Sprintf("%st-%d:u-%s", constant.RdbDarkList, t, uid)
}

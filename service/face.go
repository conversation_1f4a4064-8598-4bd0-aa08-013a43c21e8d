package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"risk/config"
	"risk/constant"
	"risk/proto"
	"risk/repo/face"
	"risk/rpc"
	"risk/startup"
	"risk/utils"
	"risk/xmetric"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"github.com/redis/go-redis/v9"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring/uuid"
	"gitlab.papegames.com/nuclear/ecode"
	"go.uber.org/zap"

	nanoid "github.com/matoous/go-nanoid/v2"
)

var faceMap = map[string]face.Face{
	"aliyun": &face.<PERSON>yun{},
}

type FaceSvc struct {
	repoFace     face.Face
	ctx          context.Context
	logger       *xlog.Logger
	faceCodeVale *proto.FaceCodeVal
}

func NewFaceSvcByVendor(ctx context.Context, vendor string, faceCodeVale *proto.FaceCodeVal) (*FaceSvc, error) {
	faceSvc, ok := faceMap[vendor]
	if !ok {
		xlog.FromContext(ctx).Warn("vendor not found", zap.String("vendor", vendor))
		return nil, constant.ErrParam
	}
	return &FaceSvc{
		repoFace:     faceSvc,
		ctx:          ctx,
		logger:       xlog.FromContext(ctx),
		faceCodeVale: faceCodeVale,
	}, nil
}

func NewFaceSvc(ctx context.Context) *FaceSvc {
	return &FaceSvc{
		ctx:    ctx,
		logger: xlog.FromContext(ctx),
	}
}

func (f *FaceSvc) GetCodeForWeb(in *proto.FaceCodeForWebRequest) (*proto.FaceCodeForWebResponse, error) {
	for appId, cs := range config.Get().Dark.Aliyun.FaceWebCodeClose {
		if appId == in.AppId && cs {
			return nil, constant.ErrFaceWebCodeClose
			break
		}
	}
	if in.Token != "" {
		//有登录态 跟 SDK 保持一致
		if in.Nid == "" {
			f.logger.Error("nid can not be empty")
			return nil, constant.ErrParam
		}
		return f.getCodeForSDKBase(in)
	}

	//无登录态
	if in.IdCard == "" && in.RealName == "" && in.Phone == "" {
		f.logger.Error("idCard realName and phone can not be empty")
		return nil, constant.ErrParam
	}

	var riskRule []config.FaceRisk
	for appId, risk := range config.Get().Dark.Aliyun.FaceCodeRisks {
		if appId == in.AppId {
			riskRule = risk
			break
		}
	}

	if len(riskRule) > 0 {
		limiter := utils.NewLimiter(f.ctx)
		var rdbKey string
		for _, rule := range riskRule {
			switch rule.Dimension {
			case "phone":
				rdbKey = fmt.Sprintf("%s%s", constant.RdbFaceRiskPhone, in.Phone)
			case "id_card":
				rdbKey = fmt.Sprintf("%s%s", constant.RdbFaceRiskIdCard, in.IdCard)
			}
			if ok := limiter.SyncWindowLimit(rdbKey, rule.RateLimit, rule.WindowTime); !ok {
				return nil, constant.ErrFaceRisk
			}
		}
	}

	//检查idCard
	if err := f.checkFailed(in.Scene, in.IdCard); err != nil {
		return nil, err
	}
	//检查phone
	if err := f.checkFailed(in.Scene, in.Phone); err != nil {
		return nil, err
	}
	in.Nid = in.Phone

	return f.getCode(in)
}

// 兼容老接口，不增加返回参数
func (f *FaceSvc) GetCodeForSDK(in *proto.FaceCodeRequest) (*proto.FaceCodeResponse, error) {
	var params proto.FaceCodeForWebRequest
	if err := copier.Copy(&params, in); err != nil {
		f.logger.Error("copier.Copy error", zap.Error(err))
		return nil, err
	}
	result, err := f.getCodeForSDKBase(&params)
	if err != nil {
		return nil, err
	}
	return &proto.FaceCodeResponse{
		Vendor:  result.Vendor,
		Code:    result.Code,
		Service: result.Service,
	}, nil
}

func (f *FaceSvc) getCodeForSDKBase(in *proto.FaceCodeForWebRequest) (*proto.FaceCodeForWebResponse, error) {
	if _, err := rpc.ChkToken(f.ctx, in.Nid, in.Token); err != nil {
		f.logger.Error("ChkToken error", zap.Error(err))
		return nil, ecode.RetErrTokenVerifyFailed
	}

	if err := f.checkFailed(in.Scene, in.Nid); err != nil {
		return nil, err
	}

	realInfo, err := rpc.GetRealInfo(f.ctx, in.Nid)
	if err != nil {
		f.logger.Error("GetRealInfo error", zap.Error(err))
		return nil, err
	}

	in.RealName = realInfo.RealName
	in.IdCard = realInfo.RealId
	return f.getCode(in)
}

func (f *FaceSvc) getCode(in *proto.FaceCodeForWebRequest) (*proto.FaceCodeForWebResponse, error) {
	if in.Extra != "" {
		extraJson := make(map[string]interface{})
		if err := sonic.Unmarshal([]byte(in.Extra), &extraJson); err != nil {
			f.logger.Error("extra sonic.Unmarshal error", xlog.Err(err))
			return nil, constant.ErrParam
		}
	}
	orderId := f.getOrderId()
	faceVerifyCode, err := f.repoFace.GetCode(f.ctx, in.Nid, orderId, in.RealName, in.IdCard, in.Extra, in.ReturnUrl, in.Scene)
	if err != nil {
		f.logger.Error("repoFace.GetCode error", zap.Error(err))
		return nil, err
	}
	f.logger.Debug("repoFace.GetCode success", zap.String("orderId", orderId), zap.String("serviceId", faceVerifyCode.Code))
	code, err := f.generateCode()
	if err != nil {
		f.logger.Error("getCode error", zap.Error(err))
		return nil, err
	}
	rdbKey := fmt.Sprintf("%s%s", constant.RdbFaceCode, code)
	rdbVal := proto.FaceCodeVal{
		Service:  faceVerifyCode.Code,
		Nid:      in.Nid,
		Scene:    in.Scene,
		Vendor:   in.Vendor,
		IdCard:   in.IdCard,
		RealName: in.RealName,
	}
	rdbValStr, err := sonic.MarshalString(&rdbVal)
	if err != nil {
		f.logger.Error("sonic.MarshalString error", zap.Error(err))
		return nil, err
	}

	if err := startup.GetRedis().SetEx(f.ctx, rdbKey, rdbValStr, time.Duration(config.Get().Dark.Aliyun.FaceCodeExpire)*time.Second).Err(); err != nil {
		f.logger.Error("SetEx error", zap.Error(err))
		return nil, err
	}

	return &proto.FaceCodeForWebResponse{
		Code:      code,
		Vendor:    in.Vendor,
		ReturnUrl: faceVerifyCode.ReturnUrl,
		Service: &proto.FaceServiceData{
			CertifyId: faceVerifyCode.Code,
		},
	}, nil

}

func (f *FaceSvc) Verify(in *proto.FaceResultRequest) (*proto.FaceResultResponse, error) {
	nid := f.faceCodeVale.Nid
	rdb := startup.GetRedis()
	ok, err := f.repoFace.Verify(f.ctx, f.faceCodeVale.Service, f.faceCodeVale.Scene, "")
	if err != nil {
		f.logger.Error("repoFace.Verify error", zap.Error(err))
		return nil, err
	} else if !ok {
		failedNum, err := f.incrVerifyFailedNum(f.ctx, f.faceCodeVale.Scene, nid)
		if err != nil {
			f.logger.Error("rdb.Incr error", zap.Error(err))
			return nil, err
		}

		if config.Get().Dark.Aliyun.FaceVerifyFailedNum > 0 && failedNum > config.Get().Dark.Aliyun.FaceVerifyFailedNum {
			f.logger.Warn("face verify failed num exceed", zap.Int("failedNum", int(failedNum)))
			return nil, constant.ErrFaceVerifyFailed
		} else if failedNum == 1 {
			rdbFailed := fmt.Sprintf("%s%s:%s", constant.RdbFaceVerifyFailed, f.faceCodeVale.Scene, nid)
			startup.GetRedis().SetEx(f.ctx, rdbFailed, 1, time.Duration(config.Get().Dark.Aliyun.FaceVerifyFailedExpire)*time.Second) //配置的过期时间
		}

		return &proto.FaceResultResponse{
			Status: 0,
		}, err
	}

	passToken, err := f.generateCode()
	if err != nil {
		f.logger.Error("getCode error", zap.Error(err))
		return nil, err
	}

	rdbVal := proto.FaceRdbTokeVal{
		ClientId: in.ClientId,
		Ip:       shared.GetClientIP(xgin.FromContext(f.ctx)),
		Nid:      nid,
		Scene:    f.faceCodeVale.Scene,
		IdCard:   f.faceCodeVale.IdCard,
		RealName: f.faceCodeVale.RealName,
		DOID:     in.DOID,
	}
	rdbValStr, err := sonic.MarshalString(&rdbVal)
	if err != nil {
		f.logger.Error("sonic.MarshalString error", zap.Error(err))
		return nil, err
	}
	_, err = rdb.Pipelined(f.ctx, func(pipe redis.Pipeliner) error {
		err1 := pipe.SetEx(f.ctx, fmt.Sprintf("%s%s", constant.RdbFacePassToke, passToken), rdbValStr, time.Duration(config.Get().Dark.Aliyun.FaceTokenExpire)*time.Second).Err()
		err2 := pipe.SetEx(f.ctx, fmt.Sprintf("%s%s:%s", constant.RdbFaceNid, f.faceCodeVale.Scene, nid), rdbValStr, time.Duration(config.Get().Dark.Aliyun.FaceTokenExpire)*time.Second).Err()
		err3 := pipe.Del(f.ctx, fmt.Sprintf("%s%s", constant.RdbFaceCode, in.Code)).Err()
		err4 := pipe.Del(f.ctx, fmt.Sprintf("%s%s:%s", constant.RdbFaceVerifyFailed, f.faceCodeVale.Scene, nid)).Err()
		if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
			f.logger.Error("rdb.Pipelined error", zap.Error(err1), zap.Error(err2), zap.Error(err3))
			return errors.New("pipelined error")
		}
		return nil
	})

	if err != nil {
		f.logger.Error("Pipelined error", zap.Error(err))
		return nil, err
	}

	return &proto.FaceResultResponse{
		Status:    1,
		PassToken: passToken,
	}, nil
}

func (f *FaceSvc) checkFailed(scene, id string) error {
	failedNum, err := f.getVerifyFailedNum(f.ctx, scene, id)
	if err != nil {
		f.logger.Error("rdb.get error", zap.Error(err))
		return err
	}
	if config.Get().Dark.Aliyun.FaceVerifyFailedNum > 0 &&
		failedNum > config.Get().Dark.Aliyun.FaceVerifyFailedNum {
		f.logger.Warn("face verify failed num exceed", zap.Int("failedNum", int(failedNum)))
		return constant.ErrFaceVerifyFailed
	}
	return nil
}

func (f *FaceSvc) getOrderId() string {
	rand.Seed(time.Now().UnixNano())       // 使用当前时间初始化随机种子
	randomNumber := rand.Intn(9000) + 1000 // 生成一个介于 1000 和 9999 之间的随机数
	return fmt.Sprintf("%s%d", time.Now().Format("20060102150405"), randomNumber)
}

func (f *FaceSvc) generateCode() (string, error) {
	code, err := nanoid.Generate("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz", 20)
	if err != nil {
		return "", err
	}
	return code, nil
}

func (f *FaceSvc) CheckStatus(in *proto.FaceStatusRequest) (*proto.FaceStatusResponse, error) {
	if (in.Nid == "" && in.Scene == "") && in.PassToken == "" {
		return nil, constant.ErrParam
	}

	rdb := startup.GetRedis()
	if in.Nid != "" {
		rdbKey := fmt.Sprintf("%s%s:%s", constant.RdbFaceNid, in.Scene, in.Nid)
		_, err := rdb.Get(f.ctx, rdbKey).Result()
		if err != nil && errors.Is(err, redis.Nil) {
			xlog.FromContext(f.ctx).Warn("nid not found", zap.String("rdbKey", rdbKey))
			return &proto.FaceStatusResponse{
				Status: 0,
			}, nil
		} else if err != nil {
			xlog.FromContext(f.ctx).Error("rdb.Get error", zap.Error(err))
			return nil, err
		}
		return &proto.FaceStatusResponse{
			Status: 1,
		}, nil
	}

	rdbKey := fmt.Sprintf("%s%s", constant.RdbFacePassToke, in.PassToken)
	val, err := rdb.Get(f.ctx, rdbKey).Result()
	if err != nil && errors.Is(err, redis.Nil) {
		xlog.FromContext(f.ctx).Warn("passToken not found", zap.String("rdbKey", rdbKey))
		return &proto.FaceStatusResponse{
			Status: 0,
		}, nil
	} else if err != nil {
		xlog.FromContext(f.ctx).Error("rdb.Get error", zap.Error(err))
		return nil, err
	}

	var rdbVal proto.FaceRdbTokeVal
	if err := sonic.UnmarshalString(val, &rdbVal); err != nil {
		xlog.FromContext(f.ctx).Error("sonic.Unmarshal error", zap.Error(err))
		return nil, err
	}
	if rdbVal.ClientId != in.ClientId {
		xlog.FromContext(f.ctx).Warn("clientId not match", zap.String("rdbKey", rdbKey), zap.Int64("clientId", rdbVal.ClientId))
		return &proto.FaceStatusResponse{
			Status: 2,
		}, nil
	}

	if in.DOID != "" && rdbVal.DOID != in.DOID {
		xlog.FromContext(f.ctx).Warn("doid not match", zap.String("rdbKey", rdbKey), zap.String("doid", rdbVal.DOID))
		return &proto.FaceStatusResponse{
			Status: 2,
		}, nil
	}

	if in.Ip != "" && rdbVal.Ip != in.Ip {
		xlog.FromContext(f.ctx).Warn("ip not match", zap.String("rdbKey", rdbKey), zap.String("ip", rdbVal.Ip))
		return &proto.FaceStatusResponse{
			Status: 2,
		}, nil
	}

	if in.IdCard != "" && rdbVal.IdCard != in.IdCard {
		xlog.FromContext(f.ctx).Warn("idCard not match", zap.String("rdbKey", rdbKey), zap.String("idCard", rdbVal.Nid))
		return &proto.FaceStatusResponse{
			Status: 2,
		}, nil
	}

	if in.RealName != "" && rdbVal.RealName != in.RealName {
		xlog.FromContext(f.ctx).Warn("realName not match", zap.String("rdbKey", rdbKey), zap.String("realName", rdbVal.Scene))
		return &proto.FaceStatusResponse{
			Status: 2,
		}, nil
	}

	return &proto.FaceStatusResponse{
		Status: 1,
	}, nil
}

func GetFaceSvcByCode(ctx context.Context, code string) (*FaceSvc, error) {
	rdb := startup.GetRedis()
	rdbKey := fmt.Sprintf("%s%s", constant.RdbFaceCode, code)
	val, err := rdb.Get(ctx, rdbKey).Result()
	if errors.Is(err, redis.Nil) {
		xlog.FromContext(ctx).Warn("code not found", zap.String("rdbKey", rdbKey))
		return nil, constant.ErrFaceCodeErr
	}
	var faceCodeVal proto.FaceCodeVal
	if err := sonic.Unmarshal([]byte(val), &faceCodeVal); err != nil {
		xlog.FromContext(ctx).Error("sonic.Unmarshal error", zap.Error(err))
		return nil, err
	}
	return NewFaceSvcByVendor(ctx, faceCodeVal.Vendor, &faceCodeVal)
}

func (f *FaceSvc) incrVerifyFailedNum(ctx context.Context, scene, nid string) (int64, error) {
	rdb := startup.GetRedis()
	rdbFailed := fmt.Sprintf("%s%s:%s", constant.RdbFaceVerifyFailed, scene, nid)
	return rdb.Incr(ctx, rdbFailed).Result()
}
func (f *FaceSvc) getVerifyFailedNum(ctx context.Context, scene, nid string) (int64, error) {
	rdb := startup.GetRedis()
	rdbFailed := fmt.Sprintf("%s%s:%s", constant.RdbFaceVerifyFailed, scene, nid)
	numStr, err := rdb.Get(ctx, rdbFailed).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	} else if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	return strconv.ParseInt(numStr, 10, 64)
}

func QrCodeGenerate(ctx context.Context, in *proto.QrCodeGenerateRequest) (string, time.Time, error) {
	//检查今日已请求次数
	rdb := startup.GetRedis()
	limitKey := fmt.Sprintf("%s%s", constant.RdbFaceQrCodeQurey, in.Nid)
	rdb.Incr(ctx, limitKey)

	now := time.Now()
	uuid := uuid.New().String()
	uuid = strings.ReplaceAll(uuid, "-", "")
	b, err := json.Marshal(in)
	if err != nil {
		return "", now, err
	}
	ttl := config.Get().FaceQrCode.TTL
	expiredAt := now.Add(ttl)
	if err := rdb.Set(ctx, fmt.Sprintf("%s%s",
		constant.RdbFaceQrCode, uuid), b, ttl).Err(); err != nil {
		return "", now, err
	}
	return uuid, expiredAt, nil
}

func QrCodeScan(ctx context.Context, qrcodeId string) (*proto.QrCodeScanResponse, error) {
	return getQrcodeMetadata(ctx, qrcodeId)
}

func FaceStatusQueryByQrCode(ctx context.Context, qrcodeId string) (uint32, error) {
	metadata, err := getQrcodeMetadata(ctx, qrcodeId)
	if err != nil {
		if err == constant.ErrFaceQrCodeNotFound {
			xmetric.MetricsFaceQrcode.Inc("status_query", "expired_fail")
			//二维码过了有效期
			return 3, nil
		} else {
			return 0, err
		}
	}
	//检查认证上限
	err = NewFaceSvc(ctx).checkFailed(metadata.Scene, metadata.Nid)
	if err != nil {
		if err == constant.ErrFaceVerifyFailed {
			xmetric.MetricsFaceQrcode.Inc("status_query", "limited_fail")
			return 2, nil
		} else {
			return 0, err
		}
	}
	//TODO 基于code 检查nid
	rdbKey := fmt.Sprintf("%s%s:%s", constant.RdbFaceNid, metadata.Scene, metadata.Nid)
	_, err = startup.GetRedis().Get(ctx, rdbKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, nil
		} else {
			return 0, err
		}
	} else {
		xmetric.MetricsFaceQrcode.Inc("status_query", "ok")
		return 1, nil
	}
}

func getQrcodeMetadata(ctx context.Context, qrcodeId string) (*proto.QrCodeScanResponse, error) {
	rdb := startup.GetRedis()
	key := fmt.Sprintf("%s%s", constant.RdbFaceQrCode, qrcodeId)
	val, err := rdb.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, constant.ErrFaceQrCodeNotFound
		} else {
			return nil, err
		}
	}
	var qrcode = new(proto.QrCodeScanResponse)
	if err := json.Unmarshal([]byte(val), qrcode); err != nil {
		return nil, err
	}
	return qrcode, nil
}

package service

import (
	"context"
	"testing"
)

func TestGenDOID(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	svcDOID, err := DOIDSvc.GenDOID()
	if err != nil {
		t.Error("server gen error:", err)
	}
	t.Log("server DOID:", svcDOID)
}

func TestGenSDKDOID(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	svcDOID := DOIDSvc.GenSDKDOID("8eRahuO1V9tK3grqZoz")
	t.Log("server DOID:", svcDOID)
}

func TestSDKDOIDCheck(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	_, err := DOIDSvc.SDKDOIDCheck("d6c15Ba46bbdVff6b1c379N664CeG118")
	if err != nil {
		t.Error("server gen error:", err)
	}
}

package service

import (
	"context"
	"testing"
)

func TestGenDOID(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	svcDOID, err := DOIDSvc.GenDOID()
	if err != nil {
		t.Error("server gen error:", err)
	}
	t.Log("server DOID:", svcDOID)
}

func TestGenSDKDOID(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	svcDOID := DOIDSvc.GenSDKDOID("8eRahuO1V9tK3grqZoz")
	t.Log("server DOID:", svcDOID)
}

func TestSDKDOIDCheck(t *testing.T) {
	DOIDSvc := NewDevice(context.Background())
	splitDeviceID24, err := DOIDSvc.SDKDOIDCheck("Ftkjp57dKNZmesnUZ6T8HARLwFk0RPOJuELb")
	if err != nil {
		t.<PERSON><PERSON>r("server gen error:", err)
	}
	t.Log("splitDeviceID24:", splitDeviceID24)
}

package service

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/constant"
	"risk/model"
	"sync/atomic"
)

var riskModel atomic.Pointer[map[string][]string] //clientid+code->modecode
var etcdRiskModel = "/sparrow/risk/conf/model"    //todo check下

func StartModel() error {
	var err error
	s := new(Model)
	err = s.ReloadRiskModel()
	if err != nil {
		xlog.Error("watcher startup failed", xlog.Err(err))
		return err
	}

	//xconf.RegisterReload(scene.ReloadRiskScence())

	return nil
}

type Model struct{}

//func (s *Scene) Reload() error {
//	ctx := context.Background()
//	scenes, err := model.GetRiskScenes(ctx)
//	if err != nil {
//		return err
//	}
//	var sencesMap = make(map[string]string)
//	for _, scene := range scenes {
//		sencesMap[strconv.FormatUint(scene.ClientId, 10)+scene.Code] = scene.ModelId
//	}
//	riskScence.Store(&sencesMap)
//	xlog.Debug("reload", xlog.Any("scenes", sencesMap))
//	return nil
//}

func (s *Model) ReloadRiskModel() error {
	ctx := context.Background()
	models, err := model.GetRiskModels(ctx)
	if err != nil {
		return err
	}
	var modelsMap = make(map[string][]string)
	for _, info := range models {
		var appIds []string
		_ = sonic.UnmarshalString(info.SetList, &appIds)
		for _, appId := range appIds {
			key := getRiskModelKey(info.ClientId, appId)
			modelsMap[key] = append(modelsMap[key], info.Code)
		}
	}
	riskModel.Store(&modelsMap)
	xlog.Debug("reload", xlog.Any("model", modelsMap))
	return nil
}

// 获取model ID
func GetRiskModel(clientId uint32, appId string) ([]string, error) {
	mtemp := *riskModel.Load()
	key := getRiskModelKey(int64(clientId), appId)
	if len(mtemp[key]) > 0 {
		return mtemp[key], nil
	} else {
		return nil, errors.New("risk models not found")
	}
}

func getRiskModelKey(clientId int64, appId string) string {
	return fmt.Sprintf("%s%d:%s", constant.RdbModel, clientId, appId)
}

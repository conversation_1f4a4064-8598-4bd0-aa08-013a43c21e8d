package service

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/proto"
	"risk/repo"
	"strconv"
)

type BindPhone struct {
	logger *xlog.Logger
}

func NewBindPhoneSvr(ctx context.Context) *BindPhone {
	return &BindPhone{
		logger: xlog.FromContext(ctx),
	}
}

func (s *BindPhone) Notice(ctx context.Context, params *proto.BindNoticeRequest) (*xtype.Empty, error) {
	hasPass := false
	if params.Code == 1 {
		hasPass = true
	}
	nid := strconv.FormatInt(params.Nid, 10)
	nonceVal, err := NewNonceMgr(ctx).GetFromRedis(params.Nonce, ctx)
	if err != nil {
		s.logger.Error(fmt.Sprintf("获取getCaptchaRedis出错,err:%v", err))
		return nil, nil
	} else if nonceVal == nil {
		s.logger.Warn(fmt.Sprintf("nonce id不存在,nonce:%v", params.Nonce))
		return nil, constant.ErrId
	} else if nonceVal.Handler != constant.HandlerForceBind {
		s.logger.Warn("nonce id不是绑定手机，nonce:%v", zap.String("nonce", params.Nonce))
		return nil, constant.ErrId
	} else if nonceVal.AppId != params.AppId && config.Get().NonceCheckAppId {
		s.logger.Warn("nonce 的app id不匹配，nonce:%v", zap.String("nonce app id", nonceVal.AppId), zap.String("request app id", params.AppId))
		return nil, constant.ErrId
	}

	sceneModelCode, err := GetRiskScene(params.ClientId, constant.RiskSDK)
	if err != nil {
		s.logger.Warn("GetRiskScene empty", zap.Error(err), zap.Any("req", params))
		return nil, nil
	}

	svc := NewStrategy(ctx)
	doid := nonceVal.DeviceId
	if params.Doid != "" {
		doid = params.Doid
	}
	svc.SetLiveTags(
		&Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nid, DOID: doid}, //记请求的账户与DOID的数，不从nonce vale中取
		constant.HandlerForceBind,
		sceneModelCode,
		hasPass)

	//上报验证成功
	if hasPass {
		svc.ReportCheckSuccess(nonceVal.SceneCode,
			constant.HandlerForceBind,
			&Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nid, DOID: params.Doid},
		)
	}

	go func() {
		event := repo.FromMapToResultEvent(ctx, nil)
		event.ClientID = strconv.Itoa(int(params.ClientId))
		event.DOID = params.Doid
		event.VOpenID = nid
		event.AppID = params.AppId
		event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
		event.CaptchaEventID = nonceVal.Id
		event.CaptchaStatus = 3 //
		event.CaptchaResult = int(params.Code)
		event.CaptchaType = uint32(constant.HandlerForceBind)
		repo.ReportBI(ctx, event)
	}()

	return nil, nil
}

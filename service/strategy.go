package service

import (
	"context"
	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	ltPB "gitlab.papegames.com/biz/protobuf/risk/livetags"
	ePB "gitlab.papegames.com/biz/protobuf/risk/risk-evaluation-engine"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"math"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/repo"
	"strconv"
	"time"
)

type Strategy struct {
	c      context.Context
	logger *xlog.Logger
}

func NewStrategy(c context.Context) *Strategy {
	return &Strategy{
		c:      c,
		logger: xlog.FromContext(c),
	}
}

// 黑白名单检查 0正常 1白名单 2黑名单
// 优先检测白名单，再检测黑名单；当黑白名单同时存在时，优先检测白名单
func (s *Strategy) BlackWhiteList(params *Params) (int32, *ePB.EvaluateResp) {
	if config.Get().BlackCheckDisable {
		return 0, nil
	}

	var request ePB.ListCheckRequest
	if err := copier.Copy(&request, params); err != nil {
		s.logger.Error("copier.Copy error:", zap.Error(err), zap.Any("params", params))
		return 0, nil
	}

	checkRes, err := repo.NewEvaluation().ListCheck(s.c, &request)
	if err != nil {
		s.logger.Error("BlackWhiteList error:", zap.Error(err))
		return 0, nil
	}

	s.logger.Info("BlackWhiteList res:", zap.Any("checkRes", checkRes), zap.Any("request", &request))
	if checkRes.Exists && (checkRes.Type == ePB.ListType_White || checkRes.Type == ePB.ListType_PeriodWhite) {
		//白名单
		return 1, nil
	} else if checkRes.Exists && (checkRes.Type == ePB.ListType_Black || checkRes.Type == ePB.ListType_PeriodBlack) {
		//黑名单
		if checkRes.Notice == "" {
			checkRes.Notice = "您的请求被拒绝"
		}
		return 2, &ePB.EvaluateResp{
			Code:   constant.HandlerReject,
			Notice: checkRes.Notice,
		}
	}
	return 0, nil
}

func (s *Strategy) Strategy(params *Params) (*ePB.EvaluateResp, *NonceIdValue, error) {
	modelCode, err := GetRiskModel(params.ClientId, params.AppId)
	//模型不存在，不计分
	if err != nil {
		s.logger.Warn("GetRiskModel empty", zap.Error(err), zap.Any("params", params))
		return nil, nil, nil
	}
	//场景code
	scene, err := GetRiskScene(params.ClientId, params.Scene)
	if err != nil {
		s.logger.Warn("GetRiskScene empty", zap.Error(err), zap.Any("params", params))
		return nil, nil, nil
	}
	var sceneInfo model.RiskScene
	err = sonic.UnmarshalString(scene, &sceneInfo)
	if err != nil {
		s.logger.Error("sonic.UnmarshalString error", zap.Error(err), zap.Any("sceneModelCode", scene))
		return nil, nil, nil
	}

	var hasInModel bool
	for _, model := range modelCode {
		if model == sceneInfo.ModelId {
			hasInModel = true
			break
		}
	}
	//场景模型不在模型库中，不计分
	if hasInModel == false {
		s.logger.Info("scene model not in model", zap.Any("params", params),
			zap.String("sceneModelCode", sceneInfo.ModelId),
			zap.Any("modelCode", modelCode))
		return nil, nil, nil
	}

	//获取计分
	var request ltPB.GetScoreRequest
	if err := copier.Copy(&request, params); err != nil {
		s.logger.Error("copier.Copy error", zap.Error(err), zap.Any("params", params))
		return nil, nil, nil
	}
	request.ModelCode = sceneInfo.ModelId
	request.Action = params.Action
	resp, err := repo.NewLiveTags().GetScore(s.c, &request)

	s.logger.Debug("get score response",
		zap.String("service", "liveTags"),
		zap.Any("request", &request),
		zap.Any("resp", resp))
	if err != nil {
		s.logger.Error("get score error",
			zap.String("service", "liveTags"),
			zap.Error(err))
		return nil, nil, nil
	}

	nonce := &NonceIdValue{
		SceneCode: params.Scene,
		ModelCode: sceneInfo.ModelId,
		Ip:        params.Ip,
		Nid:       params.Nid,
		DeviceId:  params.DOID,
		Action:    params.Action,
	}

	// 判断是否有暴力破解标签 key会是2203 2204 2206
	for _, brute := range resp.GetBruteForce() {
		if brute.Notice != nil {
			return &ePB.EvaluateResp{
				Code:   uint32(brute.Handle),
				Notice: brute.Notice.Text,
			}, nonce, nil
		} else {
			return &ePB.EvaluateResp{
				Code:   uint32(brute.Handle),
				Notice: "您的请求被拒绝",
			}, nonce, nil
		}
	}

	//计分判定,有负分的情况存在
	if resp.Score <= 0 {
		return nil, nil, nil
	}

	//ttl
	minTTL := int64(math.MaxInt64)
	if resp.Details != nil && len(resp.Details) > 0 {
		for i := range resp.Details {
			ttl := (resp.Details[i].Timestamp + resp.Details[i].Ttl) - time.Now().Unix()
			if ttl > 0 && ttl < minTTL {
				minTTL = ttl
			} else if ttl == 0 {
				minTTL = 1
			}
		}
	}
	if minTTL == int64(math.MaxInt64) {
		minTTL = 3600 * 24 * 7
	}

	//处置动作
	var ePBRequest ePB.EvaluateRequest
	if err := copier.Copy(&ePBRequest, params); err != nil {
		s.logger.Error("copier.Copy error:", zap.Error(err), zap.Any("params", params))
		return nil, nil, err
	}

	ePBRequest.SceneId = params.Scene
	ePBRequest.Score = resp.Score
	eResp, err := repo.NewEvaluation().DisposeEvaluate(s.c, &ePBRequest)
	s.logger.Debug("get evaluation response",
		zap.String("service", "evaluation"),
		zap.Any("res", eResp),
		zap.Any("request", &ePBRequest),
	)

	if err != nil {
		s.logger.Error("get evaluation response error",
			zap.String("service", "evaluation"),
			zap.Error(err))
		return nil, nil, err
	}

	nonce.Phone = eResp.Phone
	nonce.Email = eResp.Email
	nonce.Handler = eResp.Code
	nonce.Id = eResp.Nonce
	nonce.TTL = minTTL

	return eResp, nonce, nil
}

// SetLiveTags 设置特殊标签
func (s *Strategy) SetLiveTags(params *Params, handler uint32, modelCode string, hasPass bool) error {
	s.logger.Info("set liveTags", zap.Any("params", params))
	var request ltPB.ReportRequest
	if err := copier.Copy(&request, params); err != nil {
		s.logger.Error("copier.Copy error:", zap.Error(err), zap.Any("params", params))
		return err
	}

	request.Doid = params.DOID
	if hasPass {

	}

	if !hasPass {
		//未通过验证，暴力破解计数
		request.Code = ltPB.TagCode_BruteForce.String()
	} else if modelCode == "abnormal_charge_model" {
		//退款场景的通过验证
		request.Code = ltPB.TagCode_AbnormalChargePass.String()
	} else {
		//通过验证
		request.Code = ltPB.TagCode_VerifyPass.String()
	}

	request.VerifyType = handler
	request.ModelCode = modelCode
	request.RoleId = params.RoleId

	err := repo.NewLiveTags().Report(s.c, &request)
	if err != nil {
		s.logger.Error("set liveTags error", zap.Any("request", &request), zap.Error(err))
		return err
	}
	s.logger.Info("set liveTags success", zap.Any("request", &request))
	return nil
}

func (s *Strategy) ReportCheckSuccess(sceneCode string, code int, params *Params) error {
	var request ePB.ReportCheckSuccessRequest
	if err := copier.Copy(&request, params); err != nil {
		return err
	}

	request.SceneId = sceneCode
	request.Code = uint32(code)
	err := repo.NewEvaluation().ReportCheckSuccess(s.c, &request)
	if err != nil {
		s.logger.Error("ReportCheckSuccess error", zap.Any("request", &request), zap.Error(err))
	}
	s.logger.Info("ReportCheckSuccess success", zap.Any("request", &request))
	return nil
}

func (s *Strategy) StrategyForGames(params *Params) (int64, error) {
	//场景code
	modelCode, err := GetRiskScene(params.ClientId, constant.RiskEnterGame)
	if err != nil {
		return 0, err
	}

	//获取计分
	var request ltPB.ScoreRequest
	if err := copier.Copy(&request, params); err != nil {
		s.logger.Error("copier.Copy error:", zap.Error(err), zap.Any("params", params))
		return 0, nil
	}
	request.ModelCode = modelCode
	resp, err := repo.NewLiveTags().ScoreQuery(s.c, &request)
	s.logger.Info("get score response",
		zap.String("service", "liveTags"),
		zap.Any("resp", resp),
		zap.Any("request", &request))
	if err != nil {
		s.logger.Error("get score response error",
			zap.String("service", "liveTags"),
			zap.Error(err))
		return 0, nil
	}

	//计分判定
	total := resp.Score
	if total <= 0 {
		return 0, nil
	}

	//处置动作
	var ePBRequest ePB.EvaluateRequest
	if err := copier.Copy(&ePBRequest, params); err != nil {
		s.logger.Error("copier.Copy error:",
			zap.Any("params", params),
			zap.Error(err))
		return 0, nil
	}

	ePBRequest.SceneId = constant.RiskEnterGame
	ePBRequest.Score = resp.Score
	eResp, err := repo.NewEvaluation().DisposeEvaluate(s.c, &ePBRequest)
	s.logger.Info("get evaluation response",
		zap.String("service", "evaluation"),
		zap.Any("request", &ePBRequest),
		zap.Any("res", eResp),
	)
	if err != nil {
		s.logger.Error("get evaluation error:",
			zap.String("service", "evaluation"),
			zap.Any("request", &ePBRequest),
			zap.Error(err))
		return 0, nil
	} else if eResp.Code > 0 {
		return total, nil
	}
	return 0, nil
}

func (s *Strategy) VerifyPass(ctx context.Context, handler uint32, nonceVal *NonceIdValue, params *Params) error {
	var isForceAppId bool
	for _, forceAppId := range config.Get().Machine.ForceAppId {
		if params.AppId == forceAppId {
			isForceAppId = true
		}
	}

	if nonceVal.Id != "" && (nonceVal.Action == constant.ActionCheckDOID || isForceAppId) {
		if _, err := NewNonceMgr(ctx).SetToRedis(nonceVal, nonceVal.Id, ctx); err != nil {
			return err
		}
	} else {
		//清除特殊标签计时器
		_ = s.SetLiveTags(
			params, //清请求的账户与DOID的数，不从nonce vale中取
			handler,
			nonceVal.ModelCode,
			true)

		//上报验证成功
		_ = s.ReportCheckSuccess(nonceVal.SceneCode,
			int(handler),
			params,
		)

		go func() {
			event := repo.FromMapToResultEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = nonceVal.Nid
			event.RoleId = nonceVal.RoleId
			event.AppID = params.AppId
			event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
			event.CaptchaEventID = nonceVal.Id
			event.CaptchaStatus = 3
			event.CaptchaResult = 1
			event.CaptchaType = handler
			repo.ReportBI(ctx, event)
		}()

	}

	if nonceVal.Id != "" {
		_ = NewNonceMgr(ctx).DelFromRedis(nonceVal.Id, ctx)
	}

	return nil
}

func (s *Strategy) VerifyNoPass(ctx context.Context, nonceVal *NonceIdValue, params Params) error {
	_ = s.SetLiveTags(
		&params, //记请求的账户与DOID的数，不从nonce vale中取
		constant.HandlerSms,
		nonceVal.ModelCode,
		false)

	event := repo.FromMapToResultEvent(ctx, nil)
	event.ClientID = strconv.Itoa(int(params.ClientId))
	event.DOID = params.DOID
	event.VOpenID = nonceVal.Nid
	event.RoleId = nonceVal.RoleId
	event.AppID = params.AppId
	event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
	event.CaptchaEventID = nonceVal.Id
	event.CaptchaStatus = 2
	event.CaptchaResult = 2
	event.CaptchaType = uint32(constant.HandlerSms)
	event.FailedReason = "invalid phone-code"
	repo.ReportBI(ctx, event)
	return nil
}

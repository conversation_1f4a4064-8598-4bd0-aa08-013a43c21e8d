package dark

import (
	"context"
	"fmt"
	"risk/config"
	"risk/model"
	"risk/repo/dark"
	"risk/startup"
	"risk/utils"
	"risk/xmetric"
	"strconv"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

type Dark struct {
	db     *xgorm.DB
	logger *xlog.Logger
	ctx    context.Context
}

func NewDark(ctx context.Context) *Dark {
	return &Dark{
		db:     startup.GetMySql().WithContext(ctx),
		logger: xlog.FromContext(ctx),
		ctx:    ctx,
	}
}

// CheckIP 检查IP
func (d *Dark) CheckIP(appId string, clientId uint32, ip string, repo dark.Dark) bool {
	ctx, span := utils.GetSpan(d.ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appId))
	defer span.End()
	var darkModel model.RiskProviderDarkList
	ok, err := repo.CheckIP(ip, d.logger, ctx)
	if err != nil {
		//DB异常，不做处理继续剩下的逻辑
		xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), ip, "error")
		d.logger.Error(fmt.Sprintf("checkIP error: %v,repo:%#v", err, repo))
	} else if !ok {
		xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), ip, "no-pass")
		d.logger.Info(fmt.Sprintf("checkIP 在黑产设备库中 ip: %v,repo:%#v", ip, repo))
		darkModel.Add(appId, clientId, ip, 2, repo.GetName(), 4, config.Get().Dark.Black, d.db.WithContext(ctx))
		return false
	}
	xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), ip, "pass")
	d.logger.Info(fmt.Sprintf("checkIP 没在黑产设备库中 ip: %v,repo:%#v", ip, repo))
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	return true
}

// Check设备
func (d *Dark) CheckDevice(appId string, clientId uint32, deviceId, fid string, repo dark.Dark) bool {
	ctx, span := utils.GetSpan(d.ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(clientId)), attribute.String("app_id", appId))
	defer span.End()

	var darkModel model.RiskProviderDarkList

	ok, err := repo.CheckDevice(deviceId, fid, d.logger, ctx)
	if err != nil {
		//DB异常，不做处理继续剩下的逻辑
		xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), fid, "error")
		d.logger.Error(fmt.Sprintf("checkDevice error: %v,repo:%#v", err, repo))
	} else if !ok {
		xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), fid, "no-pass")
		d.logger.Warn(fmt.Sprintf("checkDevice 在黑产设备库中 device id: %v,repo:%#v", deviceId, repo))
		darkModel.Add(appId, clientId, fid, 3, repo.GetName(), 4, config.Get().Dark.Black, d.db.WithContext(ctx)) //厂商设备号加入黑产
		return false
	}
	xmetric.MetricsDark.Inc(appId, strconv.Itoa(int(clientId)), strconv.Itoa(int(repo.GetName())), fid, "pass")
	d.logger.Info(fmt.Sprintf("checkDevice 没在黑产设备库中 device id: %v,repo:%#v", deviceId, repo))

	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	return true

}

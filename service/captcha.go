package service

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/pkg/shared"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/proto"
	"risk/repo"
	"risk/repo/captcha"
	"risk/service/conf"
	"strconv"

	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type Captcha struct {
	logger *xlog.Logger
}

func NewCaptcha(ctx context.Context) *Captcha {
	return &Captcha{
		logger: xlog.FromContext(ctx),
	}
}

func (c *Captcha) GeetestCheck(params *proto.GCaptchaRequest, ctx context.Context) (*proto.GCaptchaResponse, error) {
	repoCaptcha := captcha.NewGeetest(ctx)
	geeConfIns := conf.GetConfGeetestConfSvrIns()

	nonceMgr := NewNonceMgr(ctx)
	strategySrc := NewStrategy(ctx)
	nonceVal, err := nonceMgr.GetFromRedis(params.Nonce, ctx)
	if err != nil {
		c.logger.Error(fmt.Sprintf("获取getCaptchaRedis出错,err:%v", err))
		return nil, nil
	} else if nonceVal == nil {
		c.logger.Warn(fmt.Sprintf("nonce id不存在,nonce:%v", params.Nonce))
		return nil, constant.ErrId
	} else if nonceVal.Handler != constant.HandlerCaptcha {
		c.logger.Warn("nonce id不是验证码，nonce:%v", zap.String("nonce", params.Nonce))
		return nil, constant.ErrId
	} else if nonceVal.AppId != params.AppId && config.Get().NonceCheckAppId {
		c.logger.Warn("nonce 的app id不匹配，nonce:%v", zap.String("nonce app id", nonceVal.AppId), zap.String("request app id", params.AppId))
		return nil, constant.ErrId
	}

	res, err := geeConfIns.GetByCaptchaId(ctx, params.CaptchaId)
	if err != nil {
		return nil, constant.ErrNoPass
	}

	doid := nonceVal.DeviceId
	if params.DOID != "" {
		doid = params.DOID
	}

	hasPass, err := repoCaptcha.Validate(params.CaptchaId, params.Number, params.Captcha, params.Token, params.Time, res.CaptchaKey, params.AppId)
	if err != nil {
		c.logger.Error(fmt.Sprintf("请求repo.Validate出错，err:%v", err))
		return nil, nil
	} else if !hasPass {
		c.logger.Warn("验证不通过！", zap.Any("params", params))
		go func() {
			strategySrc.SetLiveTags(
				&Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: doid}, //记请求的账户与DOID的数，不从nonce vale中取
				constant.HandlerCaptcha,
				nonceVal.SceneCode,
				false)

			event := repo.FromMapToResultEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = nonceVal.Nid
			event.RoleId = nonceVal.RoleId
			event.AppID = params.AppId
			event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
			event.CaptchaEventID = nonceVal.Id
			event.CaptchaStatus = 2
			event.CaptchaResult = 2
			event.CaptchaType = uint32(constant.HandlerCaptcha)
			event.FailedReason = "invalid pass-token"
			repo.ReportBI(ctx, event)

		}()
		return nil, constant.ErrNoPass
	}

	var isForceAppId bool
	for _, appId := range config.Get().Machine.ForceAppId {
		if appId == params.AppId {
			isForceAppId = true
		}
	}

	if nonceVal.Action == constant.ActionCheckDOID || isForceAppId {
		//特殊的风控判断，设备号检测与固定的appid
		if _, err = nonceMgr.SetToRedis(nonceVal, params.Nonce, ctx); err != nil {
			return nil, err
		}
	} else {
		//清除特殊标签计时器
		strategySrc.SetLiveTags(
			&Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: doid, RoleId: nonceVal.RoleId}, //清请求的账户与DOID的数，不从nonce vale中取
			constant.HandlerCaptcha,
			nonceVal.ModelCode,
			true)

		//上报验证成功
		strategySrc.ReportCheckSuccess(nonceVal.SceneCode,
			constant.HandlerCaptcha,
			&Params{ClientId: params.ClientId, AppId: params.AppId, Nid: nonceVal.Nid, DOID: params.DOID, RoleId: nonceVal.RoleId},
		)

		go func() {
			event := repo.FromMapToResultEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = nonceVal.Nid
			event.RoleId = nonceVal.RoleId
			event.AppID = params.AppId
			event.ClientIP = shared.GetClientIP(xgin.FromContext(ctx))
			event.CaptchaEventID = nonceVal.Id
			event.CaptchaStatus = 3
			event.CaptchaResult = 1
			event.CaptchaType = uint32(constant.HandlerCaptcha)
			repo.ReportBI(ctx, event)
		}()

	}

	_ = nonceMgr.DelFromRedis(params.Nonce, ctx)
	return nil, nil
}

func (c *Captcha) GeetestGet(ctx context.Context) (*proto.GCaptchaResponse, error) {
	return &proto.GCaptchaResponse{}, nil
}

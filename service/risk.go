package service

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/service/conf"
	"risk/service/inspection"
	"risk/startup"
	"risk/utils"
	"sync"
)

type Params struct {
	Nid       string `json:"nid,omitempty" copier:"Nid"`
	Ip        string `json:"ip,omitempty" copier:"Ip"`
	Action    string `json:"action,omitempty" copier:"Action"`
	AppId     string `json:"app_id,omitempty" copier:"AppId"`
	ClientId  uint32 `json:"client_id,omitempty" copier:"ClientId"`
	Device    string `json:"device,omitempty" copier:"Device"`
	DOID      string `json:"DOID,omitempty" copier:"DOID"`
	HasEncode uint32 `json:"has_encode,omitempty" copier:"HasEncode"`
	Type      uint32
	Lang      string `json:"lang,omitempty" copier:"Lang"`
	Score     int64  `json:"score,omitempty" copier:"Score"`
	RoleId    string `json:"roleId" copier:"RoleId"`
	Scene     string `json:"scene" copier:"Scene"`
}

type UserDetail struct {
	Nid   string `json:"nid"`
	Phone string `json:"phone"`
	Email string `json:"email"`
}

type Risk struct {
	logger *xlog.Logger
	lock   sync.Mutex
}

func NewRisk(ctx context.Context) *Risk {
	return &Risk{
		logger: xlog.FromContext(ctx),
	}
}

func (r *Risk) Check(ctx context.Context, params *Params) (*proto.HandlerRequest, error) {
	if params.AppId == "" {
		params.AppId = "all"
	}
	ctx, span := utils.GetSpan(ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(params.ClientId)), attribute.String("app_id", params.AppId))
	defer span.End()

	var err error
	DOID := params.DOID

	//风控检测是否打开
	co, err := conf.NewConfigClient(ctx).OneByClientIdAndType(r.getAppIds(params.AppId), params.ClientId, constant.ChkAll)
	if errors.Is(err, xgorm.ErrRecordNotFound) {
		r.logger.Error(fmt.Sprintf("configModel读取出错,err:%v", err))
		return r.getPassResponse(DOID, span)
	} else if co.IsOpen == constant.Disable {
		r.logger.Warn(fmt.Sprintf("app_id:%s client_id:%d 全局检测已关闭is_open=0", params.AppId, params.ClientId))
		return r.getPassResponse(DOID, span)
	}

	listConf, err := conf.NewConfigClient(ctx).ListByClientId(r.getAppIds(params.AppId), params.ClientId) //开启的策略列表

	listConfVal, _ := sonic.Marshal(listConf)
	r.logger.Info("策略列表",
		zap.String("data list:", string(listConfVal)),
		zap.String("app_id", params.AppId),
		zap.Uint32("client_id", params.ClientId),
		zap.String("device", params.Device),
		zap.String("ip", params.Ip),
		zap.String("nid", params.Nid),
	)

	//IP白名单检查，在白名单内直接放行
	if _, ok := listConf[constant.ChkWhiteListIp]; ok {
		if hasPass, _ := inspection.InspectionList[constant.ChkWhiteListIp].
			Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, params.Ip, params.Action); hasPass {
			return r.getPassResponse(DOID, span)
		}
	}
	//NID白名单检查，在白名单内直接放行
	if _, ok := listConf[constant.ChkWhiteListAccount]; ok {
		if hasPass, _ := inspection.InspectionList[constant.ChkWhiteListAccount].
			Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, params.Ip, params.Action); hasPass {
			return r.getPassResponse(DOID, span)
		}
	}

	//设备白名单检查，在白名单内直接放行
	if _, ok := listConf[constant.ChkWhiteListDevice]; ok && DOID != "" {
		if hasPass, _ := inspection.InspectionList[constant.ChkWhiteListDevice].Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, DOID, params.Action); hasPass {
			return r.getPassResponse(DOID, span)
		}
	}

	//DOID检测
	if DOID != "" && config.Get().DOIDCheck {
		tactics, _ := conf.NewConfigClient(ctx).OneByClientIdAndType(r.getAppIds(params.AppId), params.ClientId, constant.ChkAll)
		_, errs := NewDevice(ctx).SDKDOIDCheck(DOID)
		if len(DOID) == 32 && errs != nil {
			r.logger.Info("SDK本地DOID检测失败", zap.String("DOID", DOID))
			return r.getNoPassResponse(params, DOID, *tactics, ctx, span)
		} else {
			deviceModel := model.RiskDevice{}
			exist, err := deviceModel.ExistByDOID(DOID, startup.GetMySql().WithContext(ctx))
			if err != nil {
				r.logger.Error("DOID检测 DB出错:", zap.Error(err), zap.String("DOID", DOID))
				return r.getPassResponse(DOID, span)
			} else if !exist {
				r.logger.Warn("DOID检测不存在", zap.String("DOID", DOID))
				return r.getNoPassResponse(params, DOID, *tactics, ctx, span)
			}
		}
	}

	//是否通过二次验证检测
	ok, err := r.CheckRedisCaptcha(params.Ip, params.Nid, DOID, ctx)
	if err != nil {
		r.logger.Error(fmt.Sprintf("redis error:%v", err))
		return r.getPassResponse(DOID, span)
	} else if ok {
		return r.getPassResponse(DOID, span)
	}

	//黑名单检查，在黑名单内直接拒绝
	for k, v := range map[uint16]string{
		constant.ChkBlackListAccount: params.Nid,
		constant.ChkBlackListIp:      params.Ip,
		constant.ChkBlackListDevice:  DOID} {
		if tactics, ok := listConf[k]; ok {
			if hasPass, _ := inspection.InspectionList[k].
				Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, v, params.Action); !hasPass {
				r.logger.Info("黑名单拦截",
					zap.Uint16("regulation", k),
					zap.Bool("intercept", true),
					zap.String("app_id", params.AppId),
					zap.Uint32("client_id", params.ClientId),
					zap.String("device", params.Device),
					zap.String("ip", params.Ip),
					zap.String("nid", params.Nid),
				)
				return r.getNoPassResponse(params, DOID, tactics, ctx, span)
			}
		}
	}

	//频率检测
	for k, v := range map[uint16]string{
		constant.ChkActionAccountFrequency: params.Nid,
		constant.ChkActionIPFrequency:      params.Ip,
		constant.ChkActionDeviceFrequency:  DOID} {
		if tactics, ok := listConf[k]; ok {
			if hasPass, _ := inspection.InspectionList[k].
				Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, v, params.Action); !hasPass {
				r.logger.Info("频率检测拦截",
					zap.Uint16("regulation", k),
					zap.Bool("intercept", true),
					zap.String("app_id", params.AppId),
					zap.Uint32("client_id", params.ClientId),
					zap.String("device", params.Device),
					zap.String("ip", params.Ip),
					zap.String("nid", params.Nid),
				)
				return r.getNoPassResponse(params, DOID, tactics, ctx, span)
			}
		}
	}

	//间隔时间检测
	for k, v := range map[uint16]string{
		constant.ChkIntervalAccount: params.Nid,
		constant.ChkIntervalIp:      params.Ip,
		constant.ChkIntervalDevice:  DOID} {
		if tactics, ok := listConf[k]; ok {
			if hasPass, _ := inspection.InspectionList[k].
				Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, v, params.Action); !hasPass {
				r.logger.Info("间隔时间检测拦截",
					zap.Uint16("regulation", k),
					zap.Bool("intercept", true),
					zap.String("app_id", params.AppId),
					zap.Uint32("client_id", params.ClientId),
					zap.String("device", params.Device),
					zap.String("ip", params.Ip),
					zap.String("nid", params.Nid),
				)
				return r.getNoPassResponse(params, DOID, tactics, ctx, span)
			}
		}
	}

	//黑产检查
	for k, v := range map[uint16]string{
		constant.ChkDarkIP:     params.Ip,
		constant.ChkDarkDevice: DOID} {
		if tactics, ok := listConf[k]; ok {
			if hasPass, _ := inspection.InspectionList[k].
				Inspection(ctx, r.getAppIds(params.AppId), params.ClientId, params.Type, v, params.Action); !hasPass {
				r.logger.Info("黑产检测拦截",
					zap.Uint16("regulation", k),
					zap.Bool("intercept", true),
					zap.String("app_id", params.AppId),
					zap.Uint32("client_id", params.ClientId),
					zap.String("device", params.Device),
					zap.String("ip", params.Ip),
					zap.String("nid", params.Nid),
				)
				return r.getNoPassResponse(params, DOID, tactics, ctx, span)
			}
		}
	}
	return r.getPassResponse(DOID, span)
}

type CometResponseRisk struct {
	Risk *proto.HandlerRequest `json:"risk"`
}

type CometResponse struct {
	Data      *CometResponseRisk `json:"data"`
	RequestId string             `json:"request_id"`
	Code      uint32             `json:"code"`
	Info      string             `json:"info"`
}

func (r *Risk) GetNoPassResponseForComet(ctx context.Context, ip, nid, fid string, handle uint32) (*CometResponse, error) {
	span := trace.SpanFromContext(ctx)
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	nonce, err := NewNonceMgr(ctx).Generate("comet", ip, nid, fid, constant.RiskSDK, handle, ctx)
	if err != nil {
		r.logger.Error("generate error", zap.Error(err))
		return nil, nil
	}
	nonceResp, err := NewHandler(ctx).Handler(handle, "", nonce)
	if err != nil {
		if v, ok := err.(ecode.Code); ok {
			return &CometResponse{
				Data: &CometResponseRisk{
					Risk: nonceResp,
				},
				Code: uint32(v.Value()),
				Info: err.Error(),
			}, nil
		} else {
			xlog.FromContext(ctx).Error("handler error", zap.Error(err))
			return nil, err
		}
	}

	return nil, ecode.Unavailable
}

func (r *Risk) CheckRedisCaptcha(ip, nid, device string, ctx context.Context) (bool, error) {
	rdb := startup.GetRedis()
	nidKey := fmt.Sprintf("%s%s", constant.RdbApprovedNid, nid)
	rs := rdb.Get(ctx, nidKey)
	if rs.Err() != nil && !errors.Is(rs.Err(), redis.Nil) {
		r.logger.Error("checkRedisCaptcha error", zap.Error(rs.Err()))
		return false, rs.Err()
	} else if rs.Val() == "1" {
		r.logger.Info("checkRedisCaptcha 通过二次认证，无需进行其他处理", zap.String("key", nidKey))
		return true, nil
	}
	r.logger.Info("checkRedisCaptcha 没有通过二次认证", zap.String("key", nidKey))
	return false, nil
	//pipeliner := rdb.Pipeline()
	//result := make([]*xredis.IntCmd, 0)
	//
	//result = append(result, pipeliner.Exists(ctx, nidKey))
	//
	////ipKey := fmt.Sprintf("%s%s", constant.RdbApprovedIp, ip)
	////result = append(result, pipeliner.Exists(ctx, ipKey))
	////
	////deviceKey := fmt.Sprintf("%s%s", constant.RdbApprovedDevice, device)
	////result = append(result, pipeliner.Exists(ctx, deviceKey))
	//
	//_, err := pipeliner.Exec(ctx)
	//if err != nil {
	//	r.logger.Error("checkRedisCaptcha error", zap.Error(err))
	//	return false, err
	//}
	//
	//for _, c := range result {
	//	if c.Val() > 0 {
	//		r.logger.Info("checkRedisCaptcha  通过二次认证，无需进行其他处理！", zap.String("key", c.String()))
	//		return true, nil
	//	} else {
	//		r.logger.Info("checkRedisCaptcha  未使用二次认证", zap.String("key", c.String()))
	//	}
	//}
	//return false, nil
}

func (r *Risk) getPassResponse(fid string, span trace.Span) (*proto.HandlerRequest, error) {
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	return &proto.HandlerRequest{}, nil
}

func (r *Risk) getNoPassResponse(params *Params, fid string, co model.RiskConfigApp, ctx context.Context, span trace.Span) (*proto.HandlerRequest, error) {
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	r.logger.Info("no pass response,config:",
		zap.String("ip", params.Ip),
		zap.String("nid", params.Nid),
		zap.String("fid", fid),
		zap.Any("config", co))

	nonce, err := NewNonceMgr(ctx).Generate(params.Action, params.Ip, params.Nid, fid, "", uint32(co.Handle), ctx)
	if err != nil {
		r.logger.Error("generate error", zap.Error(err))
		return &proto.HandlerRequest{}, nil
	}
	return NewHandler(ctx).Handler(uint32(co.Handle), "", nonce)
}

func (r *Risk) getAppIds(appId string) []string {
	if appId == "all" {
		return []string{
			"all",
		}
	} else {
		return []string{
			appId, "all",
		}
	}
}

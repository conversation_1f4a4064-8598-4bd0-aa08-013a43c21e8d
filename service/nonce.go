package service

import (
	"context"
	"errors"
	"fmt"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/rpc"
	"risk/startup"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type NonceIdValue struct {
	Id        string `json:"id"`
	Ip        string `json:"ip"`
	Nid       string `json:"nid"`
	DeviceId  string `json:"device_id"`
	Phone     string `json:"phone,omitempty"`
	Email     string `json:"email,omitempty"`
	Action    string `json:"action,omitempty"`
	SceneCode string `json:"scene_code"`
	ModelCode string `json:"model_code"`
	Handler   uint32 `json:"handler"`
	AppId     string `json:"app_id"`
	TTL       int64  `json:"ttl"`
	RoleId string
}

type NonceMgr struct {
	logger *xlog.Logger
	lock   sync.Mutex
}

func NewNonceMgr(ctx context.Context) *NonceMgr {
	return &NonceMgr{
		logger: xlog.FromContext(ctx),
	}
}

func (c *NonceMgr) GetFromRedis(nonceId string, ctx context.Context) (*NonceIdValue, error) {
	rdb := startup.GetRedis()
	redisKey := fmt.Sprintf("%s%s", constant.RdbCaptchaNonceId, nonceId)
	nonceVal, err := rdb.Get(ctx, redisKey).Result()
	if err != nil && !errors.Is(err, xredis.Nil) {
		return nil, err
	} else if errors.Is(err, xredis.Nil) {
		c.logger.Error(fmt.Sprintf("redis key:%v 不存在！", redisKey))
		return nil, nil
	}
	var nonceValue NonceIdValue
	err = sonic.UnmarshalString(nonceVal, &nonceValue)
	if err != nil {
		c.logger.Error(fmt.Sprintf("json Unmarshal data:%v err:%v", nonceVal, err))
	}
	c.logger.Info("nonce", zap.String("redis keys", redisKey), zap.Any("nonceValue", nonceValue))
	return &nonceValue, err
}

// 设置临时白名单
func (c *NonceMgr) SetToRedis(nonceVal *NonceIdValue, nonceId string, ctx context.Context) (bool, error) {
	rdb := startup.GetRedis()
	t := time.Duration(config.Get().Machine.VerifyValExpire) * time.Second
	nidKey := fmt.Sprintf("%s%s", constant.RdbApprovedNid, nonceVal.Nid)
	err := rdb.SetEx(ctx, nidKey, 1, t).Err()
	if err != nil {
		c.logger.Error("setToRedis error", zap.Error(err))
		return false, err
	}
	return true, nil
	//pipeliner := rdb.Pipeline()
	//
	//pipeliner.SetEx(ctx, nidKey, 1, t)
	//
	////ip的维度太大
	//ipKey := fmt.Sprintf("%s%s", constant.RdbApprovedIp, nonceVal.Ip)
	//pipeliner.SetEx(ctx, ipKey, 1, t)
	//
	//if nonceVal.DeviceId != "" {
	//	deviceKey := fmt.Sprintf("%s%s", constant.RdbApprovedDevice, nonceVal.DeviceId)
	//	pipeliner.SetEx(ctx, deviceKey, 1, t)
	//}
	//
	//nonceIdKey := fmt.Sprintf("%s%s", constant.RdbCaptchaNonceId, nonceId)
	//pipeliner.Del(ctx, nonceIdKey)
	//
	//_, err := pipeliner.Exec(ctx)
	//if err != nil {
	//	return false, nil
	//}
	//
	//return true, nil

}

func (c *NonceMgr) DelFromRedis(nonceId string, ctx context.Context) error {
	rdb := startup.GetRedis()
	nonceIdKey := fmt.Sprintf("%s%s", constant.RdbCaptchaNonceId, nonceId)
	return rdb.Del(ctx, nonceIdKey).Err()
}

func (r *NonceMgr) GenerateStrategy(nonceValue *NonceIdValue, ctx context.Context) error {
	r.lock.Lock()
	defer r.lock.Unlock()
	rdb := startup.GetRedis()
	marshalString, _ := sonic.MarshalString(nonceValue)
	redisKey := fmt.Sprintf("%s%s", constant.RdbCaptchaNonceId, nonceValue.Id)
	expire := config.Get().Machine.NonceExpire
	err := rdb.SetEx(ctx, redisKey, marshalString, time.Duration(expire)*time.Second).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r *NonceMgr) Generate(action, ip, nid, deviceId, sceneCode string, handle uint32, ctx context.Context) (*NonceIdValue, error) {
	r.lock.Lock()
	defer r.lock.Unlock()
	rdb := startup.GetRedis()
	id, err := gonanoid.New(12)
	if err != nil {
		return nil, err
	}
	var userInfo = &rpc.GetSimpleProfileResp{}

	// todo: 丢到配置里
	if handle == constant.HandlerSms || handle == constant.HandlerEmail {
		userInfo, err = rpc.GetUserSimpleProfile(ctx, nid, "")
	}

	nonceValue := NonceIdValue{
		Id:        id,
		Ip:        ip,
		Nid:       nid,
		DeviceId:  deviceId,
		Email:     userInfo.Email,
		Phone:     userInfo.Phone,
		Action:    action,
		SceneCode: sceneCode,
		Handler:   handle,
	}

	marshalString, _ := sonic.MarshalString(nonceValue)
	redisKey := fmt.Sprintf("%s%s", constant.RdbCaptchaNonceId, id)
	expire := config.Get().Machine.NonceExpire
	err = rdb.SetEx(ctx, redisKey, marshalString, time.Duration(expire)*time.Second).Err()
	if err != nil {
		return nil, err
	}
	return &nonceValue, nil
}

package service

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/startup"
)

type Interval struct {
	db     *xgorm.DB
	rdb    *xredis.Client
	logger *xlog.Logger
	ctx    context.Context
}

func NewInterval(ctx context.Context) *Interval {
	return &Interval{
		db:     startup.GetMySql().WithContext(ctx),
		logger: xlog.FromContext(ctx),
		ctx:    ctx,
		rdb:    startup.GetRedis(),
	}
}

//
//func (i *Interval) Check(params *Params, appIds []string, fid string) (bool, uint64) {
//	//11 请求间隔
//	if fid == constant.PassDevice {
//		i.logger.Info("设备间隔检测，webview跳过检测")
//		return true, 0
//	}
//	conf, err := conf2.NewConfigClient(i.ctx).OneByClientIdAndType(appIds, params.ClientId, constant.ChkIntervalCheck)
//	if err != nil && !errors.Is(err, xgorm.ErrRecordNotFound) {
//		i.logger.Error(fmt.Sprintf("configModel读取DB出错,err:%v", err))
//		return true, 0
//	} else if errors.Is(err, xgorm.ErrRecordNotFound) || conf.IsOpen == 0 {
//		i.logger.Warn("type:11 请求间隔没有配置或is_open=0")
//		return true, 0
//	}
//
//	//开启了检测
//	key := ""
//	switch params.Type {
//	case constant.RiskTypePay:
//		key = constant.RdbIntervalPay
//	case constant.RiskTypeAccount:
//		key = constant.RdbIntervalAccount
//	case constant.RiskTypeApp:
//		key = constant.RdbIntervalApp
//	}
//
//	var interval model.FrequencyArray
//	err = sonic.UnmarshalString(conf.Frequency, &interval)
//	if err != nil {
//		i.logger.Error(fmt.Sprintf("UnmarshalString error: %v data:%#v", err, conf.Frequency))
//		return true, 0
//	}
//	//账号
//	rdbNidKey := fmt.Sprintf("%sc%d:u-%s:a-%d", key, params.ClientId, params.Nid, params.Action)
//	nidCmder := i.rdb.SetNX(i.ctx, rdbNidKey, 1, time.Duration(interval.Time)*time.Second)
//	if nidCmder.Err() != nil {
//		xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "nid", params.Action, params.Nid, "error")
//		i.logger.Error(fmt.Sprintf("redis SetNX 保持，rdbNidKey:%v,err:%v", rdbNidKey, nidCmder.Err()))
//		return true, 0
//	} else if !nidCmder.Val() {
//		xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "nid", params.Action, params.Nid, "no-pass")
//		i.logger.Info(fmt.Sprintf("nid:%v的请求间隔时间太短被限制了，间隔时间要求:%v，rdbNidKey:%v", params.Nid, interval.Time, rdbNidKey))
//		return false, conf.ID
//	}
//
//	//设备号
//	rdbFidKey := fmt.Sprintf("%sc%d:f-%s:a-%d", key, params.ClientId, fid, params.Action)
//	fidCmder := i.rdb.SetNX(i.ctx, rdbFidKey, 1, time.Duration(interval.Time)*time.Second)
//	if fidCmder.Err() != nil {
//		xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "fid", params.Action, fid, "error")
//		i.logger.Error(fmt.Sprintf("redis SetNX 保持，rdbNidKey:%v,err:%v", rdbNidKey, nidCmder.Err()))
//		return true, 0
//	} else if !fidCmder.Val() {
//		xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "fid", params.Action, fid, "no-pass")
//		i.logger.Info(fmt.Sprintf("fid:%v的请求间隔时间太短被限制了，间隔时间要求:%v，rdbNidKey:%v", fid, interval.Time, rdbNidKey))
//		return false, conf.ID
//	}
//	xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "nid", params.Action, params.Nid, "pass")
//	xmetric.MetricsInterval.Inc(params.AppId, strconv.Itoa(int(params.ClientId)), "fid", params.Action, fid, "pass")
//	return true, 0
//}

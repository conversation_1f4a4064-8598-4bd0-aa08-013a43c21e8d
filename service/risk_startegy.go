package service

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"risk/config"
	"risk/constant"
	"risk/model"
	"risk/proto"
	"risk/repo"
	"risk/startup"
	"risk/utils"
	"strconv"
	"time"
)

type RiskStrategy struct {
	ctx    context.Context
	logger *xlog.Logger
}

func NewRiskStrategy(ctx context.Context) *RiskStrategy {
	return &RiskStrategy{
		ctx:    ctx,
		logger: xlog.FromContext(ctx),
	}
}

func (r *RiskStrategy) ChkStrategy(params *Params) (*proto.HandlerRequest, error) {
	ctx, span := utils.GetSpan(r.ctx)
	span.SetAttributes(attribute.Int64("client_id", int64(params.ClientId)), attribute.String("app_id", params.AppId))
	defer span.End()

	if config.Get().CloseRisk.Login {
		r.logger.Info("登陆风控已关闭", zap.Any("config.Get().CloseRisk.login", config.Get().CloseRisk.Login))
		//登陆风控配置开关
		return nil, nil
	}

	for _, clientId := range config.Get().CloseRisk.LoginGames {
		if clientId == params.ClientId {
			r.logger.Info("登陆风控已关闭", zap.Any("config.Get().CloseRisk.loginGames", config.Get().CloseRisk.LoginGames))
			return nil, nil
		}
	}

	params.Lang = utils.Lang(params.Lang)

	nonceVal := &NonceIdValue{
		SceneCode: params.Scene,
		Ip:        params.Ip,
		Nid:       params.Nid,
		DeviceId:  params.DOID,
		Action:    params.Action,
		AppId:     params.AppId,
	}
	r.logger.Debug("config.Get().Machine.ForceAppId", zap.Any("Machine", config.Get().Machine))
	// 强制走极验
	for _, appId := range config.Get().Machine.ForceAppId {
		if appId == params.AppId {
			//是否通过二次验证检测
			r.logger.Debug("强制走极验", zap.Any("params", params))
			if ok, _ := NewRisk(ctx).CheckRedisCaptcha(params.Ip, params.Nid, params.DOID, ctx); ok {
				return nil, nil
			}
			return r.getNoPassResponse(nonceVal, constant.HandlerCaptcha, ctx, span)
		}
	}

	if params.Device != "" {
		//老版本的deviceId不走风控
		r.logger.Info("老版本的deviceId不走风控", zap.Any("params", params))
		return nil, nil
	}

	s := NewStrategy(ctx)
	isWhiteBlack, res := s.BlackWhiteList(params)
	if isWhiteBlack == 1 {
		r.logger.Info("在白名单内", zap.Any("params", params))
		go func() {
			event, _ := repo.FromMapToScoreEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = params.Nid
			event.AppID = params.AppId
			event.ClientIP = params.Ip
			event.RiskScoreTrigger = 1
			event.RiskScoreScene = params.Scene
			event.IsWhitelist = 1
			event.IsBlacklist = 2
			repo.ReportBI(ctx, event)
		}()
		return nil, nil
	} else if isWhiteBlack == 2 {
		r.logger.Info("在黑名单内", zap.Any("params", params))
		go func() {
			event, _ := repo.FromMapToScoreEvent(ctx, nil)
			event.ClientID = strconv.Itoa(int(params.ClientId))
			event.DOID = params.DOID
			event.VOpenID = params.Nid
			event.AppID = params.AppId
			event.ClientIP = params.Ip
			event.RiskScoreTrigger = 1
			event.RiskScoreScene = params.Scene
			event.IsBlacklist = 1
			event.IsWhitelist = 2

			repo.ReportBI(ctx, event)
		}()

		return r.getNoPassResponse(nonceVal, res.Code, ctx, span, res.Notice)
	}

	//DOID检测
	//for _, clientId := range config.Get().DOIDCheckGames {
	//	if clientId == params.ClientId && !r.chkDOID(ctx, params.Ip, params.Nid, params.DOID) {
	//		nonceVal.Action = constant.ActionCheckDOID
	//		return r.getNoPassResponse(nonceVal, constant.HandlerCaptcha, ctx, span)
	//	}
	//}

	_ = r.DOIDHasException(ctx, params)
	sRes, nonceValue, _ := s.Strategy(params)

	if sRes != nil && sRes.Disposed && sRes.Code == 0 {
		r.serviceDisposed(params)
	}

	if sRes != nil && sRes.Code > 0 {
		r.logger.Info("处置下发", zap.Any("params", params))
		return r.getNoPassResponse(nonceValue, sRes.Code, ctx, span, sRes.Notice)
	}
	return nil, nil
}

func (r *RiskStrategy) chkDOID(ctx context.Context, ip, nid, DOID string) bool {
	//DOID检测

	//是否通过二次验证检测
	if ok, _ := NewRisk(ctx).CheckRedisCaptcha(ip, nid, DOID, ctx); ok {
		return true
	}

	if DOID == "" {
		r.logger.Named("DOID_check_failed").Info("DOID检测失败，DOID为空")
		return false
	} else if len(DOID) == 32 {
		if _, errs := NewDevice(ctx).SDKDOIDCheck(DOID); errs != nil {
			r.logger.Named("DOID_check_failed").Info("SDK本地DOID检测失败", zap.String("DOID", DOID))
			return false
		} else {
			return true
		}
	} else {
		rdbKey := fmt.Sprintf("%s%s", constant.RdbDOID, DOID)
		if exist := startup.GetRedis().Exists(ctx, rdbKey).Val(); exist == 1 {
			return true
		}
		deviceModel := model.RiskDeviceProvider{}
		exist, err := deviceModel.ExistByDOID(DOID, startup.GetMySql().WithContext(ctx))
		if err != nil {
			r.logger.Named("DOID_check_failed").Error("DOID检测 DB出错:", zap.Error(err), zap.String("DOID", DOID))
			return true
		} else if !exist {
			r.logger.Named("DOID_check_failed").Warn("DOID检测不存在", zap.String("DOID", DOID))
			return false
		} else {
			return true
		}
	}
}

func (r *RiskStrategy) DOIDHasException(ctx context.Context, params *Params) error {
	if params.DOID == "" {
		r.logger.Named("DOID_check_failed").Info("DOID检测失败，DOID为空")
		params.DOID = constant.DOIDNone
		return nil
	}

	modelTags := new(model.RiskDeviceAbnormalTag)
	DOID := params.DOID
	if len(DOID) == 32 {
		if _, errs := NewDevice(ctx).SDKDOIDCheck(DOID); errs != nil {
			r.logger.Named("DOID_check_failed").Info("SDK本地DOID检测失败", zap.String("DOID", DOID))
			_ = modelTags.CreateOrUpdateTagByDOID(DOID, constant.DOIDAbnormal, startup.GetMySql().WithContext(ctx))
		}
		return nil
	}

	rdbKey := fmt.Sprintf("%s%s", constant.RdbDOID, DOID)
	if exist := startup.GetRedis().Exists(ctx, rdbKey).Val(); exist == 1 {
		return nil
	}
	deviceModel := model.RiskDeviceProvider{}
	exist, err := deviceModel.ExistByDOID(DOID, startup.GetMySql().WithContext(ctx))
	if err != nil {
		r.logger.Named("DOID_check_failed").Error("DOID检测 DB出错:", zap.Error(err), zap.String("DOID", DOID))
	} else if !exist {
		r.logger.Named("DOID_check_failed").Warn("DOID检测不存在", zap.String("DOID", DOID))
		_ = modelTags.CreateOrUpdateTagByDOID(DOID, constant.DOIDAbnormal, startup.GetMySql().WithContext(ctx))
	}
	return nil
}

func (r *RiskStrategy) getNoPassResponse(nonceValue *NonceIdValue, handle uint32, ctx context.Context, span trace.Span, notes ...string) (*proto.HandlerRequest, error) {
	span.SetStatus(codes.Ok, ecode.NotFound.Error())
	r.logger.Info("no pass response,config:",
		zap.Any("nonceValue", nonceValue),
		zap.Uint32("handle", handle))

	if nonceValue.Handler == 0 {
		nonceValue.Handler = handle
	}

	note := ""
	if len(notes) > 0 && notes[0] != "" {
		note = notes[0]
	}

	if nonceValue.Id == "" {
		id, err := gonanoid.New(12)
		if err != nil {
			r.logger.Error("generate id error", zap.Error(err))
			return &proto.HandlerRequest{}, nil
		}
		nonceValue.Id = id
	}

	res, err := NewHandler(ctx).Handler(handle, note, nonceValue)
	if handle != constant.HandlerReject {
		err := NewNonceMgr(ctx).GenerateStrategy(nonceValue, ctx)
		if err != nil {
			r.logger.Error("generate error", zap.Error(err))
			return &proto.HandlerRequest{}, nil
		}
	}

	return res, err
}

func (r *RiskStrategy) ChkByNidForGame(params *Params) (*proto.NIDCheckResponse, error) {
	if config.Get().CloseRisk.EnterGame {
		//进游戏的风控开关
		return nil, nil
	}
	//异常兼容测试
	if config.Get().Debug {
		for _, cid := range config.Get().CloseRisk.EnterGameConf.ClientId {
			if cid == params.ClientId {
				if config.Get().CloseRisk.EnterGameConf.HttpCode > 0 {
					xgin.FromContext(r.ctx).String(config.Get().CloseRisk.EnterGameConf.HttpCode, fmt.Sprintf("httpCode:%d", config.Get().CloseRisk.EnterGameConf.HttpCode))
					return nil, ecode.ResetContent
				}

				if config.Get().CloseRisk.EnterGameConf.Timeout > 0 {
					time.Sleep(time.Duration(config.Get().CloseRisk.EnterGameConf.Timeout) * time.Second)
					return nil, nil
				}
			}
		}
	}

	//是否通过二次验证检测
	ctx := r.ctx
	ok, _ := NewRisk(ctx).CheckRedisCaptcha(params.Ip, params.Nid, params.DOID, ctx)
	if ok {
		return nil, nil
	}

	s := NewStrategy(ctx)
	isWhiteBlack, _ := s.BlackWhiteList(params)
	if isWhiteBlack == 2 {
		r.logger.Info("在黑名单内", zap.Any("params", params))
		return &proto.NIDCheckResponse{
			RiskScore: 10,
		}, nil
	} else if isWhiteBlack == 1 {
		r.logger.Info("在白名单内", zap.Any("params", params))
		return nil, nil
	}
	var resScore int64
	score, _ := s.StrategyForGames(params)

	//只返回给游戏0与1
	if score > 0 {
		resScore = 1
	} else {
		resScore = 0
	}
	return &proto.NIDCheckResponse{
		RiskScore: resScore,
	}, nil

}

func (r *RiskStrategy) serviceDisposed(params *Params) {
	//场景code
	scene, err := GetRiskScene(params.ClientId, params.Scene)
	if err != nil {
		r.logger.Warn("GetRiskScene empty", zap.Error(err), zap.Any("params", params))
		return
	}

	var sceneInfo model.RiskScene
	err = sonic.UnmarshalString(scene, &sceneInfo)
	if err != nil {
		r.logger.Error("sonic.UnmarshalString error", zap.Error(err), zap.Any("sceneModelCode", scene))
		return
	}

	nonceVal := &NonceIdValue{
		Id:        "",
		SceneCode: params.Scene,
		ModelCode: sceneInfo.ModelId,
		Nid:       params.Nid,
		Action:    params.Action,
		RoleId:    params.RoleId,
	}
	err = NewStrategy(r.ctx).VerifyPass(r.ctx, 1, nonceVal, params)
	if err != nil {
		r.logger.Error("VerifyPass error", zap.Error(err), zap.Any("params", params))
	}
	return
}

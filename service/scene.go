package service

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/constant"
	"risk/model"
	"sync/atomic"
)

var riskScene atomic.Pointer[map[string]string] //clientid+code->modecode
var etcdRiskScene = "/sparrow/risk/conf/scene"  //todo check下

func StartScene() error {
	var err error
	s := new(Scene)
	err = s.ReloadRiskScene()
	if err != nil {
		xlog.Error("watcher startup failed", xlog.Err(err))
		return err
	}

	//xconf.RegisterReload(scene.ReloadRiskScence())
	return nil
}

type Scene struct{}

//func (s *Scene) Reload() error {
//	ctx := context.Background()
//	scenes, err := model.GetRiskScenes(ctx)
//	if err != nil {
//		return err
//	}
//	var sencesMap = make(map[string]string)
//	for _, scene := range scenes {
//		sencesMap[strconv.FormatUint(scene.ClientId, 10)+scene.Code] = scene.ModelId
//	}
//	riskScence.Store(&sencesMap)
//	xlog.Debug("reload", xlog.Any("scenes", sencesMap))
//	return nil
//}

func (s *Scene) ReloadRiskScene() error {
	ctx := context.Background()
	scenes, err := model.GetRiskScenes(ctx)
	if err != nil {
		return err
	}
	var scenesMap = make(map[string]string)
	for _, scene := range scenes {
		key := getRiskSceneKey(scene.ClientId, scene.Code)
		sceneStr, _ := sonic.MarshalString(scene)
		scenesMap[key] = sceneStr
	}
	riskScene.Store(&scenesMap)
	xlog.Debug("reload", xlog.Any("scenes", scenesMap))
	return nil
}

// 获取场景ID
func GetRiskScene(clientId uint32, sceneCode string) (string, error) {
	mtemp := *riskScene.Load()
	key := getRiskSceneKey(uint64(clientId), sceneCode)
	if mtemp[key] != "" {
		return mtemp[key], nil
	} else {
		return "", errors.New("risk scene not found")
	}
}

func getRiskSceneKey(clientId uint64, sceneCode string) string {
	return fmt.Sprintf("%s%d:%s", constant.RdbScene, clientId, sceneCode)
}

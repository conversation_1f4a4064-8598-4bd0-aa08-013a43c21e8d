package service

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/util/gvalid"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/config"
	"risk/constant"
	"risk/proto"
	"strings"
)

type Handler struct {
	c context.Context
}

func NewHandler(c context.Context) *Handler {
	return &Handler{
		c: c,
	}

}

func (h *Handler) Handler(handle uint32, note string, nonce *NonceIdValue) (*proto.HandlerRequest, error) {
	xlog.FromContext(h.c).Info("处置类型", xlog.Any("handle", handle))
	var res *proto.HandlerRequest
	var err error
	switch handle {
	case constant.HandlerCaptcha:
		res, err = h.getCaptcha(nonce)
	case constant.HandlerSms:
		//处置层已处理
		if nonce.Phone == "" || gvalid.New().Rules("phone").Data(nonce.Phone).Run(context.Background()) != nil {
			xlog.FromContext(h.c).Info("手机号未绑定,走降级！", xlog.String("phone", nonce.Phone))
			handle = constant.HandlerCaptcha
			nonce.Handler = handle
			res, err = h.getCaptcha(nonce)
		} else {
			res, err = h.getPhone(nonce), constant.ErrRiskSms
		}
	case constant.HandlerEmail:
		//处置层已处理
		if nonce.Email == "" || gvalid.New().Rules("email").Data(nonce.Phone).Run(context.Background()) != nil {
			xlog.FromContext(h.c).Info("邮箱验证未绑定,走降级！", xlog.String("email", nonce.Email))
			handle = constant.HandlerCaptcha
			nonce.Handler = handle
			res, err = h.getCaptcha(nonce)
		} else {
			res, err = h.getEmail(nonce), constant.ErrRiskEmail
		}
	case constant.HandlerReject:
		res, err = h.getReject(nonce, note)
	case constant.HandlerLogout:
		res, err = h.getLogout(nonce, note)
	case constant.HandlerForceBind:
		if nonce.Phone == "" || gvalid.New().Rules("phone").Data(nonce.Phone).Run(context.Background()) != nil {
			xlog.FromContext(h.c).Info("手机号未绑定,强制绑定！", xlog.String("phone", nonce.Phone))
			res, err = h.getForceBind(nonce), constant.ErrRiskForceBind
		} else {
			handle = constant.HandlerSms
			nonce.Handler = handle
			xlog.FromContext(h.c).Info("手机号绑定", xlog.String("phone", nonce.Phone))
			res, err = h.getPhone(nonce), constant.ErrRiskSms
		}
	default:
		res, err = h.getCaptcha(nonce)
	}

	//处置层已处理
	//go func() {
	//	event := repo.FromMapToResultEvent(ctx, nil)
	//	event.ClientID = strconv.Itoa(int(params.ClientId))
	//	event.DOID = params.DOID
	//	event.VOpenID = params.Nid
	//	event.AppID = params.AppId
	//	event.ClientIP = params.Ip
	//	event.CaptchaEventID = nonce.Id
	//	event.CaptchaStatus = 1
	//	event.CaptchaType = uint32(handle)
	//	repo.ReportBI(ctx, event)
	//}()
	res.Ttl = nonce.TTL
	return res, err
}

func (h *Handler) getPhone(nonce *NonceIdValue) *proto.HandlerRequest {
	// 手机号取前三位和后四位
	return &proto.HandlerRequest{
		Nonce:     nonce.Id,
		Phone:     []string{nonce.Phone[0:3], nonce.Phone[7:]},
		SalePhone: nil,
	}
}

func (h *Handler) getEmail(nonce *NonceIdValue) *proto.HandlerRequest {
	emailArr := strings.Split("@", nonce.Email)
	return &proto.HandlerRequest{
		Nonce:     nonce.Id,
		Email:     fmt.Sprintf("%s****%s", emailArr[0][0:1], emailArr[1]), //nonce.Email,
		SaleEmail: "",
	}
}

func (h *Handler) getCaptcha(nonce *NonceIdValue) (*proto.HandlerRequest, error) {
	// 极验开关
	if config.Get().Machine.Geetest.Disable {
		return nil, nil
	}

	return &proto.HandlerRequest{
		Provider: "1", // 1:极验
		Nonce:    nonce.Id,
	}, constant.ErrRiskCaptcha
}

func (h *Handler) getReject(nonce *NonceIdValue, note string) (*proto.HandlerRequest, error) {
	if note == "" {
		note = "您的请求被拒绝"
	}
	return &proto.HandlerRequest{
		Nonce: "2206",
	}, ecode.Error(constant.ErrRiskReject, note)
}

func (h *Handler) getLogout(nonce *NonceIdValue, note string) (*proto.HandlerRequest, error) {
	if note == "" {
		note = "登陆退出"
	}
	return &proto.HandlerRequest{
		Nonce: "2210",
	}, ecode.Error(constant.ErrRiskLogout, note)
}

func (h *Handler) getForceBind(nonce *NonceIdValue) *proto.HandlerRequest {
	return &proto.HandlerRequest{
		Nonce: nonce.Id,
	}
}

package server

import (
	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/pkg/interceptors/encrypt"
	"gitlab.papegames.com/fringe/pkg/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/log"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/origin"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/trace"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"risk/config"
	"risk/control"
	"risk/proto"
	"risk/repo"
	"risk/startup"
	"risk/utils"
	"risk/xmetric"
)

var (
	ginServerInner   = xgin.NewServer()
	ginServerOutward = xgin.NewServer()
)

func GetOutward() *xgin.Server { return ginServerOutward }

func GetInner() *xgin.Server { return ginServerInner }

func Startup() error {
	hooks.Append(log.New())
	hooks.Append(origin.New())
	hooks.Append(trace.New())

	// 请求签名验签
	if err := startup.InitVerify(); err != nil {
		panic(err)
	}
	//clientConfig初始化
	if err := startup.ClientConfigStartup(); err != nil {
		panic(err)
	}

	if err := repo.NewEvaluationConn(); err != nil {
		panic(err)
	}
	if err := repo.NewLiveTagsConn(); err != nil {
		panic(err)
	}

	if err := encrypt.Startup(); err != nil {
		panic(err)
	}

	// Outward Service
	xgin.RegisterInterceptor(
		ginServerOutward.GetGinEngine(),
		startup.AppVerify(config.Get().SignVerifyIgnores, config.Get().Debug),
		encrypt.Decrypt(startup.GetVerify()),
		encrypt.Encrypt(startup.GetVerify()),
	)

	InitServer(ginServerOutward, config.Get().Host, "Outward Service")
	ginServerOutward.GetGinEngine().Use(hooks.GetHandlerFunc()...)
	proto.RegisterRiskSDKServiceGinServer(ginServerOutward, control.GetSDKControl())

	// Inner Service
	InitServer(ginServerInner, config.Get().InnerHost, "Inner Service")
	ginServerInner.GetGinEngine().Use(hooks.GetHandlerFunc()...)
	proto.RegisterRiskServerServiceGinServer(ginServerInner, control.GetServerControl())

	return nil
}

func InitServer(s *xgin.Server, host string, serviceName string) {
	eng := s.GetGinEngine()
	eng.Use(hooks.GetHandlerFunc()...)
	eng.RedirectFixedPath = true

	xgin.RegisterInterceptor(
		eng,
		SetCommonParams(),
	)
	opts := []server.Option{
		server.ServiceName(serviceName),
		server.ServiceHost(host),
		// 先把服务注册都关掉
		server.ServiceRegistrar(false),
		server.HTTPBasicHook(xmetric.NewXMetric()),
	}
	xlog.Info("server:"+serviceName+" 启动", xlog.String("host", host))
	s.Init(opts...)
}

func SetCommonParams() xgin.Interceptor {
	signer := shared.NewSigner(startup.GetClientConfig())
	return func(next xgin.Handler) xgin.Handler {
		return func(c *gin.Context) (interface{}, ecode.Code) {
			clientid := c.Request.FormValue("clientid")
			client_id := c.Request.FormValue("client_id")
			if clientid == "" && client_id != "" {
				c.Request.Form.Set("clientid", client_id)
				clientid = client_id
			}
			utils.WithClientid(c, clientid)
			utils.WithLang(c, c.Request.FormValue("lang"))
			utils.WithSigner(c, signer)
			xlog.FromContext(c).Info("http request header", xlog.Any("header", c.Request.Header))
			return next(c)
		}
	}
}
